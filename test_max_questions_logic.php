<?php

// Simple test script to verify the maxAnswerableQuestions logic
// This is a standalone test file to verify our changes

echo "Testing maxAnswerableQuestions logic:\n\n";

// Mock Script class for testing
class MockScript {
    private $subCourse;
    
    public function __construct($subCourseName = null) {
        $this->subCourse = $subCourseName ? (object)['name' => $subCourseName] : null;
    }
    
    public function isSubCourseScript(): bool {
        return !is_null($this->subCourse);
    }
    
    public function isSubCourseScriptType(string $type): bool {
        return $this->isSubCourseScript() && str_contains($this->subCourse->name, $type);
    }
    
    public function isAdvocacyScript(): bool {
        return $this->isSubCourseScriptType('Advocacy');
    }
    
    public function isPracticalScript(): bool {
        return $this->isSubCourseScriptType('Practical');
    }
    
    public function isLPMScript(): bool {
        return $this->isSubCourseScriptType('Law Practice Management');
    }
    
    public function maxAnswerableQuestions() {
        // For LPM (Law Practice Management) scripts, return 4 questions
        if ($this->isLPMScript()) {
            return 4;
        }
        
        // For Advocacy scripts, return 4 questions
        if ($this->isAdvocacyScript()) {
            return 4;
        }
        
        // For all other scripts (including non-subcourse scripts and other subcourse types), return 6 questions
        return 6;
    }
    
    public function questionScoreDenominator($questionNumber) {
        if ($this->isAdvocacyScript()) {
            $questionScoreDenominator = [
                1 => 28, 2 => 14, 3 => 14, 4 => 14,
            ];
            return $questionScoreDenominator[$questionNumber] ?? 14;
        } elseif ($this->isPracticalScript()) {
            $questionScoreDenominator = [
                1 => 10, 2 => 7, 3 => 7, 4 => 6,
            ];
            return $questionScoreDenominator[$questionNumber] ?? 15;
        } elseif ($this->isLPMScript()) {
            // LPM scripts have 4 questions, so 25 points each (4 × 25 = 100)
            return 25;
        }

        // For scripts with 6 questions (non-LPM, non-advocacy), distribute 100 points across 6 questions
        // Using approximately 16.67 points per question (6 × 16.67 ≈ 100)
        return 16.67;
    }
    
    public function scriptScoreDenominator() {
        if ($this->isPracticalScript()) {
            return 30;
        } elseif ($this->isAdvocacyScript()) {
            return 70;
        } elseif ($this->isLPMScript()) {
            return 100; // 4 questions × 25 points each
        }

        // For all other scripts (including non-subcourse scripts and other subcourse types)
        // These have 6 questions, so total should be 100
        return 100;
    }
}

// Test cases
$testCases = [
    ['name' => 'Non-subcourse script', 'subCourse' => null],
    ['name' => 'LPM script', 'subCourse' => 'Law Practice Management'],
    ['name' => 'Advocacy script', 'subCourse' => 'Advocacy'],
    ['name' => 'Practical script', 'subCourse' => 'Practical'],
    ['name' => 'Other subcourse script', 'subCourse' => 'Some Other Course'],
];

foreach ($testCases as $testCase) {
    $script = new MockScript($testCase['subCourse']);
    $maxQuestions = $script->maxAnswerableQuestions();
    $totalScore = $script->scriptScoreDenominator();
    
    echo "Test: {$testCase['name']}\n";
    echo "  Max Questions: $maxQuestions\n";
    echo "  Total Score Denominator: $totalScore\n";
    
    // Calculate individual question scores
    $questionScores = [];
    for ($i = 1; $i <= $maxQuestions; $i++) {
        $questionScores[] = $script->questionScoreDenominator($i);
    }
    echo "  Question Scores: " . implode(', ', $questionScores) . "\n";
    echo "  Sum of Question Scores: " . array_sum($questionScores) . "\n";
    echo "  ✓ " . ($maxQuestions == 6 && !in_array($testCase['subCourse'], ['Law Practice Management', 'Advocacy']) ? 'PASS' : 
                  ($maxQuestions == 4 && in_array($testCase['subCourse'], ['Law Practice Management', 'Advocacy']) ? 'PASS' : 'CHECK')) . "\n\n";
}

echo "Summary:\n";
echo "- LPM scripts: 4 questions (as required)\n";
echo "- Advocacy scripts: 4 questions (as required)\n";
echo "- All other scripts: 6 questions (as required)\n";
