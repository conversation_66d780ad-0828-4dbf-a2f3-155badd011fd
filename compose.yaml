services:
  php:
    container_name: php
    build:
      context: .
      target: php
      dockerfile: Dockerfile.dev
    working_dir: /var/www/html
    ports:
      - 9000:9000
    depends_on:
      - iec_db
    volumes:
      - ./:/var/www/html
    networks:
      - iec_network

  queue:
    container_name: queue
    build:
      context: .
      target: php
      dockerfile: Dockerfile.dev
    working_dir: /var/www/html
    command: php artisan queue:work
    depends_on:
      - iec_db
      - php
    volumes:
      - ./:/var/www/html
    networks:
      - iec_network

  nginx:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    container_name: iec_nginx
    restart: always
    ports:
      - 7000:80
    volumes:
      - .:/var/www/html
    depends_on:
      - php
    networks:
      - iec_network
  iec_db_admin:
    image: phpmyadmin
    environment:
      - PMA_HOST=iec_db
    ports:
      - 8080:80
    depends_on:
      - iec_db
    networks:
      - iec_network
  iec_db:
    container_name: iec_db
    image: mariadb:latest
    restart: always
    environment:
      - MARIADB_ROOT_PASSWORD=${DB_PASSWORD}
      - MARIADB_PASSWORD=${DB_PASSWORD}
      - MARIADB_DATABASE=${DB_DATABASE}
      - MARIADB_USER=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DATABASE=${DB_DATABASE}
      - DB_USERNAME=${DB_USERNAME}
    ports:
      - 3307:3306
    volumes:
      - mariadb_volume:/var/lib/mysql
      # - ./my.cnf:/usr/local/etc/my.cnf
      # - pgdata:/var/lib/postgresql/data
      # - ./pgdata/init.sql:/docker-entrypoint-initdb.d/create_tables.sql
    networks:
      - iec_network

volumes:
  mariadb_volume:


networks:
  iec_network:
