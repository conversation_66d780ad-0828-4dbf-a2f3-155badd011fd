<?php

namespace App\Listeners;

use App\Notifications\ApplicationNotification;
use Illuminate\Notifications\Events\NotificationSent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class ApplicationNotificationListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(NotificationSent $event): void
    {
        if ($event->notification instanceof ApplicationNotification) {
            $event->notifiable->update(["notified" => 1]);
        }
    }
}
