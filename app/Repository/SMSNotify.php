<?php

namespace App\Repository;

use Illuminate\Support\Facades\Http;

class SMSNotify
{
    private $to;
    private $message;

    public function __construct($to, $message)
    {
        $this->to = $to;
        $this->message = $message;
    }

    public function send()
    {
        if (!env('SEND_SMS') || !str_starts_with(trim($this->to, "+"), "233")) return true;
        return $this->{env('SMS_CONNECTION')}();
    }

    public function arkesel(): bool
    {
        $response = Http::get(env('ARKESEL_BASE_URL'), [
            'action' => 'send-sms',
            'api_key' => env('ARKESEL_API_KEY'),
            'to' => $this->to,
            'from' => env('ARKESEL_FROM'),
            'sms' => $this->message
        ]);

        $body = json_decode($response->body(), true);
        return $body['code'] === 'ok';
    }

    public function hubtel(): bool
    {
        $response = Http::get(env('HUBTEL_BASE_URL'), [
            'clientsecret' => env('HUBTEL_CLIENT_SECRET'),
            'clientid' => env('HUBTEL_CLIENT_ID'),
            'to' => $this->to,
            'from' => env('HUBTEL_FROM'),
            'content' => $this->message
        ]);
        $body = json_decode($response->body(), true);
        return $body['status'] === 0;
    }
}
