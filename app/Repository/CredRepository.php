<?php

namespace App\Repository;

use App\Mail\VoucherMail;
use App\Models\Cred;
use App\Notifications\VoucherPaymentNotification;
use Illuminate\Support\Facades\Mail;

class CredRepository
{
    public function createPin(Cred $cred)
    {
        $pin = $this->generatePin();
        $cred->serial = bin2hex(random_bytes(6));
        $cred->pin = bcrypt($pin);
        $cred->save();

        $this->notify($cred, $pin);
    }

    public function resetPin(Cred $cred)
    {
        $pin = $this->generatePin();
        $cred->pin = bcrypt($pin);
        $cred->save();

        $this->notify($cred, $pin);
    }

    public function notify($cred, $pin)
    {
        // Send email using Mail class (AppServiceProvider handles override logic)
        Mail::to($cred->email)->send(new VoucherMail($cred, $pin));

        // Send SMS
        $smsNotify = new SMSNotify($cred->phone, "Thank you for applying to write the exam. Serial Number: $cred->serial and your Pin: $pin. Use these to complete your application. - IEC");
        return $smsNotify->send();
    }

    private function generatePin()
    {
        return mt_rand(10000000, 99999999);
    }
}
