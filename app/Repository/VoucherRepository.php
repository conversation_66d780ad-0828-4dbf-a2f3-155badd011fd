<?php

namespace App\Repository;

use App\Mail\VoucherCodeMail;
use App\Models\EVoucher;
use App\Models\Cred;
use App\Repository\CredRepository;
use App\Repository\SMSNotify;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class VoucherRepository
{
    /**
     * Process voucher purchase payment
     */
    public function processVoucherPurchase(EVoucher $voucher, string $paymentReference): bool
    {
        try {
            $voucher->update([
                'status' => 'paid',
                'payment_reference' => $paymentReference,
                'paid_at' => now()
            ]);

            $this->sendVoucherCode($voucher);
            
            Log::info('Voucher purchase processed successfully', [
                'voucher_id' => $voucher->id,
                'voucher_code' => $voucher->voucher_code,
                'payment_reference' => $paymentReference
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to process voucher purchase', [
                'voucher_id' => $voucher->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Process application fee payment with voucher
     */
    public function processExamFeePayment(EVoucher $voucher, string $paymentReference): ?Cred
    {
        try {
            if (!$voucher->isRedeemable()) {
                Log::error('Attempted to redeem invalid voucher', [
                    'voucher_id' => $voucher->id,
                    'status' => $voucher->status
                ]);
                return null;
            }

            // Create credential record
            $cred = Cred::create([
                'email' => $voucher->email,
                'phone' => $voucher->phone,
                'first_name' => $voucher->first_name,
                'last_name' => $voucher->last_name,
                'application_id' => $voucher->application_id,
                'status' => 1
            ]);

            // Create order for the application fee
            $application = \App\Models\Application::find($voucher->application_id);
            $examFee = $application ? $application->amount : 550;

            $order = $cred->createOrder();
            $order->update([
                'charges' => $examFee * charges(),
                'reference' => $paymentReference,
                'unit_price' => $examFee,
                'status' => 1
            ]);

            // Mark voucher as redeemed
            $voucher->redeem($cred->id);

            // Generate and send PIN
            $credRepo = new CredRepository();
            $credRepo->createPin($cred);

            Log::info('Exam fee payment processed with voucher', [
                'voucher_id' => $voucher->id,
                'cred_id' => $cred->id,
                'payment_reference' => $paymentReference
            ]);

            return $cred;
        } catch (\Exception $e) {
            Log::error('Failed to process application fee payment with voucher', [
                'voucher_id' => $voucher->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Send voucher code via email and SMS
     */
    private function sendVoucherCode(EVoucher $voucher): void
    {
        try {
            // Send email using existing Mail class (AppServiceProvider handles override logic)
            Log::info('Attempting to send voucher email', [
                'voucher_id' => $voucher->id,
                'email' => $voucher->email,
                'voucher_code' => $voucher->voucher_code
            ]);

            Mail::to($voucher->email)->send(new VoucherCodeMail($voucher));

            Log::info('Voucher email sent successfully', [
                'voucher_id' => $voucher->id,
                'email' => $voucher->email
            ]);

            // Send SMS
            $application = \App\Models\Application::find($voucher->application_id);
            $examFee = $application ? $application->amount : 550;

            $smsNotify = new SMSNotify(
                $voucher->phone,
                "Your E-voucher code is: {$voucher->voucher_code}. Use this code to pay the application fee of GHS {$examFee}. Valid for current application period only. - IEC"
            );
            $smsNotify->send();

            Log::info('Voucher code sent successfully', [
                'voucher_id' => $voucher->id,
                'email' => $voucher->email,
                'phone' => $voucher->phone
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send voucher code', [
                'voucher_id' => $voucher->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Validate voucher code
     */
    public function validateVoucherCode(string $voucherCode): ?EVoucher
    {
        $voucher = EVoucher::where('voucher_code', strtoupper(trim($voucherCode)))
            ->where('status', 'paid')
            ->first();

        if ($voucher && $voucher->isRedeemable()) {
            return $voucher;
        }

        return null;
    }

    /**
     * Get voucher statistics
     */
    public function getVoucherStats(): array
    {
        return [
            'total_vouchers' => EVoucher::count(),
            'paid_vouchers' => EVoucher::where('status', 'paid')->count(),
            'redeemed_vouchers' => EVoucher::where('status', 'redeemed')->count(),
            'pending_vouchers' => EVoucher::where('status', 'pending')->count(),
            'total_voucher_revenue' => EVoucher::where('status', '!=', 'pending')->sum('amount'),
        ];
    }
}
