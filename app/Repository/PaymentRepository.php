<?php

namespace App\Repository;

use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PaymentRepository
{
    private $baseUrl = 'https://api.paystack.co/transaction/';

    public function getPaymentLink($user, $amount, $metadata)
    {
        Log::info('Generating payment link', [
            'user_id' => $user->id,
            'amount' => $amount,
            'metadata' => $metadata
        ]);

        try {
            $payload = [
                'email' => $user->email,
                'amount' => $amount * 100,
                'callback_url' => route('payment.callback'),
                // "split_code" => "ACCT_bw1x1q02pbgavfn",
                "subaccount" => env("PAYSTACK_SUBACCOUNT_CODE"),
                'metadata' => $metadata
            ];

            Log::debug('Sending request to Paystack', ['payload' => $payload]);

            $response = $this->httpRequest()->post($this->baseUrl . 'initialize', $payload);
            $responseData = $response->json();
            
            Log::debug('Paystack API response', [
                'status' => $response->status(),
                'response' => $responseData
            ]);

            if ($response->successful() && isset($responseData['data']['authorization_url'])) {
                Log::info('Payment link generated successfully', [
                    'authorization_url' => $responseData['data']['authorization_url']
                ]);
                return $responseData['data']['authorization_url'];
            }

            Log::error('Failed to generate payment link', [
                'status' => $response->status(),
                'response' => $responseData
            ]);
            return false;

        } catch (\Exception $e) {
            Log::error('Exception in getPaymentLink: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Retrieve a list of transactions from Paystack.
     *
     * @param array $filters Optional filters (e.g., 'perPage', 'page', 'from', 'to', 'status').
     * @return array|false Returns an array containing 'data' (transactions) and 'meta' (pagination) on success, or false on failure.
     */
    public function listTransactions(array $filters = [])
    {
        Log::info('Listing Paystack transactions', ['filters' => $filters]);

        try {
            $query = [];
            // Map common filter names to Paystack API parameter names
            if (isset($filters['perPage'])) $query['perPage'] = $filters['perPage'];
            if (isset($filters['page'])) $query['page'] = $filters['page'];
            if (isset($filters['from'])) $query['from'] = $filters['from']; // Expects YYYY-MM-DD HH:MM:SS format
            if (isset($filters['to'])) $query['to'] = $filters['to'];     // Expects YYYY-MM-DD HH:MM:SS format
            if (isset($filters['status'])) $query['status'] = $filters['status']; // e.g., 'success', 'failed', 'abandoned'
            // Add other filters as needed based on Paystack docs: https://paystack.com/docs/api/transaction/#list

            $response = $this->httpRequest()->get($this->baseUrl, $query);
            $responseData = $response->json();

            Log::debug('Paystack list transactions API response', [
                'status' => $response->status(),
                'response' => $responseData
            ]);

            if ($response->successful() && isset($responseData['data'])) {
                return $responseData; // Return the full response including meta and data
            }

            Log::error('Failed to list Paystack transactions', ['status' => $response->status(), 'response' => $responseData]);
            return false;

        } catch (\Exception $e) {
            Log::error('Exception in listTransactions: ' . $e->getMessage(), ['trace' => $e->getTraceAsString(), 'filters' => $filters]);
            return false;
        }
    }

    public function verifyPayment($reference)
    {
        try {
            $response = $this->httpRequest()->get($this->baseUrl . 'verify/' . $reference);
            
            if ($response->successful()) {
                return $response->json();
            }
            
            Log::error('Payment verification failed', [
                'reference' => $reference,
                'status' => $response->status(),
                'response' => $response->json()
            ]);
            
        } catch (\Exception $e) {
            Log::error('Exception in verifyPayment: ' . $e->getMessage(), [
                'reference' => $reference,
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        return false;
    }

    protected function httpRequest(): PendingRequest
    {
        $token = env('PAYSTACK_SECRET');
        if (empty($token)) {
            Log::error('Paystack secret key is not configured');
            throw new \RuntimeException('Payment configuration error');
        }
        
        return Http::withToken($token)
            ->timeout(30)
            ->retry(3, 100);
    }
}
