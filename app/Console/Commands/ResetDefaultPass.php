<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Submission;
use App\Models\Script;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class ResetDefaultPass extends Command
{
    protected $signature = 'reset:passwords';
    protected $description = 'Reset passwords for markers and moderators';

    public function handle()
    {
        $markers = User::role('marker')->get();
        $moderators = User::role('moderator')->get();
        foreach ($markers as $user) {
            $this->resetDefaultPassword($user);
        }
        foreach ($moderators as $user) {
            $this->resetDefaultPassword($user);
        }

        $this->info('Passwords updated successfully.');
    }

    public function resetDefaultPassword(User $user)
    {
        $user->password = Hash::make('iec@pass');
        $user->save();
    }
}
