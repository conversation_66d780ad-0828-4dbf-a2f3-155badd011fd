<?php

namespace App\Console\Commands;

use App\Models\Application;
use Illuminate\Console\Command;
use App\Models\Submission;
use App\Models\Script;

class CreatePLCScripts extends Command
{
    protected $signature = 'create:plc_scripts';
    protected $description = 'Create scripts for each result';

    public function handle()
    {
        $currentYear = date('Y');
        $activeApplications = Application::where('year', $currentYear)
            ->whereIn('level_id', [1, 2, 3])
            ->whereRaw('LOWER(name) NOT LIKE ?', ['%entrance%'])
            ->with(['courses.sub_courses', 'results.course'])->get();

        foreach ($activeApplications as $application) {
            foreach ($application->results as $result) {
                if ($result->course->sub_courses->isNotEmpty()) {
                    foreach ($result->course->sub_courses as $subCourse) {
                        Script::firstOrCreate([
                            'student_id' => $result->student_id,
                            'name' => "{$application->name} {$subCourse->name}",
                            'application_id' => $application->id,
                            'level_id' => $application->level_id,
                            'result_id' => $result->id,
               //            'section' => '',
                            'course_id' => $result->course->id,
                            'sub_course_id' => $subCourse->id,
                        ]);
                    }
                } else {
                    Script::firstOrCreate([
                        'student_id' => $result->student_id,
                        // 'section' => '',
                        'name' => "{$application->name} {$result->course->name}",
                        'application_id' => $application->id,
                        'level_id' => $application->level_id,
                        'result_id' => $result->id,
                        'course_id' => $result->course->id,
                    ]);
                }
            }
        }

        $this->info('Scripts created successfully.');
    }
}
