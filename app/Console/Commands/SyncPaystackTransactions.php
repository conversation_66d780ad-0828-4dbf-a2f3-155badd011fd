<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Repository\PaymentRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SyncPaystackTransactions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:paystack-transactions
                            {--from= : Start date (YYYY-MM-DD) to fetch transactions from}
                            {--to= : End date (YYYY-MM-DD) to fetch transactions up to}
                            {--status=success : Filter transactions by status (e.g., success, failed, abandoned)}
                            {--perPage=100 : Number of transactions per page (max 100)}
                            {--page=1 : Starting page number}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Syncs Paystack transactions with local orders, fixing missing references, incorrect amounts, and statuses.';

    protected $paymentRepository;

    /**
     * Create a new command instance.
     *
     * @param PaymentRepository $paymentRepository
     * @return void
     */
    public function __construct(PaymentRepository $paymentRepository)
    {
        parent::__construct();
        $this->paymentRepository = $paymentRepository;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting Paystack transaction sync...');

        $filters = $this->options();

        // Format dates for Paystack API if provided
        if ($filters['from']) {
            try {
                $filters['from'] = Carbon::parse($filters['from'])->startOfDay()->toDateTimeString();
            } catch (\Exception $e) {
                $this->error("Invalid 'from' date format. Please use YYYY-MM-DD.");
                return Command::FAILURE;
            }
        }
        if ($filters['to']) {
            try {
                $filters['to'] = Carbon::parse($filters['to'])->endOfDay()->toDateTimeString();
            } catch (\Exception $e) {
                $this->error("Invalid 'to' date format. Please use YYYY-MM-DD.");
                return Command::FAILURE;
            }
        }

        $page = (int) $filters['page'];
        $perPage = (int) $filters['perPage'];
        $totalProcessed = 0;
        $totalUpdated = 0; // Orders that had any field (amount/reference/status) changed
        $totalMatchedAndUpdatedByMetadata = 0; // Orders identified by metadata AND updated
        $totalUnmatched = 0;
        $ordersWithUpdatedUnitPrice = [];

        do {
            $this->info("Fetching page {$page}...");
            $currentFilters = array_merge($filters, ['page' => $page, 'perPage' => $perPage]);

            $paystackResponse = $this->paymentRepository->listTransactions($currentFilters);

            if (!$paystackResponse || empty($paystackResponse['data'])) {
                if ($page === 1) {
                    $this->info('No transactions found for the given filters.');
                } else {
                    $this->info('No more transactions found.');
                }
                break; // Exit loop if no data is returned
            }

            $transactions = $paystackResponse['data'];
            $meta = $paystackResponse['meta'];
            $this->line("paystack response meta = " . json_encode($meta));

            $this->info(sprintf('Processing %d transactions on page %d...', count($transactions), $page));

            foreach ($transactions as $paystackTransaction) {
                $totalProcessed++;
                $reference = $paystackTransaction['reference'];
                $amountKobo = $paystackTransaction['amount']; // Amount in kobo
                $amountGhc = $amountKobo / 100; // Convert to GHC
                $metadata = $paystackTransaction['metadata'];
                $paystackStatus = $paystackTransaction['status'];

                $orderProcessedAndUpdatedThisIteration = false;

                $this->line("Processing Paystack transaction: transaction=" . json_encode($paystackTransaction));
                $this->line("Processing Paystack transaction: Reference={$reference}, Amount={$amountGhc}, Status={$paystackStatus}");
                Log::debug("Processing Paystack transaction", ['reference' => $reference, 'amount' => $amountGhc, 'status' => $paystackStatus, 'metadata' => $metadata]);

                // 1. Try to find the order by reference
                $order = Order::where('reference', $reference)->first();

                // Calculate Paystack amount before estimated charges
                // Ensure charges() helper is robust and returns a decimal rate (e.g., 0.019 for 1.9%)
                // If charges() can be 0, (1 + charges()) is still valid.
                $chargeRate = charges(); // Assuming charges() returns the rate, e.g., 0.019
                $paystackAmountBeforeCharges = $amountGhc / (1 + $chargeRate);


                if ($order) {
                    $this->line(sprintf("  Found order by reference: ID=%s", $order->id));
                    Log::debug("Found order by reference", ['order_id' => $order->id, 'reference' => $reference]);

                    $orderNeedsSave = false;
                    $orderAmount = $order->unit_price * $order->qty;

                    $amountMatchesDirectlyWithPaystack = abs($orderAmount - $amountGhc) < 0.01;
                    $amountMatchesWithCalculatedBeforeCharges = abs($orderAmount - $paystackAmountBeforeCharges) < 0.01;

                    if (!$amountMatchesDirectlyWithPaystack && !$amountMatchesWithCalculatedBeforeCharges) {
                        $this->warn(sprintf("  Amount mismatch for order %s: DB Amount=%s, Paystack Raw Amount=%s, Paystack Amount (est. before charges)=%s", $order->id, money($orderAmount), money($amountGhc), money($paystackAmountBeforeCharges)));
                        Log::warning("Amount mismatch for order by reference", ['order_id' => $order->id, 'db_amount' => $orderAmount, 'paystack_raw_amount_ghc' => $amountGhc, 'paystack_amount_before_charges' => $paystackAmountBeforeCharges]);

                        if ($order->qty > 0) {
                            $newUnitPrice = $paystackAmountBeforeCharges / $order->qty;
                            $this->info(sprintf("    Updating unit_price for order %s from %s to %s", $order->id, money($order->unit_price), money($newUnitPrice)));
                            Log::info("Updating unit_price due to mismatch (order by reference)", ['order_id' => $order->id, 'old_unit_price' => $order->unit_price, 'new_unit_price' => $newUnitPrice]);
                            $order->unit_price = $newUnitPrice;
                            $ordersWithUpdatedUnitPrice[] = $order->id;
                            $orderNeedsSave = true;
                        } else {
                            $this->warn(sprintf("    Cannot update unit_price for order %s: quantity is zero or invalid.", $order->id));
                            Log::warning("Cannot update unit_price (order by reference) due to zero quantity", ['order_id' => $order->id]);
                        }
                    } elseif ($amountMatchesDirectlyWithPaystack && !$amountMatchesWithCalculatedBeforeCharges) {
                        $this->line(sprintf("  Order %s amount (%s) matches Paystack raw amount (%s) directly. Assuming charges were not applied or handled differently. No unit_price update.", $order->id, money($orderAmount), money($amountGhc)));
                        Log::info("Amount matches Paystack raw amount directly (order by reference)", ['order_id' => $order->id, 'db_amount' => $orderAmount, 'paystack_raw_amount_ghc' => $amountGhc]);
                    } elseif ($amountMatchesWithCalculatedBeforeCharges) {
                        $this->line(sprintf("  Order %s amount (%s) matches Paystack amount after reversing estimated charges (%s). No unit_price update.", $order->id, money($orderAmount), money($paystackAmountBeforeCharges)));
                        Log::info("Amount matches Paystack amount after reversing estimated charges (order by reference)", ['order_id' => $order->id, 'db_amount' => $orderAmount, 'paystack_amount_before_charges' => $paystackAmountBeforeCharges]);
                    }


                    // Ensure status is correct if the transaction was successful
                    if ($paystackStatus === 'success' && $order->status !== 1) {
                        $this->info(sprintf("  Updating status for order %s to paid (1)", $order->id));
                        $order->status = 1;
                        $orderNeedsSave = true;
                    }

                    if ($orderNeedsSave) {
                        $order->save();
                        $orderProcessedAndUpdatedThisIteration = true;
                    }

                } else {
                    // 2. Order not found by reference, try to match using metadata
                    $this->line("  Order not found by reference. Attempting to match by metadata...");
                    Log::debug("Order not found by reference", ['reference' => $reference, 'metadata' => $metadata]);

                    $uniqueMetadataMatchFound = false; // Indicates if a single potential order was found by metadata
                    $metadataMatchLedToUpdate = false; // Indicates if the metadata match resulted in an update

                    // Basic check for required metadata fields
                    if (isset($metadata['user_id']) && isset($metadata['application_id'])) {
                        // Find orders for this user and application that are NOT paid and have no reference yet
                        $potentialOrders = Order::where('user_id', $metadata['user_id'])
                            ->where('application_id', $metadata['application_id'])
                            ->where(function ($q) {
                                $q->whereNull('reference')
                                  ->orWhere('reference', '');
                            })
                            ->where('status', '!=', 1) // Only consider unpaid orders
                            ->get();

                        if ($potentialOrders->count() === 1) {
                            $uniqueMetadataMatchFound = true;
                            // Found a single potential match
                            $potentialOrder = $potentialOrders->first();
                            $this->line(sprintf("  Found single potential order %s by metadata.", $potentialOrder->id));
                            Log::debug("Found single potential order by metadata", ['order_id' => $potentialOrder->id, 'paystack_reference' => $reference, 'metadata' => $metadata]);

                            $orderNeedsSaveForMetadataMatch = false;
                            $orderAmount = $potentialOrder->unit_price * $potentialOrder->qty;

                            $amountMatchesDirectlyWithPaystack = abs($orderAmount - $amountGhc) < 0.01;
                            $amountMatchesWithCalculatedBeforeCharges = abs($orderAmount - $paystackAmountBeforeCharges) < 0.01;

                            if (!$amountMatchesDirectlyWithPaystack && !$amountMatchesWithCalculatedBeforeCharges) {
                                $this->warn(sprintf("  Amount mismatch for potential order %s (matched by metadata): DB Amount=%s, Paystack Raw Amount=%s, Paystack Amount (est. before charges)=%s", $potentialOrder->id, money($orderAmount), money($amountGhc), money($paystackAmountBeforeCharges)));
                                Log::warning("Amount mismatch for order matched by metadata", ['order_id' => $potentialOrder->id, 'db_amount' => $orderAmount, 'paystack_raw_amount_ghc' => $amountGhc, 'paystack_amount_before_charges' => $paystackAmountBeforeCharges]);
                                if ($potentialOrder->qty > 0) {
                                    $newUnitPrice = $paystackAmountBeforeCharges / $potentialOrder->qty;
                                    $this->info(sprintf("    Updating unit_price for potential order %s from %s to %s", $potentialOrder->id, money($potentialOrder->unit_price), money($newUnitPrice)));
                                    Log::info("Updating unit_price for order matched by metadata", ['order_id' => $potentialOrder->id, 'old_unit_price' => $potentialOrder->unit_price, 'new_unit_price' => $newUnitPrice]);
                                    $potentialOrder->unit_price = $newUnitPrice;
                                    $ordersWithUpdatedUnitPrice[] = $potentialOrder->id;
                                    $orderNeedsSaveForMetadataMatch = true;
                                } else {
                                    $this->warn(sprintf("    Cannot update unit_price for potential order %s: quantity is zero or invalid.", $potentialOrder->id));
                                    Log::warning("Cannot update unit_price (metadata match) due to zero quantity", ['order_id' => $potentialOrder->id]);
                                }
                            } elseif ($amountMatchesDirectlyWithPaystack && !$amountMatchesWithCalculatedBeforeCharges) {
                                $this->line(sprintf("  Potential order %s amount (%s) matches Paystack raw amount (%s) directly. No unit_price update.", $potentialOrder->id, money($orderAmount), money($amountGhc)));
                                Log::info("Amount matches Paystack raw amount directly (metadata match)", ['order_id' => $potentialOrder->id, 'db_amount' => $orderAmount, 'paystack_raw_amount_ghc' => $amountGhc]);
                            } elseif ($amountMatchesWithCalculatedBeforeCharges) {
                                $this->line(sprintf("  Potential order %s amount (%s) matches Paystack amount after reversing estimated charges (%s). No unit_price update.", $potentialOrder->id, money($orderAmount), money($paystackAmountBeforeCharges)));
                                Log::info("Amount matches Paystack amount after reversing estimated charges (metadata match)", ['order_id' => $potentialOrder->id, 'db_amount' => $orderAmount, 'paystack_amount_before_charges' => $paystackAmountBeforeCharges]);
                            }


                            if (empty($potentialOrder->reference) || $potentialOrder->reference !== $reference) {
                                $this->info(sprintf("  Updating reference for order %s from '%s' to '%s'.", $potentialOrder->id, $potentialOrder->reference, $reference));
                                $potentialOrder->reference = $reference;
                                $orderNeedsSaveForMetadataMatch = true;
                            }
                            
                            if ($paystackStatus === 'success' && $potentialOrder->status !== 1) {
                                $this->info(sprintf("  Updating status for order %s to paid (1).", $potentialOrder->id));
                                $potentialOrder->status = 1;
                                $orderNeedsSaveForMetadataMatch = true;
                            }

                            if ($orderNeedsSaveForMetadataMatch) {
                                $this->info(sprintf("  Saving updates for order %s (matched by metadata).", $potentialOrder->id));
                                $potentialOrder->save();
                                $metadataMatchLedToUpdate = true;
                                $orderProcessedAndUpdatedThisIteration = true;
                            } else {
                                $this->line(sprintf("  Order %s (matched by metadata) requires no updates.", $potentialOrder->id));
                            }

                        } elseif ($potentialOrders->count() > 1) {
                            // Multiple potential matches found - requires manual review
                            $this->warn(sprintf("  Multiple potential orders found for user %s, application %s with no reference. Manual review needed.", $metadata['user_id'], $metadata['application_id']));
                            Log::warning("Multiple potential orders found by metadata", ['user_id' => $metadata['user_id'], 'application_id' => $metadata['application_id'], 'potential_order_ids' => $potentialOrders->pluck('id')->toArray(), 'paystack_reference' => $reference]);
                        } else {
                            // No potential orders found for this user/application combination
                            $this->line("  No unpaid orders found for user/application in metadata that need linking.");
                        }
                    } else {
                        $this->warn("  Metadata missing user_id or application_id. Cannot match by metadata.");
                        Log::warning("Metadata missing required fields for metadata match", ['metadata' => $metadata, 'paystack_reference' => $reference]);
                    }

                    if ($metadataMatchLedToUpdate) {
                        $totalMatchedAndUpdatedByMetadata++;
                    }
                    
                    if (!$uniqueMetadataMatchFound) { // If no unique match was found (not 0, not >1, or missing metadata)
                        $this->line("  Could not match Paystack transaction to an order via metadata.");
                        Log::warning("Could not match Paystack transaction to an order via metadata", ['paystack_reference' => $reference, 'metadata' => $metadata]);
                        $totalUnmatched++;
                    }
                }

                if ($orderProcessedAndUpdatedThisIteration) {
                    $totalUpdated++;
                }
            }

            // Move to the next page if there are more transactions
            $page++;

        } while ($page <= $meta['pageCount']); // Continue if there are more pages

        $this->info('Paystack transaction sync completed.');
        $this->info("Total transactions processed from Paystack: {$totalProcessed}");
        $this->info("Total local orders modified (amount/reference/status): {$totalUpdated}");
        $this->info("Total orders matched and updated using metadata: {$totalMatchedAndUpdatedByMetadata}");
        $this->info("Total Paystack transactions that could not be matched to any local order: {$totalUnmatched}");

        if (!empty($ordersWithUpdatedUnitPrice)) {
            $uniqueUpdatedOrderIds = array_unique($ordersWithUpdatedUnitPrice);
            $this->info("Order IDs with updated unit prices: " . implode(', ', $uniqueUpdatedOrderIds));
            Log::info("Order IDs with updated unit prices after sync:", ['order_ids' => $uniqueUpdatedOrderIds]);
        } else {
            $this->info("No order unit prices were updated during this sync.");
            Log::info("No order unit prices were updated during this sync.");
        }

        return Command::SUCCESS;
    }
}