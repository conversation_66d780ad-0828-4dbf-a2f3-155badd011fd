<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ActivateEligibleStudents extends Command
{
    protected $signature = 'students:activate-eligible';
    protected $description = 'Activate all eligible students for exam registration';

    public function handle()
    {
        $this->info('Starting activation of eligible students...');
        
        $count = User::where('is_eligible', true)
            ->whereNull('email_verified_at')
            ->update([
                'email_verified_at' => now(),
            ]);
            
        $this->info("Successfully activated {$count} eligible student accounts.");
        Log::info("Activated {$count} eligible student accounts via command.");
        
        return Command::SUCCESS;
    }
}
