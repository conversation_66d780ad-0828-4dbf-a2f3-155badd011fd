<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Submission;
use App\Models\Script;

class CreateScripts extends Command
{
    protected $signature = 'create:scripts';
    protected $description = 'Create scripts for each submission';

    public function handle()
    {
        $submissions = Submission::all();

        foreach ($submissions as $submission) {
            $this->createScript($submission, 'A');
            $this->createScript($submission, 'B');
        }

        $this->info('Scripts created successfully.');
    }

    private function createScript(Submission $submission, string $section)
    {
        Script::create([
            'student_id' => $submission->student_id,
            'name' => $submission->name ?? '2024 Entrance Exam',
            'section' => $section,
            'submission_id' => $submission->id, // Use id directly
            'application_id' => $submission->application_id ?? null,
            'level_id' => $submission->level_id ?? null,
            'course_id' => $submission->course_id ?? null,
        ]);
    }
}
