<?php

namespace App\Providers;

use Filament\Support\Assets\Css;
use Filament\Support\Facades\FilamentAsset;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Log;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $environment = $this->app->environment();
        $recipient = env('MAIL_OVERRIDE_RECIPIENT');

        Log::info("Email Override Check", [
            'environment' => $environment,
            'is_production' => $this->app->environment('production'),
            'override_recipient' => $recipient ? 'SET' : 'NOT SET',
            'will_override' => !$this->app->environment('production') && $recipient
        ]);

        if (!$this->app->environment('production')) {
            if ($recipient) {
                Mail::alwaysTo($recipient);
                Log::info("Mail override APPLIED. All emails will go to: {$recipient}");
            } else {
                Log::info('Mail override NOT applied. No recipient specified. Emails will go to real users.');
            }
        } else {
            Log::info('Application is in production mode. Mail override NOT applied. Emails will go to real users.');
        }
        FilamentAsset::register([
            Css::make('custom-filament', public_path('css/custom-filament.css'))
        ]);
    }
}
