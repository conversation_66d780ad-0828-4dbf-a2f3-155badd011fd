<?php

namespace App\Providers\Filament;

use Althinect\FilamentSpatieRolesPermissions\FilamentSpatieRolesPermissionsPlugin;
use App\Filament\Pages\Welcome;
use App\Filament\Resources\AdminResource;
use App\Filament\Resources\CourseResource;
use App\Filament\Resources\OrderResource;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Str;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Filament\Navigation\NavigationItem;
use Spatie\Permission\Models\Role;


class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->brandLogo(asset('imgs/ieclogo.jpg'))
            ->brandLogoHeight('80px')
            ->path('admin')
            ->login()
            ->passwordReset()
            ->profile()
            ->sidebarWidth('220px')
            ->sidebarCollapsibleOnDesktop(true)
            ->colors([
                'primary' => Color::Amber,
            ])
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Welcome::class
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                // Widgets\AccountWidget::class,
                // Widgets\FilamentInfoWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])->brandName('IEC')
            ->plugin(FilamentSpatieRolesPermissionsPlugin::make())
            ->databaseNotifications()
            ->databaseNotificationsPolling('30s')
            ->navigationItems([...$this->adminNavs()]);
    }

    public function adminNavs(): array
    {
        // $i = 3;
        $adminNavs = [];

        // foreach ([null, ...Role::all()] as $role) {
        //     $adminNavs[] = NavigationItem::make(Str::plural(ucfirst($role->name ?? "All Admins")))
        //         ->url(fn(): string => AdminResource::getUrl("index", ["tableFilters[roles][value]" => $role?->name]))
        //         // ->sort(++$i)
        //         ->group('Admin Accounts')
        //         ->isActiveWhen(fn() => request()->fullUrlIs(AdminResource::getUrl("index", ["tableFilters[roles][value]" => $role?->name])));
        // }

        return $adminNavs;
    }
}
