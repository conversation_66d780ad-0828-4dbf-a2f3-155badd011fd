<?php

namespace App\Livewire;

use App\FormResources\FormResource;
use App\Models\EVoucher;
use App\Repository\PaymentRepository;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Livewire\Component;

class VoucherRedemption extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];
    public $voucher = null;
    public $examFee;
    public $error = null;

    public function mount(): void
    {
        $application = currentApplication();
        $this->examFee = $application ? $application->amount : 550;
        $this->form->fill();
    }

    public function render()
    {
        return view('livewire.voucher-redemption');
    }

    public function form(Form $form): Form
    {
        return FormResource::voucherRedemption($form)
            ->statePath('data')
            ->model(EVoucher::class);
    }

    public function validateVoucher()
    {
        $data = $this->form->getState();
        $voucherCode = strtoupper(trim($data['voucher_code']));

        $this->voucher = EVoucher::where('voucher_code', $voucherCode)
            ->where('status', 'paid')
            ->first();

        if (!$this->voucher) {
            $this->error = "Invalid voucher code or voucher has not been paid for yet.";
            return;
        }

        if (!$this->voucher->isRedeemable()) {
            $this->error = "This voucher has already been used or is not valid for redemption.";
            return;
        }

        $this->error = null;
    }

    public function proceedToExamPayment()
    {
        if (!$this->voucher || !$this->voucher->isRedeemable()) {
            $this->error = "Please validate your voucher code first.";
            return;
        }

        $application = currentApplication();
        if (!$application) {
            $this->error = "No active application found.";
            return;
        }

        // Create user object for payment
        $user = new \App\Models\User();
        $user->email = $this->voucher->email;

        $paymentUrl = (new PaymentRepository())->getPaymentLink(
            $user, 
            amountWithCharges($this->examFee), 
            [
                'application_id' => $application->id,
                'voucher_id' => $this->voucher->id,
                'transaction_type' => 'exam_fee_with_voucher',
                'charges' => $this->examFee * charges(),
                'email' => $this->voucher->email,
                'phone' => $this->voucher->phone,
                'first_name' => $this->voucher->first_name,
                'last_name' => $this->voucher->last_name,
            ]
        );

        if (!$paymentUrl) {
            $this->error = "Payment link generation failed. Please try again.";
            return;
        }

        return redirect($paymentUrl);
    }
}
