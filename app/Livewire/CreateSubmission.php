<?php

namespace App\Livewire;

use App\Filament\Resources\ResourceUtilities\ResourceUtilities;
use App\FormResources\FormResource;
use App\Models\Submission;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Livewire\Component;
use Illuminate\Contracts\View\View;

class CreateSubmission extends Component implements HasForms, HasInfolists
{
    use InteractsWithForms;
    use InteractsWithInfolists;

    public ?array $data = [];
    public $submitted;
    public Submission $record;
    public $editRoute = false;

    public function mount(): void
    {
        $this->editRoute = Route::currentRouteName() == 'entrance.edit';

        $data = $this->record->attributesToArray();

        // Handle institution logic for existing records
        if (isset($data['institution']) && !empty($data['institution'])) {
            $predefinedInstitutions = institutionList();

            if (!in_array($data['institution'], $predefinedInstitutions)) {
                $data['institution_other'] = $data['institution'];
                $data['institution'] = '-Other-';
            }
        }

        $this->form->fill($data);
    }

    public function form(Form $form): Form
    {
        return FormResource::entrance($form)
            ->columns(2)
            ->statePath('data')
            ->model($this->record);
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return ResourceUtilities::EntranceInfolist($infolist, true)->record($this->record);
    }

    public function editForm(): void
    {
        $this->redirect(route('entrance.application'));
    }

    public function finalSubmission(): void
    {
        $this->record->update([
            'isSubmitted' => 1
        ]);

        $this->redirect(route('entrance.application'));
    }

    public function create()
    {
        $cred = Auth::guard('cred')->user();
        $formData = $this->form->getState();

        // Handle institution logic
        if (isset($formData['institution']) && $formData['institution'] === '-Other-') {
            if (isset($formData['institution_other']) && !empty($formData['institution_other'])) {
                $formData['institution'] = $formData['institution_other'];
            }
        }

        // Remove institution_other from the data to be saved
        unset($formData['institution_other']);

        $this->record = Submission::updateOrCreate([
            'cred_id' => $cred->id,
            'application_id' => $cred->application_id
        ], $formData);

        $this->form->model($this->record)->saveRelationships();

        Notification::make()
            ->title('Saved successfully')
            ->success()
            ->send();

        $this->redirect(route('entrance.preview'));
    }

    public function render(): View
    {
        return view('livewire.create-submission');
    }
}
