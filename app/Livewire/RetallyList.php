<?php

namespace App\Livewire;

use App\Repository\PaymentRepository;
use Livewire\Component;

class RetallyList extends Component
{
    public $applications;
    public $retallyApplication;
    public $selectedResults = [];
    private $cost = 0;

    public function toggleSelection($choice, $application_id)
    {
        if (in_array($choice, $this->selectedResults)) {
            $this->selectedResults = array_diff($this->selectedResults, [$choice]);
        } else {
            $this->selectedResults[] = $choice;
        }

        $this->cost = $this->retallyApplication->amount * count($this->selectedResults);

        $this->dispatch('selected-results', selectedResults: $this->selectedResults);
    }

    public function mount($applications, $retallyApplication)
    {
        $this->applications = $applications;
        $this->retallyApplication = $retallyApplication;
    }

    public function submitRetallying()
    {
        $user = request()->user();
        $amount = $this->retallyApplication->amount * count($this->selectedResults);

        $paymentUrl = (new PaymentRepository())->getPaymentLink($user, amountWithCharges($amount), [
            'transaction' => 'retallying',
            'application_id' => $this->retallyApplication->id,
            'results' => $this->selectedResults,
            'charges' => $amount * charges(),
            'user_id' => $user->id
        ]);

        if (!$paymentUrl) back()->with('error', 'Payment unsuccessful');

        return redirect($paymentUrl);
    }

    public function render()
    {
        return view('livewire.retally-list');
    }
}
