<?php

namespace App\Livewire;

use App\FormResources\FormResource;
use App\Models\EVoucher;
use App\Repository\PaymentRepository;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Livewire\Component;

class VoucherPurchase extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];
    public $voucherAmount;
    public $examFee;

    public function mount(): void
    {
        $application = currentApplication();

        // Check if current application supports vouchers
        if (!$application || !$application->supportsVouchers()) {
            session()->flash('error', 'E-vouchers are not available for this application type.');
            $this->redirect(route('entrance.registration'));
            return;
        }

        $this->voucherAmount = $application->getVoucherAmount() ?? 200;
        $this->examFee = $application->amount ?? 550;
        $this->form->fill();
    }

    public function render()
    {
        return view('livewire.voucher-purchase');
    }

    public function form(Form $form): Form
    {
        return FormResource::voucherPurchase($form)
            ->columns(2)
            ->statePath('data')
            ->model(EVoucher::class);
    }

    public function purchaseVoucher()
    {
        $data = $this->form->getState();
        $application = currentApplication();

        if (!$application) {
            session()->flash('error', 'No active application found.');
            return;
        }

        if (!$application->supportsVouchers()) {
            session()->flash('error', 'E-vouchers are not available for this application type.');
            return;
        }

        // Create E-voucher record
        $voucher = EVoucher::create([
            'voucher_code' => EVoucher::generateVoucherCode(),
            'email' => $data['email'],
            'phone' => $data['phone'],
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'amount' => $this->voucherAmount,
            'charges' => $this->voucherAmount * charges(),
            'application_id' => $application->id,
            'status' => 'pending'
        ]);

        // Create payment link for voucher purchase
        $user = new \App\Models\User();
        $user->email = $data['email'];

        $paymentUrl = (new PaymentRepository())->getPaymentLink(
            $user, 
            amountWithCharges($this->voucherAmount), 
            [
                'application_id' => $application->id,
                'voucher_id' => $voucher->id,
                'transaction_type' => 'voucher_purchase',
                'charges' => $this->voucherAmount * charges(),
                ...$data
            ]
        );

        if (!$paymentUrl) {
            $voucher->delete();
            session()->flash('error', 'Payment link generation failed. Please try again.');
            return;
        }

        return redirect($paymentUrl);
    }
}
