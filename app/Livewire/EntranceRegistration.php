<?php

namespace App\Livewire;

use App\FormResources\FormResource;
use App\Models\Cred;
use App\Models\Order;
use App\Models\User;
use App\Repository\PaymentRepository;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class EntranceRegistration extends Component implements HasForms
{
    use InteractsWithForms;

    public $step = 1;

    public ?array $data = [];
    private $submitted = false;


    public function mount()
    {
        if (Auth::guard('cred')->check()) return redirect()->route('entrance.application');

        // Handle different message types and set appropriate step
        $message = request()->get('message');
        if ($message == 'voucher_purchased') {
            $this->selectStep(4); // Go to voucher redemption step
        } elseif ($message == 'entrance') {
            $this->selectStep(3); // Go to login step
        } else {
            $this->selectStep(request()->step ?? 1);
        }

        $this->form->fill();
    }

    public function selectStep($id)
    {
        $this->step = $id;
    }

    public function form(Form $form): Form
    {
        return FormResource::cred($form)
            ->columns(2)
            ->statePath('data')
            ->model(Cred::class);
    }

    // Old single-payment method removed - now using two-step voucher process

    public function render()
    {
        return view('livewire.entrance-registration');
    }
}
