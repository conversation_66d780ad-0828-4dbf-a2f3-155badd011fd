<?php

namespace App\Livewire;

use App\Models\RemarkResult;
use App\Repository\PaymentRepository;
use Livewire\Component;

class RemarkList extends Component
{
    public $applications;
    public $remarkApplication;
    public $selectedResults = [];
    private $cost = 0;

    public function toggleSelection($choice, $application_id)
    {
        if (in_array($choice, $this->selectedResults)) {
            $this->selectedResults = array_diff($this->selectedResults, [$choice]);
        } else {
            $this->selectedResults[] = $choice;
        }

        $this->cost = $this->remarkApplication->amount * count($this->selectedResults);

        $this->dispatch('selected-results', selectedResults: $this->selectedResults);
    }

    public function cancelRemarkRequest($remarkResultId)
    {
        $remarkResult = RemarkResult::find($remarkResultId);
        if($remarkResult) {
            $remarkResult->status = 'cancel_requested';
            $remarkResult->save();
            session()->flash('success', 'Remark cancel requested successfully');
        } else {
            dd('remark result not found');
            session()->flash('error', 'Failed to submit cancel request');
        }
    }

    public function mount($applications, $remarkApplication)
    {
        $this->applications = $applications;
        $this->remarkApplication = $remarkApplication;
    }

    public function submitRemarking()
    {
        $user = request()->user();
        $amount = $this->remarkApplication->amount * count($this->selectedResults);

        $paymentUrl = (new PaymentRepository())->getPaymentLink($user, amountWithCharges($amount), [
            'transaction' => 'remarking',
            'application_id' => $this->remarkApplication->id,
            'results' => $this->selectedResults,
            'charges' => $amount * charges(),
            'user_id' => $user->id
        ]);

        if (!$paymentUrl) back()->with('error', 'Payment unsuccessful');

        return redirect($paymentUrl);
    }

    public function render()
    {
        return view('livewire.remark-list');
    }
}
