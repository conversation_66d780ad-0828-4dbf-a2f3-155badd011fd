<?php

namespace App\Livewire;

use App\Repository\PaymentRepository;
use Livewire\Component;

class RetakePapers extends Component
{
    public $application;
    public $referrals;
    public $transaction;

    public $selectedCourses = [];

    public function mount($application, $referrals)
    {
        $this->application = $application;
        $this->referrals = $referrals;
    }

    public function toggleSelection($choice)
    {
        if (in_array($choice, $this->selectedCourses)) {
            $this->selectedCourses = array_diff($this->selectedCourses, [$choice]);
        } else {
            $this->selectedCourses[] = $choice;
        }

        $this->cost = $this->transaction->amount * count($this->selectedCourses);

        $this->dispatch('selected-courses', selectedCourses: $this->selectedCourses);
    }

    public function submitRetakes()
    {
        $user = request()->user();
        $amount = $this->transaction->amount * count($this->selectedCourses);

        $paymentUrl = (new PaymentRepository())->getPaymentLink($user, amountWithCharges($amount), [
            'transaction' => 'referral',
            'transaction_id' => $this->transaction->id,
            'application_id' => $this->application->id,
            'charges' => $amount * charges(),
            'courses' => $this->selectedCourses,
            'user_id' => $user->id,
            'student_id' => $user->student_id,
        ]);

        if (!$paymentUrl) back()->with('error', 'Payment unsuccessful');

        return redirect($paymentUrl);
    }

    public function render()
    {
        return view('livewire.retake-papers');
    }
}
