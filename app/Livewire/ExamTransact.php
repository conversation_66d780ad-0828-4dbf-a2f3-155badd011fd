<?php

namespace App\Livewire;

use App\Models\Pwd;
use App\Repository\PaymentRepository;
use Filament\Forms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Livewire\Component;
use Illuminate\Contracts\View\View;

class ExamTransact extends Component implements HasForms
{
    use InteractsWithForms;

    public $application;
    public $amount;

    public ?array $data = [];

    public function mount($amount = null): void
    {
        $this->amount = $amount ?? $this->application->amount;
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Toggle::make('disability')
                    ->inline(false)
                    ->live()
                    ->label('Do you have any disability?'),
                Forms\Components\Fieldset::make('Disability Information')
                    ->columns(2)
                    ->hidden(fn(Forms\Get $get) => !$get('disability'))
                    ->schema([
                        Forms\Components\Select::make('pwd_type')
                            ->options([...disabilities(), "Other" => "Other"])
                            ->label("Select Disability")
                            ->live()
                            ->placeholder('Select Disability')
                            ->required(),
                        Forms\Components\TextInput::make('pwd_details')
                            ->label('What is your disability?')
                            ->placeholder('describe your disability')
                            ->hidden(fn(Forms\Get $get) => $get('pwd_type') !== "Other")
                            ->required(),
                        Forms\Components\SpatieMediaLibraryFileUpload::make('pwd')
                            ->label('Upload Supporting Documents')
                            ->downloadable()
                            ->multiple()
                            ->collection('pwd')
                            ->required()
                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']),
                    ]),
            ])
            ->statePath('data')
            ->model(Pwd::class);
    }

    public function create()
    {
        $user = request()->user();
        $data = $this->form->getState();

        if ($data['disability']) {
            $record = Pwd::updateOrCreate([
                'application_id' => $this->application->id,
                'user_id' => request()->user()->id,
            ], [
                'pwd_type' => $data['pwd_type'],
                'pwd_details' => $data['pwd_details'] ?? '',
            ]);
        } else {
            $record = Pwd::where('application_id', $this->application->id)
                ->where('user_id', request()
                    ->user()->id)
                ->delete();
        }

        $this->form->model($record)->saveRelationships();

        $paymentUrl = (new PaymentRepository())->getPaymentLink($user, amountWithCharges($this->amount), [
            'application_id' => $this->application->id,
            'user_id' => $user->id,
            'student_id' => $user->student_id,
            'charges' => $this->amount * charges()
        ]);

        if (!$paymentUrl) back()->with('error', 'Payment unsuccessful');

        return redirect($paymentUrl);
    }

    public function render(): View
    {
        return view('livewire.exam-transact', [
            'amount' => $this->amount
        ]);
    }
}
