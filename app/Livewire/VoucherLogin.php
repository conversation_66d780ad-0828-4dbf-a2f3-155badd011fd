<?php

namespace App\Livewire;

use App\FormResources\FormResource;
use App\Models\Cred;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class VoucherLogin extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];
    private $submitted = false;
    private $error;

    public function mount(): void
    {
        $this->form->fill();
    }

    public function render()
    {
        return view('livewire.voucher-login');
    }

    public function form(Form $form): Form
    {
        return FormResource::credLogin($form)
                ->statePath('data')
                ->columns(1)
                ->model(Cred::class);
    }

    public function login()
    {
        $details = $this->form->getState();
        if (Auth::guard('cred')->attempt(['serial' => $details['serial'], 'password' => $details['pin']])) {
            return redirect()->route('entrance.application');
        }
        $this->error = "The serial number and/or pin you entered is not valid";
    }
}
