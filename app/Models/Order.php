<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory, Uuids;

    public $incrementing = false;

    protected $guarded = ['status'];

    protected $casts = ['meta' => 'array'];

    public function user()
    {
        return $this->transaction_id === 1
            ? $this->belongsTo(Cred::class, 'user_id', 'id')
            : $this->belongsTo(User::class);
    }

    public function cred()
    {
        return $this->belongsTo(Cred::class, 'user_id', 'id');
    }

    public function getFullnameAttribute()
    {
        return $this->user?->full_name;
    }

    public function getStudentIdAttribute()
    {
        return $this->user?->student_id;
    }

    public function viewUser()
    {
        return $this->belongsTo(ViewUser::class, 'user_id', 'id');
    }

    public function getNewName()
    {
        return $this->orderUser?->full_name;
    }

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    public function transaction()
    {
        return $this->belongsTo(Transaction::class);
    }

    public function scopeEntranceTransactions(Builder $query): void
    {
        $query->where('transaction_id', 1);
    }

    public function scopeOtherTransactions(Builder $query): void
    {
        $query->where('transaction_id', '!=', 1);
    }
    
    public function scopeScholarships(Builder $query): void
    {
        $query->where(function($q) {
            $q->where('reference', 'scholarship')
              ->orWhere('reference', 'like', 'scholarship:%');
        });
    }
    
    public function isScholarship(): bool
    {
        return str_starts_with($this->reference, 'scholarship:') || $this->reference === 'scholarship';
    }

    public function getScholarshipType(): ?string
    {
        if (!$this->isScholarship()) {
            return null;
        }
        
        if ($this->reference === 'scholarship') {
            return 'general';
        }
        
        // Extract the type from 'scholarship:type'
        return str_replace('scholarship:', '', $this->reference);
    }

    public function scopeScholarshipType(Builder $query, string $type): void
    {
        $query->where('reference', 'scholarship:' . $type);
    }
}
