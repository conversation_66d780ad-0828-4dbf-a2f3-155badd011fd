<?php

namespace App\Models;

use App\Traits\HasExam;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Result extends Model
{
    use HasFactory, HasExam;

    protected $guarded = [];

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function level()
    {
        return $this->belongsTo(Level::class);
    }

    public function venue()
    {
        return $this->belongsTo(Venue::class);
    }

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    public function scripts()
    {
        return $this->hasMany(Script::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'student_id', 'student_id');
    }

    public function getScoreAttribute()
    {
        return $this->total_score;
    }

    public function getFinalScoreAttribute()
    {
        $remark = $this->remarkResult;
        // If exempted, the score for calculation purposes could be the pass_mark.
        if ($this->is_exempted) {
            return $this->course->pass_mark ?? 50;
        }
        return $remark && !is_null($remark->score) ? $remark->score : $this->total_score;
    }

    public function didPass()
    {
        if ($this->is_exempted) {
            return true;
        }
        return $this->course->didPass($this->finalScore);
    }

    public function remarksText()
    {
        if ($this->is_exempted) {
            return 'Exempted: ' . $this->exemption_reason ?? 'You are exempted from this course';
        }
        // return $this->course->remarksText($this->finalScore);
    }



    public function remarksTextForScore($score)
    {
        return $this->course->remarksText($score);
    }

    public function remarkResult()
    {
        return $this->hasOne(RemarkResult::class);
    }

    public function retallyResult()
    {
        return $this->hasOne(RetallyResult::class);
    }

    public function sanction()
    {
        return $this->belongsTo(Sanction::class);
    }

    public function totalScore()
    {
        if ($this->is_exempted) {
            return $this->course->pass_mark ?? 50; // Or null if it shouldn't contribute to a sum
        }
        return $this->scripts->sum(function ($script) {
            return $script?->questions->sum(function ($question) use ($script) {
                return $script->isMarkerScoreWithinThreshold() && $script->moderator_score_confirmed ? $question->moderator_score : $question->marker_score;
            });
        });
    }

    public function moderatorScore()
    {
        if ($this->notModeratable()) {
            return 0;
        }

        return $this->scripts->sum(function ($script) {
            return $script?->questions->sum(function ($question) use ($script) {
                return $script->isMarkerScoreWithinThreshold() && $script->moderator_score_confirmed ? $question->moderator_score : $question->marker_score;
            });
        });
    }
    public function notModeratable()
    {
        return $this->scripts->every(function ($script) {
            return !$script->isMarkerScoreWithinThreshold();
        });
    }
}
