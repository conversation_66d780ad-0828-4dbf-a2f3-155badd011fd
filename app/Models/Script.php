<?php

namespace App\Models;

use App\Traits\FormatsScores;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Spatie\MediaLibrary\HasMedia;
use <PERSON><PERSON>\MediaLibrary\InteractsWithMedia;

class Script extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia, Notifiable, FormatsScores;

    protected $guarded = [];

    protected const DEFAULT_SCRIPT_SCORE_DENOMINATOR = 100;
    protected const DEFAULT_SCRIPT_MAX_ANSWERABLE_QUESTIONS = 4;
    protected const DEFAULT_SCRIPT_MAX_PROVIDED_QUESTIONS = 6;

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function result()
    {
        return $this->belongsTo(Result::class);
    }

    public function sub_course()
    {
        return $this->belongsTo(SubCourse::class, 'sub_course_id');
    }

    public function level()
    {
        return $this->belongsTo(Level::class);
    }

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    public function submission()
    {
        return $this->belongsTo(Submission::class);
    }

    public function marker()
    {
        return $this->belongsTo(User::class, 'marker_id')->markers();
    }

    public function moderator()
    {
        return $this->belongsTo(User::class, 'moderator_id')->moderators();
    }

    public function didPass($score)
    {
        return $score >= ($this->pass_mark ?? 50);
    }

    public function wasModerated()
    {
        return $this->moderator_score_confirmed;
    }

    public function questions()
    {
        return $this->hasMany(Question::class);
    }

    public function getScriptScanAttribute()
    {
        return $this->getFirstMediaUrl('Script_Scans') ?: "https://via.placeholder.com/300";
    }

    public function getAttendanceDisplayAttribute()
    {
        // Check if the related submission has 'wrote_exam' as false
        return $this?->result?->wrote_exam === 1 ? 'PRESENT' : 'ABSENT';
    }

    public function getWroteExamAttribute()
    {
        // Check if the related submission has 'wrote_exam' as false
        return $this?->result?->wrote_exam;
    }

    public function isSubCourseScript(): bool
    {
        return !is_null($this?->sub_course);
    }

    public function isSubCourseScriptType(string $type): bool
    {
        return $this->isSubCourseScript() && str_contains($this?->sub_course?->name, $type);
    }

    public function isAdvocacyScript(): bool
    {
        return $this->isSubCourseScriptType('Advocacy');
    }

    public function isPracticalScript(): bool
    {
        return $this->isSubCourseScriptType('Practical');
    }

    public function isLPMScript(): bool
    {
        return $this->isSubCourseScriptType('Law Practice Management');
    }

    public function isLegalAccountancyScript(): bool
    {
        return $this->isSubCourseScriptType('Legal Accountancy');
    }


    public function questionScoreDenominator($questionNumber)
    {
        if ($this->isAdvocacyScript()) {
            return $this->getAdvocacyQuestionScoreDenominator($questionNumber);
        } elseif ($this->isPracticalScript()) {
            return $this->getPracticalsQuestionScoreDenominator($questionNumber);
        } elseif ($this->isLPMScript()) {
            // LPM scripts have 4 questions, so 25 points each (4 × 25 = 100)
            return 25;
        }

        // For scripts with 6 questions (non-LPM, non-advocacy), distribute 100 points across 6 questions
        // Using approximately 16.67 points per question (6 × 16.67 ≈ 100)
        return 16.67;
    }

    public function scriptScoreDenominator()
    {
        if ($this->isPracticalScript()) {
            return 30;
        } elseif ($this->isAdvocacyScript()) {
            return 70;
        } elseif ($this->isLPMScript()) {
            return 100; // 4 questions × 25 points each
        }

        // For all other scripts (including non-subcourse scripts and other subcourse types)
        // These have 6 questions, so total should be 100
        return 100;
    }

    public function markerLabel($questionNumber)
    {
        if ($this->isPracticalScript()) {
            return match($questionNumber) {
                1 => 'Facts',
                2=> 'Argument',
                3=> 'Expression & Diction',
                4 => 'Presentation & Appearance',
                default => 'Marker Q' . $questionNumber
            };
        }
        return 'Marker Q' . $questionNumber;
    }

    public function moderatorLabel($questionNumber)
    {
        if ($this->isPracticalScript()) {
            return match($questionNumber) {
                1 => 'Mod Facts',
                2=> 'Mod Argument',
                3=> 'Mod Expression & Diction',
                4 => 'Mod Presentation & Appearance',
                default => 'Moderator Q' . $questionNumber
            };
        }
        return 'Moderator Q' . $questionNumber;
    }

    public function maxProvidedQuestions()
    {
        if (!$this->isSubCourseScript()) {
            return self::DEFAULT_SCRIPT_MAX_PROVIDED_QUESTIONS;
        }

        if ($this->isPracticalScript()) {
            return 4;
        } elseif ($this->isAdvocacyScript()) {
            return 6;
        } elseif ($this->isLPMScript() || $this->isLegalAccountancyScript()) {
            return 3;
        } elseif ($this->isSubCourseScript()) {
            return 3;
        }
    }

    public function maxAnswerableQuestions()
    {
        if (!$this->isSubCourseScript()) {
            return self::DEFAULT_SCRIPT_MAX_ANSWERABLE_QUESTIONS;
        }

        if ($this->isPracticalScript()) {
            return 4;
        } elseif ($this->isAdvocacyScript()) {
            return 4;
        } elseif ($this->isLPMScript() || $this->isLegalAccountancyScript()) {
            return 2;
        } elseif ($this->isSubCourseScript()) {
            return 2;
        }
    }


    // Helper method to get the score for advocacy scripts based on question number
    public function getAdvocacyQuestionScoreDenominator($questionNumber)
    {
        $questionScoreDenominator = [
            1 => 28,
            2 => 14,
            3 => 14,
            4 => 14,
        ];

        return $questionScoreDenominator[$questionNumber] ?? 14; // Default to 0 for other questions
    }

    public function getPracticalsQuestionScoreDenominator($questionNumber)
    {
        $questionScoreDenominator = [
            1 => 10,
            2 => 7,
            3 => 7,
            4 => 6,
        ];

        return $questionScoreDenominator[$questionNumber] ?? 15; // Default to 0 for other questions
    }

    public function filledQuestionsCount($scoreType = 'marker_score'): int
    {
        return $this->questions->filter(function ($question) use ($scoreType) {
            return !is_null($question->$scoreType); // Count only filled questions
        })->count();
    }

    public function isMarkerScoreWithinThreshold()
    {
        $thresholdSlugs = [
            'default' => 'moderator_score_thresholds',
            'advocacy_theory' => 'advocacy_theory_course_moderator_score_thresholds',
            'advocacy_practicals' => 'advocacy_practicals_course_moderator_score_thresholds',
            'split_course' => 'split_course_moderator_score_thresholds',
        ];

        $thresholdSlug = $thresholdSlugs['default'];
        if ($this->isAdvocacyScript()) {
            $thresholdSlug = $thresholdSlugs['advocacy_theory'];
        } elseif ($this->isPracticalScript()) {
            $thresholdSlug = $thresholdSlugs['advocacy_practicals'];
        } elseif ($this->isSubCourseScript()) {
            $thresholdSlug = $thresholdSlugs['split_course'];
        }

        $thresholdsSetting = Setting::where('slug', $thresholdSlug)->first();
        $thresholdsValue = $thresholdsSetting ? $thresholdsSetting->value : '';
        $thresholds = array_map('trim', explode(',', $thresholdsValue));
        if (!$thresholds) return false;
        $thresholds = array_map(function ($range) {
            return array_map('intval', explode('-', $range));
        }, $thresholds);

        $score = $this->questions->sum('marker_score');
        foreach ($thresholds as $range) {
            if ($score >= $range[0] && $score <= $range[1]) {
                return true;
            }
        }
        return false;
    }

    public function canEditMarker($questionNumber): bool
    {
        $hasModeratorEdits = false;
        if (!$this->questions || ($this->filledQuestionsCount() >= $this->maxAnswerableQuestions()
            && is_null($this->questions[$questionNumber - 1]->marker_score))) {
            return false;
        }

        $hasModeratorEdits = $this->questions->where('question_number', $questionNumber)
            ->whereNotNull('moderator_score')
            ->isNotEmpty();

        return !$hasModeratorEdits
            && $this->result?->wrote_exam
            && (!$this->marker_score_confirmed || $this->reactivate_marker);
    }


    public function canEditModerator($questionNumber): bool
    {
        $hasMarkerEdits = $this->questions->where('question_number', $questionNumber)
            ->whereNotNull('marker_score')
            ->isNotEmpty();
        return $hasMarkerEdits
            && $this->isMarkerScoreWithinThreshold()
            && $this->marker_score_confirmed
            && (!$this->moderator_score_confirmed || $this->reactivate_moderator)
            && $this->result?->wrote_exam;
    }

    // Add methods to handle score confirmation
    public function confirmMarkerScore()
    {
        $this->marker_score_confirmed = true;
        $this->reactivate_marker = false; // Reset the reactivation flag
        $this->save();
    }

    public function confirmModeratorScore()
    {
        $this->moderator_score_confirmed = true;
        $this->reactivate_moderator = false; // Reset the reactivation flag
        $this->save();
    }
}
