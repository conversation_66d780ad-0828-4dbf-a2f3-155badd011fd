<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Transaction extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function applications()
    {
        return $this->hasMany(Application::class);
    }

    public function currentApplication($name)
    {
        return $this->where('name', $name)->first()->applications()->orderBy('created_at', 'DESC')->first();
    }
}
