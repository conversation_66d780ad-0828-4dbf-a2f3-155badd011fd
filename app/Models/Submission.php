<?php

namespace App\Models;

use App\Notifications\SubmissionReceived;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Submission extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia, Notifiable;

    public $incrementing = false;

    protected $guarded = [];

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    public function venue()
    {
        return $this->belongsTo(Venue::class);
    }

    public function entranceResult()
    {
        return $this->hasOne(EntranceResult::class);
    }
    public function approve()
    {
        return $this->update(['status' => 1]);
    }

    public function markPresent()
    {
        return $this->update(['wrote_exam' => 1]);
    }

    public function markAbsent()
    {
        return $this->update(['wrote_exam' => 0]);
    }

    public function reject()
    {
        return $this->update(['status' => 0]);
    }

    public function markAsPending()
    {
        return $this->update(['status' => null]);
    }

    public function getFilesAttribute()
    {
        return $this->getMedia('*');
    }

    public function getFileUrlsAttribute()
    {
        return $this->files->implode('original_url', ', ');
    }

    public function getAvatarAttribute()
    {
        return $this->getFirstMediaUrl('Passport Pic') ?: "https://via.placeholder.com/300";
    }

    public function getPassportPicUrlAttribute()
    {
        $media = $this->getFirstMedia('Passport Pc');
        return $media ? $media->getUrl() : null;
    }
    public function marker()
    {
        return $this->belongsTo(User::class, 'marker_id');
    }

    public function moderator()
    {
        return $this->belongsTo(User::class, 'moderator_id');
    }

    public function getMarkerRemarksAttribute()
    {
        if ($this->wrote_exam === 0) return "ABSENT";
        if (is_null($this->marker_score)) return "";
        return $this->marker_score < 50 ? "FAIL" : "PASS";
    }

    public function getModeratorRemarksAttribute()
    {
        if ($this->wrote_exam === 0) return "ABSENT";
        if (is_null($this->moderator_score)) return "";
        return $this->moderator_score < 50 ? "FAIL" : "PASS";
    }

    protected $casts = ['id' => 'string'];
    protected $keyType = 'string';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->{$model->getKeyName()} = (string)Str::uuid();
            $model->year = $model->application->year;

            // Set serial field if not provided
            if (empty($model->serial)) {
                // Get the next serial number for this application
                $lastSerial = static::where('application_id', $model->application_id)
                    ->max('serial') ?? 0;
                $model->serial = $lastSerial + 1;
            }
        });

        static::created(function ($submission) {
            $submission->notify(new SubmissionReceived());
        });

        static::addGlobalScope('orderByCreatedAt', function (Builder $builder) {
            $builder->orderBy('created_at', 'DESC');
        });
    }
}
