<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EntranceResult extends Model
{
    use HasFactory;

    protected $guarded = [];

    // Add computed attributes
    protected $appends = [
        'sec_a_marker_score',
        'sec_b_marker_score',
        'sec_a_moderator_score',
        'sec_b_moderator_score',
        'moderator_score',
        'marker_score'
    ];

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function level()
    {
        return $this->belongsTo(Level::class);
    }

    public function application()
    {
        return $this->belongsTo(Application::class);
    }
    public function submission()
    {
        return $this->belongsTo(Submission::class);
    }

    public function secAMarker()
    {
        return $this->belongsTo(User::class, 'sec_a_marker_id');
    }

    public function secBMarker()
    {
        return $this->belongsTo(User::class, 'sec_b_marker_id');
    }

    public function secAModerator()
    {
        return $this->belongsTo(User::class, 'sec_a_moderator_id');
    }

    public function secBModerator()
    {
        return $this->belongsTo(User::class, 'sec_b_moderator_id');
    }

    // Computed attributes
    public function getSecAMarkerScoreAttribute()
    {
        return $this->sec_a_marker_que_one + $this->sec_a_marker_que_two;
    }

    public function getSecBMarkerScoreAttribute()
    {
        return $this->sec_b_marker_que_one + $this->sec_b_marker_que_two;
    }

    public function getModeratorScoreAttribute()
    {
        return $this->sec_a_moderator_que_one + $this->sec_a_moderator_que_two +
               $this->sec_b_moderator_que_one + $this->sec_b_moderator_que_two;
    }

    public function getSecAModeratorScoreAttribute()
    {
        return $this->sec_a_moderator_que_one + $this->sec_a_moderator_que_two;
    }

    public function getSecBModeratorScoreAttribute()
    {
        return $this->sec_b_moderator_que_one + $this->sec_b_moderator_que_two;
    }

    public function getMarkerScoreAttribute()
    {
        return $this->sec_a_marker_score + $this->sec_b_marker_score;
    }
}
