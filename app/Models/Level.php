<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Level extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function courses()
    {
        return $this->hasMany(Course::class);
    }

    public function examResults()
    {
        return $this->hasMany(ExamResult::class);
    }

    public function applications()
    {
        return $this->hasMany(Application::class);
    }
}
