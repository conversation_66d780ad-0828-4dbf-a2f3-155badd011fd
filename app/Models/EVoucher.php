<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EVoucher extends Model
{
    use HasFactory, Uuids;

    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'voucher_code',
        'email',
        'phone',
        'first_name',
        'last_name',
        'amount',
        'charges',
        'status',
        'application_id',
        'payment_reference',
        'paid_at',
        'redeemed_at',
        'redeemed_by'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'charges' => 'decimal:2',
        'paid_at' => 'datetime',
        'redeemed_at' => 'datetime',
    ];

    /**
     * Generate a unique voucher code
     */
    public static function generateVoucherCode(): string
    {
        do {
            $code = 'EV' . strtoupper(substr(bin2hex(random_bytes(4)), 0, 8));
        } while (self::where('voucher_code', $code)->exists());

        return $code;
    }

    /**
     * Check if voucher is valid for redemption
     */
    public function isRedeemable(): bool
    {
        return $this->status === 'paid' && 
               $this->paid_at !== null && 
               $this->redeemed_at === null;
    }

    /**
     * Mark voucher as redeemed
     */
    public function redeem(string $credId): bool
    {
        if (!$this->isRedeemable()) {
            return false;
        }

        $this->update([
            'status' => 'redeemed',
            'redeemed_at' => now(),
            'redeemed_by' => $credId
        ]);

        return true;
    }

    /**
     * Get the application this voucher belongs to
     */
    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    /**
     * Get the credential that redeemed this voucher
     */
    public function redeemedBy()
    {
        return $this->belongsTo(Cred::class, 'redeemed_by', 'id');
    }

    /**
     * Scope for paid vouchers
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope for redeemable vouchers
     */
    public function scopeRedeemable($query)
    {
        return $query->where('status', 'paid')
                    ->whereNotNull('paid_at')
                    ->whereNull('redeemed_at');
    }

    /**
     * Scope for redeemed vouchers
     */
    public function scopeRedeemed($query)
    {
        return $query->where('status', 'redeemed');
    }
}
