<?php

namespace App\Models;

use App\Enums\EntranceSection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Application extends Model
{
    use HasFactory, OrderTrait;

    protected $guarded = [];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'exam_start_date' => 'date',
        'exam_end_date' => 'date',
        'amount' => 'decimal:2',
        'voucher_amount' => 'decimal:2',
        'id' => 'string'
    ];

    /**
     * Check if this application supports vouchers (entrance applications only)
     */
    public function supportsVouchers(): bool
    {
        return $this->transaction_id == 1;
    }

    /**
     * Get the voucher amount for this application
     */
    public function getVoucherAmount(): ?float
    {
        return $this->supportsVouchers() ? $this->voucher_amount : null;
    }

    protected $keyType = 'string';

    public function submissions()
    {
        return $this->hasMany(Submission::class);
    }

    public function candidates()
    {
        return $this->submissions()->where('status', 1);
    }

    public function scripts()
    {
        return $this->hasMany(Script::class);
    }

    public function candidateResults(): HasMany
    {
        return $this->scripts()->where('student_id', $this->student_id);
    }

    public function hasPendingSubmissions()
    {
        return $this->submissions()->whereNull('status')->exists();
    }

    public function level()
    {
        return $this->belongsTo(Level::class);
    }

    public function transaction()
    {
        return $this->belongsTo(Transaction::class);
    }

    public function courses()
    {
        return $this->hasMany(Course::class, 'level_id', 'level_id');
    }

    public function examResults()
    {
        return $this->hasMany(ExamResult::class);
    }

    public function results()
    {
        return $this->hasMany(Result::class);
    }

    public function compositeRefactored()
    {
        $results = $this->calculateScores();
        $withCourses = $this->prepareResults($results);

        return $this->mapResults($withCourses);
    }

    public function mergedComposite()
    {
        $results = $this->calculateScores();
        $withCourses = $this->prepareMergedResults($results);

        return $this->mapResults($withCourses);
    }

    private function calculateScores()
    {
        $results = [];

        foreach ($this->results as $result) {
            if ($result->is_exempted) {
                $results[$result->student_id][$result->course->name] = "EXEMPTED";
            } elseif (!$result->wrote_exam) {
                $results[$result->student_id][$result->course->name] = "ABSENT";
            } else {
                $results[$result->student_id][$result->course->name] = !is_null($result->iec_score) ? $result->iec_score : $result->totalScore();
            }
        }

        return $results;
    }

    private function prepareMergedResults($results)
    {
        $withCourses = [];
        $courses = $this->courses()->pluck('name');

        foreach ($results as $student_id => $scores) {
            $failCount = 0;

            // Fetch all of a student's previous results
            $user = User::where('student_id', $student_id)->first();
            $previousResults = $user?->results?->where('level_id', $this->level_id);
            if (is_null($previousResults)) continue;

            // Merge the scores for every course
            $mergedScores = array_fill_keys($courses->all(), 0);
            foreach ($previousResults as $previousResult) {
                $courseName = $previousResult->course->name;
                $score = $previousResult->score ?? 0;
                $mergedScores[$courseName] = max($mergedScores[$courseName] ?? 0, $score);
            }

            // Merge the current scores with the previous scores
            foreach ($scores as $courseName => $score) {
                $mergedScores[$courseName] = ($score === "EXEMPTED" || $score === "ABSENT") ? $score : max($mergedScores[$courseName] ?? 0, $score);
            }

            // Add the merged scores to the $withCourses array
            $withCourses[$student_id] = $mergedScores;

            // Count the number of fails
            foreach ($mergedScores as $score) {
                if ($score === "ABSENT" || (is_numeric($score) && $score < 50)) {
                    $failCount++;
                }
            }

            // Add the fail count to the $withCourses array
            $withCourses[$student_id]["failCount"] = $failCount;
            $remark = getRemark($this->name, $failCount, count($mergedScores));
            $withCourses[$student_id]["remarks"] = $remark;
        }

        return $withCourses;
    }

    private function prepareResults($results)
    {
        $withCourses = [];
        $courses = $this->courses()->pluck('name');

        foreach ($results as $student_id => $scores) {
            $failCount = 0;
            foreach ($scores as $score) {
                if ($score === "ABSENT" || (is_numeric($score) && $score < 50)) {
                    $failCount++;
                }
            }

            foreach ($courses as $course) {
                $withCourses[$student_id][] = $scores[$course] ?? 0;
            }

            // $remark = getRemark($this->name, $failCount, count($scores));
            // $withCourses[$student_id]["remarks"] = $remark;
        }

        return $withCourses;
    }

    private function mapResults($withCourses)
    {
        $mappedResults[] = ['id' => '', ...$this->courses()->pluck('name'), "FAIL COUNT", "REMARKS"];

        foreach ($withCourses as $id => $record) {
            $mappedResults[] = ['id' => $id, ...$record];
        }

        return $mappedResults;
    }

    public function composite()
    {
        $results = [];

        foreach ($this->results as $result) {
            if ($result->is_exempted) {
                $results[$result->student_id][$result->course->name] = "EXEMPTED";
            } else {
                $results[$result->student_id][$result->course->name] = !$result->wrote_exam ? "ABSENT" : $result->iec_score;
            }
        }

        $withCourses = [];
        $courses = $this->courses()->pluck('name');

        // Check if the application name contains 'Post-Call' or 'PLC 2'
        $isPostCall = str_contains($this->name, 'Post-Call');
        $isPLC2 = str_contains($this->name, 'PLC 2');

        foreach ($results as $student_id => $scores) {
            $failCount = 0;
            foreach ($scores as $score) {
                if ($score === "ABSENT" || (is_numeric($score) && $score < 50)) {
                    $failCount++;
                }
            }

            foreach ($courses as $course) {
                $withCourses[$student_id][] = $scores[$course] ?? 0;
            }

            $remark = getRemark($this->name, $failCount, count($scores));
            $withCourses[$student_id]["remarks"] = $remark;
        }

        $mappedResults[] = ['id' => '', ...$courses, "REMARKS"];

        foreach ($withCourses as $id => $record) {
            $mappedResults[] = ['id' => $id, ...$record];
        }

        return $mappedResults;
    }

    public function sectionComposite()
    {
        $results = [];

        foreach ($this->scripts as $script) {
            $studentId = $script->student_id;
            $section = $script->section;

            if (!isset($results[$studentId])) {
                $results[$studentId] = [
                    'student_id' => $studentId,
                    'section_a_total' => 0, // Total for Section A
                    'section_b_total' => 0, // Total for Section B
                    'total' => 0, // Total for both sections
                    'wrote_exam' => $script->attendance_display,
                    'remarks' => ''
                ];
            }

            if ($section === 'A') {
                $results[$studentId]['section_a_total'] += ($script->marker_que_one ?? 0) + ($script->marker_que_two ?? 0);
            } elseif ($section === 'B') {
                $results[$studentId]['section_b_total'] += ($script->marker_que_one ?? 0) + ($script->marker_que_two ?? 0);
            }
        }

        // Generate total and remarks based on total scores
        foreach ($results as &$record) {
            $wroteExam = $record['wrote_exam'];
            $record['total'] = $wroteExam === 'PRESENT' ? $record['section_a_total'] + $record['section_b_total'] : 'ABSENT';
            unset($record['wrote_exam']);
            if ($record['total'] === 'ABSENT') {
                $record['remarks'] = 'FAIL';
            } elseif ($record['total'] < 50) {
                $record['remarks'] = 'FAIL';
            } else {
                $record['remarks'] = 'PASS';
            }
        }

        ksort($results);

        // Add column headers
        $headers = ['Candidate ID', 'Section A (Q1 & Q2)', 'Section B (Q1 & Q2)', 'Final Score', 'Remarks'];
        return array_merge([$headers], array_values($results));
    }

    public function entrance_results()
    {
        return $this->hasMany(EntranceResult::class);
    }

    public function users()
    {
        return $this->with('results.user')->get()->flatMap(function ($application) {
            return $application->results->pluck('user')->unique('id');
        });
    }

    public function isActive()
    {
        return now()->between($this->start_date, $this->end_date);
    }

    public static function active()
    {
        return self::all()->filter(function ($application) {
            return $application->isActive();
        });
    }

    public static function examActive()
    {
         return static::where('exam_end_date', '>=', now())->get();
    }

    public function audits()
    {
        return $this->hasMany(Audit::class);
    }

    public function pwds()
    {
        return $this->hasMany(Pwd::class);
    }

    public function expired()
    {
        return (bool)now()->subDay(1)->greaterThan($this->end_date);
    }
}
