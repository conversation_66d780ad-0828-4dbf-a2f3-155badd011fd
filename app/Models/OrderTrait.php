<?php

namespace App\Models;

trait OrderTrait
{
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function createOrder($user_id, $description = '', $qty = 1, $meta = null)
    {
        return $this->orders()->firstOrCreate([
            'user_id' => $user_id,
            'transaction_id' => $this->transaction->id,
            'unit_price' => $this->amount,
            'description' => $description,
            'meta' => $meta,
            'qty' => $qty,
        ]);
    }
}
