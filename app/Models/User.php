<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Althinect\FilamentSpatieRolesPermissions\Concerns\HasSuperAdmin;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements FilamentUser
{
    use HasApiTokens, HasFactory, Notifiable, Uuids, HasRoles, HasSuperAdmin, SoftDeletes;

    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'section',
        'student_id',
        'start_level',
        'password',
        'ghana_card',
        'venue_id',
        'is_eligible',
        'eligibility_notes',
        'eligibility_date',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'id' => 'string',
        'is_eligible' => 'boolean',
        'eligibility_date' => 'datetime',
    ];

    protected $keyType = 'string';

    public function venue()
    {
        return $this->belongsTo(Venue::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function paidOrders()
    {
        return $this->orders()->where('status', 1);
    }

    public function paidApplication($application)
    {
        return $this->orders()->where('application_id', $application->id)->first();
    }

    public function applicationOrder($application)
    {
        return $this->orders()->where('application_id', $application->id)->first();
    }

    public function examResults()
    {
        return $this->hasMany(Result::class, 'student_id', 'student_id');
    }

    public function results()
    {
        return $this->hasMany(Result::class, 'student_id', 'student_id');
    }

    public function submissions()
    {
        return $this->hasMany(Submission::class, 'student_id', 'student_id');
    }

    public function groupedResults($options = [])
    {
        $query = $this->examResults();
        if (array_key_exists('published', $options)) $query = $query->isPublished();
        if (array_key_exists('level', $options)) $query = $query->where('level_id', $options['level']);
        if (array_key_exists('failed', $options)) $query = $query->where('iec_score', '<', 50);
        if (array_key_exists('remark', $options)) $query = $query->whereBetween('iec_score', [40, 100]);
        if (array_key_exists('retally', $options)) $query = $query;
        if (array_key_exists('dateFrom', $options)) $query = $query->whereDate('updated_at', '>=', $options['dateFrom']);
        return $query->with('course')->get()->groupBy('application_id')->all();
    }

    public function getNameAttribute()
    {
        return "$this->full_name";
    }

//    public function courses()
//    {
//        return $this->hasManyThrough(Course::class, ExamResult::class, 'student_id', 'course_id');
//    }

    public function assignedResults()
    {
        $role = $this->getRoleNames()->first() ?? 'marker';
        if ($role) return $this->hasMany(Result::class, $role . '_id');
    }

    public function getFirstRole()
    {
        return $this->getRoleNames()->first();
    }

    public function getCurrentLevelAttribute()
    {
        return getNextLevel($this->start_level, $this->examResults());
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return $this->hasAnyRole('marker', 'moderator', 'iec', 'admin', 'accountant', 'Super Admin', 'gsl', 'glc');
    }

    public function scopeStudents(Builder $query): void
    {
        $query->whereDoesntHave('roles');
    }

    /**
     * Check if the student is a postcall student
     *
     * @return bool
     */
    public function isPostCallStudent(): bool
    {
        return $this->start_level == 3;
    }

    public function scopeAdmins(Builder $query): void
    {
        $query->whereHas('roles');
    }

    public function scopeMarkers(Builder $query): void
{
    $query->whereHas('roles', function ($query) {
        $query->where('name', 'marker');
    });
}

public function scopeModerators(Builder $query): void
{
    $query->whereHas('roles', function ($query) {
        $query->where('name', 'moderator');
    });
}

    public function assignedCourses($role, $application_id)
    {
        return Course::join('results', 'courses.id', '=', 'results.course_id')
            ->where('results.application_id', $application_id)
            ->where('results.' . $role . '_id', $this->id)
            ->pluck('courses.name', 'courses.id');
    }

    public function hasUnpublisedResults($course_id)
    {
        return $this->examResults()->where('course_id', $course_id)->where('isPublished', 0)->exists();
    }

    public function hasSection($section)
    {
        return $this->section === $section;
    }

    public function canImpersonate()
    {
        return $this->hasAnyRole('Super Admin') || $this->email === '<EMAIL>';
    }
}
