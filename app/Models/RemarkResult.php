<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RemarkResult extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function result()
    {
        return $this->belongsTo(Result::class);
    }

    public function course()
    {
        return $this->result->course;
    }

    public function marker()
    {
        return $this->belongsTo(User::class, 'marker_id');
    }

    public function moderator()
    {
        return $this->belongsTo(User::class, 'moderator_id');
    }
}
