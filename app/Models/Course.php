<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Course extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function level()
    {
        return $this->belongsTo(Level::class);
    }

    public function didPass($score)
    {
        return $score >= ($this->pass_mark ?? 50);
    }

    public function remarksText($score)
    {
        return $this->didPass($score) ? 'Passed' : 'Failed';
    }

    public function results()
    {
        return $this->hasMany(Result::class);
    }

    public function sub_courses()
    {
        return $this->hasMany(SubCourse::class);
    }
}
