<?php

namespace App\Models;

use App\Repository\CredRepository;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class Cred extends Authenticatable
{
    use HasFactory, Uuids, Notifiable;

    public $incrementing = false;

    protected $hidden = [
        'pin',
    ];

    protected $guarded = ['pin', 'serial', 'status'];

    public function getAuthPassword()
    {
        return $this->pin;
    }

    public function submission()
    {
        return $this->hasOne(Submission::class);
    }

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    public function transaction()
    {
        return $this->belongsTo(Application::class);
    }

    public function order()
    {
        return $this->hasOne(Order::class, 'user_id', 'id');
    }

    public function resetPin()
    {
        $credRepo = new CredRepository();
        $credRepo->resetPin($this);
    }

    public function createOrder()
    {
        $application = $this->application;

        return $this->order()->firstOrCreate([
            'application_id' => $application->id,
            'transaction_id' => $application->transaction->id,
            'unit_price' => $application->amount,
            'description' => $application->name,
            'qty' => 1,
        ]);
    }
}
