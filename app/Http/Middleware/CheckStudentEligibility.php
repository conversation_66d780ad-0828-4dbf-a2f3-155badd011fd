<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckStudentEligibility
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check() && !Auth::user()->is_eligible) {
            // If the user is logged in but not eligible
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'You are not eligible to register for exams.',
                    'status' => 'error'
                ], 403);
            }
            
            return redirect()->route('student.ineligible')->with('error', 'You are not eligible to register for exams.');
        }

        return $next($request);
    }
}
