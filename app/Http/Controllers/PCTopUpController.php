<?php

namespace App\Http\Controllers;

use App\Models\Application;
use App\Models\Order;
use App\Models\User;
use App\Repository\PaymentRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PCTopUpController extends Controller
{
    public function index()
    {
        // Hard-coded amount for the top-up
        $topupAmount = 3500;
        
        return view('pc-topup', [
            'amount' => $topupAmount,
            'charges' => $topupAmount * charges(),
            'total' => amountWithCharges($topupAmount)
        ]);
    }
    
    public function process(Request $request)
    {
        $user = Auth::user();
        $topupAmount = 3500;
        
        $pcApplication = Application::where('name', '2025 POST CALL EXAMINATION')->first();
        
        if (!$pcApplication) {
            return back()->with('error', 'PC application not found');
        }
        
        $paymentUrl = (new PaymentRepository())->getPaymentLink($user, amountWithCharges($topupAmount), [
            'application_id' => $pcApplication->id,
            'user_id' => $user->id,
            'student_id' => $user->student_id,
            'charges' => $topupAmount * charges(),
            'is_topup' => true
        ]);

        if (!$paymentUrl) {
            return back()->with('error', 'Payment initialization failed');
        }

        return redirect($paymentUrl);
    }
}