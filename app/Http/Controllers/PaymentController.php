<?php

namespace App\Http\Controllers;

use App\Models\Application;
use App\Models\Cred;
use App\Models\Order;
use App\Models\Remarking;
use App\Models\RemarkResult;
use App\Models\Result;
use App\Models\RetallyResult;
use App\Models\User;
use App\Repository\CredRepository;
use App\Repository\PaymentRepository;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    public $reference = null;

    public function callback(Request $request)
    {
        $verifiedTransaction = (new PaymentRepository())->verifyPayment($request->reference);
        if (!$verifiedTransaction) return back()->with('error', 'Payment unsuccessful');

        $this->reference = $request->reference;

        $metadata = (object)$verifiedTransaction['data']['metadata'];

        $application = Application::find($metadata->application_id);

        // Handle voucher-related payments
        if (isset($metadata->transaction_type)) {
            if ($metadata->transaction_type === 'voucher_purchase') {
                return $this->handleVoucherPurchase($metadata);
            }
            if ($metadata->transaction_type === 'exam_fee_with_voucher') {
                return $this->handleExamFeeWithVoucher($metadata);
            }
        }

        if (($metadata->transaction ?? null) === 'referral') {
            $application = Application::find($metadata->transaction_id);
            $this->referral($application, $metadata);
        }
        if ($application->transaction->name === 'remarking') $this->remarking($application, $metadata);
        if ($application->transaction->name === 'retallying') $this->retallying($application, $metadata);
        if ($application->transaction->name === 'exam') $this->exam($application, $metadata);
        if ($application->transaction->name === 'entrance') return $this->entrance($application, $metadata);

        return redirect('/')->with('success', 'Payment successful');
    }

    // Check if student is on scholarship for the given application
    protected function isOnScholarship($userId, $applicationId)
    {
        return Order::where('user_id', $userId)
            ->where('application_id', $applicationId)
            ->where(function($query) {
                $query->where('reference', 'scholarship')
                      ->orWhere('reference', 'like', 'scholarship:%');
            })
            ->where('status', 1)
            ->exists();
    }

    // Check if student is on a specific scholarship type
    protected function isOnScholarshipType($userId, $applicationId, $type)
    {
        return Order::where('user_id', $userId)
            ->where('application_id', $applicationId)
            ->where('reference', 'scholarship:' . $type)
            ->where('status', 1)
            ->exists();
    }

    // Check if student is on scholarship by student_id
    protected function isStudentOnScholarship($studentId, $applicationId)
    {
        $user = User::where('student_id', $studentId)->first();
        if (!$user) return false;
        
        return $this->isOnScholarship($user->id, $applicationId);
    }

    public function retallying($application, $metadata)
    {
        $order = $application->createOrder(
            $metadata->user_id,
            remarkingDescription($metadata->results),
            count($metadata->results),
            ['results' => $metadata->results]
        );

        foreach ($metadata->results as $result_id) {
            RetallyResult::firstOrCreate([
                'result_id' => $result_id
            ]);
        }

        $order->update(['charges' => $metadata->charges, 'reference' => $this->reference]);

        return $order;
    }

    public function remarking($application, $metadata)
    {
        $order = $application->createOrder(
            $metadata->user_id,
            remarkingDescription($metadata->results),
            count($metadata->results),
            ['results' => $metadata->results]
        );

        foreach ($metadata->results as $result_id) {
            RemarkResult::firstOrCreate([
                'result_id' => $result_id
            ]);
        }

        $order->update(['charges' => $metadata->charges, 'reference' => $this->reference]);

        return $order;
    }

    public function referral($application, $metadata)
    {
        $exam = Application::find($metadata->application_id);

        $order = $application->createOrder(
            $metadata->user_id,
            referralDescription($metadata->courses, $exam->name),
            count($metadata->courses),
            ['exam' => $exam->name, 'exam_id' => $exam->id, 'courses' => $metadata->courses]
        );

        foreach ($metadata->courses as $course_id) {
            Result::firstOrCreate([
                'student_id' => $metadata->student_id,
                'application_id' => $exam->id,
                'level_id' => $exam->level_id,
                'course_id' => $course_id
            ]);
        }

        $order->update(['reference' => $this->reference]);

        return $order;
    }

    public function exam($application, $metadata)
    {
        // Check if student is on scholarship for this application
        if ($this->isOnScholarship($metadata->user_id, $application->id)) {
            // Student is already on scholarship, no need to create a new order
            // Just ensure results are created
            foreach ($application->courses as $course) {
                Result::firstOrCreate([
                    'student_id' => $metadata->student_id,
                    'application_id' => $application->id,
                    'level_id' => $application->level_id,
                    'course_id' => $course->id
                ]);
            }
            
            // Return the existing scholarship order
            return Order::where('user_id', $metadata->user_id)
                ->where('application_id', $application->id)
                ->where('reference', 'scholarship')
                ->first();
        }
        
        // Regular payment flow
        $order = $application->createOrder(
            $metadata->user_id,
            $application->name
        );

        foreach ($application->courses as $course) {
            Result::firstOrCreate([
                'student_id' => $metadata->student_id,
                'application_id' => $application->id,
                'level_id' => $application->level_id,
                'course_id' => $course->id
            ]);
        }

        $order->update(['charges' => $metadata->charges, 'reference' => $this->reference]);

        return $order;
    }

    public function entrance($application, $metadata)
    {
        $cred = Cred::create((array)$metadata);
        $order = $cred->createOrder();

        $order->update(['charges' => $metadata->charges, 'reference' => $this->reference]);

        $credRepo = new CredRepository();
        $credRepo->createPin($order->user);

        return redirect()->route('entrance.registration', ['step' => 3, 'message' => 'entrance']);
    }

    /**
     * Handle voucher purchase payment
     */
    private function handleVoucherPurchase($metadata)
    {
        $voucher = \App\Models\EVoucher::find($metadata->voucher_id);

        if (!$voucher) {
            return redirect()->route('entrance.registration')->with('error', 'Voucher not found');
        }

        $voucherRepo = new \App\Repository\VoucherRepository();
        $success = $voucherRepo->processVoucherPurchase($voucher, $this->reference);

        if ($success) {
            return redirect()->route('entrance.registration', ['step' => 4, 'message' => 'voucher_purchased']);
        }

        return redirect()->route('entrance.registration')->with('error', 'Failed to process voucher purchase');
    }

    /**
     * Handle application fee payment with voucher
     */
    private function handleExamFeeWithVoucher($metadata)
    {
        $voucher = \App\Models\EVoucher::find($metadata->voucher_id);

        if (!$voucher) {
            return redirect()->route('entrance.registration')->with('error', 'Voucher not found');
        }

        $voucherRepo = new \App\Repository\VoucherRepository();
        $cred = $voucherRepo->processExamFeePayment($voucher, $this->reference);

        if ($cred) {
            return redirect()->route('entrance.registration', ['step' => 3, 'message' => 'entrance']);
        }

        return redirect()->route('entrance.registration')->with('error', 'Failed to process application fee payment');
    }
}
