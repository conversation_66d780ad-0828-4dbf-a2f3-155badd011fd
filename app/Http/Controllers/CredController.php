<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CredController extends Controller
{
    public function register()
    {
        /*
         * user selects application, enters email and phone
         * option 1: system redirects to PG and passes email, phone, application_id to PG
         * ---- on successful payment, system adds new credentials and sends an email
         * option 2: system creates credential and passes cred_id to payment gateway
         * ---- on successful payment, system updates payment table
         */

        // register by providing email, phone, application_id
        // redirect to payment page
    }

    public function login(Request $request)
    {
        if (Auth::guard('cred')->attempt(['serial' => $request->serial, 'password' => $request->pin])) {
            return "Success";
        }
        return "failure";
    }

    public function logout()
    {
        Auth::guard('cred')->logout();
        return redirect()->route('entrance.registration');
    }
}
