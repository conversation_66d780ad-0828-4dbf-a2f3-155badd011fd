<?php

namespace App\Http\Controllers;

use App\Models\Application;
use App\Models\Course;
use App\Models\ExamResult;
use App\Models\Level;
use App\Models\Transaction;
use App\Models\Venue;
use App\Repository\PaymentRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ExamController extends Controller
{
    public function index()
    {
        //
    }

    public function create()
    {
        //
    }

    public function store(Request $request)
    {
        //
    }

    public function results(Level $level)
    {
        $user = request()->user();
        $results = $user->examResults()->isPublished()->where('level_id', $level->id)->with('course')->get()->groupBy('application_id');
        return view('results', compact('results', 'level', 'user'));
    }

    public function remark()
    {
        if (!$remarkApplication = Application::whereHas('transaction', function ($query) {
            $query->where('name', 'remarking');
        })->first()) return redirect()->back();

        $user = request()->user();
        $applications = $user->groupedResults(['dateFrom' => remarkDate(), 'remark' => true, 'published' => true]);

        return view('remark', compact('applications', 'user', 'remarkApplication'));
    }

    public function retally()
    {
        if (!$retallyApplication = Application::whereHas('transaction', function ($query) {
            $query->where('name', 'retallying');
        })->first()) return redirect()->back();

        $user = request()->user();
        $applications = $user->groupedResults(['dateFrom' => remarkDate(), 'retally' => true, 'published' => true]);

        return view('retally', compact('applications', 'user', 'retallyApplication'));
    }

    public function submitRemark()
    {
        return view('remark');
    }

    public function submitRetally()
    {
        return view('retally');
    }

    public function retake(Application $application)
    {
        $user = request()->user();

        $transaction = (new Transaction())->currentApplication('referral');

        // Determine course IDs that have been passed by the user at this level
        $passed_course_ids = $user->examResults()
            ->where('level_id', $application->level_id)
            ->where('total_score', '>=', 50)
            ->pluck('course_id') // Get the course_id from exam_results table
            ->unique();          // Ensure we have a unique list of passed course IDs

        // Get referrals: failing scores for courses that have not been passed at this level
        $referrals = $user->examResults()
            ->where('level_id', $application->level_id)
            ->where('total_score', '<', 50)
            ->whereNotIn('course_id', $passed_course_ids) // Exclude courses that have already been passed
            ->with('course') // Eager load course for pluck('course.id') and potential use in view
            ->orderBy('total_score') // Order by score ascending
            ->get() // Retrieve the collection of ExamResult models
            ->pluck('total_score', 'course.id'); // Pluck total_score, keyed by course.id.
                                                 // For a course with multiple failing scores, this takes the highest one.
        $referralRequests = ($user->orders()->where('transaction_id', 3)->get());

        return view('retake', compact('application', 'user', 'transaction', 'referrals', 'referralRequests'));
    }

    public function show(Application $application)
    {
        // TODO: prevent viewing or applying for unavailable Exam
        $user = request()->user();
        $paidApplication = $user->paidApplication($application);
        $courses = Course::where('level_id', $application->level_id)->get();
        $studentStatus = (object) studentStatus($user);

        $centres = Venue::all();
        return view('exam.register', compact('application', 'user', 'centres', 'paidApplication', 'courses', 'studentStatus'));
    }

    public function register(Application $application)
    {
        $user = request()->user();

        $paymentUrl = (new PaymentRepository())->getPaymentLink($user, amountWithCharges($application->amount), [
            'application_id' => $application->id,
            'user_id' => $user->id,
            'student_id' => $user->student_id,
            'charges' => $application->amount * charges()
        ]);

        if (!$paymentUrl) back()->with('error', 'Payment unsuccessful');

        return redirect($paymentUrl);
    }

    public function edit(ExamResult $examResult)
    {
        //
    }

    public function update(Request $request, ExamResult $examResult)
    {
        //
    }

    public function destroy(ExamResult $examResult)
    {
        //
    }
}
