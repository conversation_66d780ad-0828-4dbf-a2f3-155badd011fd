<?php

namespace App\Http\Controllers;

use App\Models\EVoucher;
use App\Repository\VoucherRepository;
use Illuminate\Http\Request;

class VoucherController extends Controller
{
    private VoucherRepository $voucherRepo;

    public function __construct(VoucherRepository $voucherRepo)
    {
        $this->voucherRepo = $voucherRepo;
    }

    /**
     * Display voucher dashboard
     */
    public function index()
    {
        $stats = $this->voucherRepo->getVoucherStats();
        
        $recentVouchers = EVoucher::with('application')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('admin.vouchers.index', compact('stats', 'recentVouchers'));
    }

    /**
     * Display all vouchers with filtering
     */
    public function list(Request $request)
    {
        $query = EVoucher::with('application', 'redeemedBy');

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search by email or voucher code
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('email', 'like', "%{$search}%")
                  ->orWhere('voucher_code', 'like', "%{$search}%")
                  ->orWhere('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%");
            });
        }

        $vouchers = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.vouchers.list', compact('vouchers'));
    }

    /**
     * Show voucher details
     */
    public function show(EVoucher $voucher)
    {
        $voucher->load('application', 'redeemedBy');
        return view('admin.vouchers.show', compact('voucher'));
    }

    /**
     * Validate a voucher code (for admin use)
     */
    public function validateVoucher(Request $request)
    {
        $request->validate([
            'voucher_code' => 'required|string|max:20'
        ]);

        $voucher = $this->voucherRepo->validateVoucherCode($request->voucher_code);

        if ($voucher) {
            return response()->json([
                'valid' => true,
                'voucher' => $voucher->load('application')
            ]);
        }

        return response()->json([
            'valid' => false,
            'message' => 'Invalid or already used voucher code'
        ]);
    }

    /**
     * Export vouchers to CSV
     */
    public function export(Request $request)
    {
        $query = EVoucher::with('application', 'redeemedBy');

        // Apply same filters as list method
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $vouchers = $query->orderBy('created_at', 'desc')->get();

        $filename = 'vouchers_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($vouchers) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Voucher Code',
                'Full Name',
                'Email',
                'Phone',
                'Amount',
                'Status',
                'Created At',
                'Paid At',
                'Redeemed At',
                'Application'
            ]);

            // CSV data
            foreach ($vouchers as $voucher) {
                fputcsv($file, [
                    $voucher->voucher_code,
                    $voucher->full_name,
                    $voucher->email,
                    $voucher->phone,
                    $voucher->amount,
                    $voucher->status,
                    $voucher->created_at->format('Y-m-d H:i:s'),
                    $voucher->paid_at?->format('Y-m-d H:i:s') ?? '',
                    $voucher->redeemed_at?->format('Y-m-d H:i:s') ?? '',
                    $voucher->application->name ?? ''
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
