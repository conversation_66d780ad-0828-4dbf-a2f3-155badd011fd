<?php

namespace App\Http\Controllers;

use App\Models\Cred;
use App\Models\Order;
use App\Repository\CredRepository;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    public function pay(Order $order)
    {
        // if successful
        $order->status = 1;
        $order->save();

        if ($order->transaction->name == 'entrance') {
            $credRepo = new CredRepository();
            $credRepo->createPin($order->user);

            return redirect()->route('entrance.registration', ['step' => 3, 'message' => 'entrance']);
        }

        return redirect()->route('orders.show', $order);
    }

    public function index()
    {
        $user = auth()->user();
        return view('orders.index', compact('user'));
    }

    public function show(Order $order)
    {
        return view('orders.show', compact('order'));
    }
}
