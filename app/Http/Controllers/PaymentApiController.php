<?php

namespace App\Http\Controllers;

use App\Models\Application;
use App\Models\Cred;
use App\Models\Order;
use App\Models\Remarking;
use App\Models\RemarkResult;
use App\Models\Result;
use App\Repository\CredRepository;
use App\Repository\PaymentRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use League\Csv\Reader;

class PaymentApiController extends Controller
{
    public $reference;

    public function callback(Request $request)
    {
        if ($request->event !== "charge.success") return response()->json(["message" => "unacceptable request"]);

        $this->reference = $request->isJson() ? ($request->data["reference"] ?? "") : $request->reference;

        if (Order::where('reference', $this->reference)->exists()) return response()->json(["message" => "unacceptable request (order exists)"]);

        try {
            $response = $this->processPayment($this->reference, $request);
            return response()->json($response, 200);
        } catch (\Exception $e) {
            return response()->json(["message" => $e->getMessage()], 400);
        }
    }

    private function processPayment($reference, $request = null)
    {
        $verifiedTransaction = (new PaymentRepository())->verifyPayment($reference);

        if (!$verifiedTransaction) return ['message' => 'received'];
        Log::info('verifiedTransaction', [$verifiedTransaction]);

        $metadata = (object)$verifiedTransaction['data']['metadata'];
        Log::info('metadata', [$metadata]);

        $application = Application::find($metadata->application_id);
        if (!$application) {
            throw new \Exception('Application not found');
        }

        // Handle the PC top-up payment
        if (isset($metadata->is_topup) && $metadata->is_topup) {
            $this->handlePCTopUp($application, $metadata);
            return ['message' => 'success'];
        }

        // Handle voucher-related payments
        if (isset($metadata->transaction_type)) {
            if ($metadata->transaction_type === 'voucher_purchase') {
                return $this->handleVoucherPurchase($metadata, $reference);
            }
            if ($metadata->transaction_type === 'exam_fee_with_voucher') {
                return $this->handleExamFeeWithVoucher($metadata, $reference);
            }
        }

        if (($metadata->transaction ?? null) === 'referral') {
            $application = Application::find($metadata->transaction_id);
            return $this->referral($application, $metadata, $reference);
        }
        if ($application->transaction->name === 'remarking') return $this->remarking($application, $metadata, $reference);
        if ($application->transaction->name === 'exam') return $this->exam($application, $metadata, $reference);
        if ($application->transaction->name === 'entrance') return $this->entrance($application, $metadata, $reference);
        // TODO find out why referral transaction is not in this list
    }

    private function processPayments($references)
    {

        $results = [];

        foreach ($references as $reference) {
            if (!is_string($reference)) {
                $results[] = [
                    'reference' => $reference,
                    'status' => 'error',
                    'message' => 'Invalid input: reference must be a string'
                ];
                continue;
            }

            if (Order::where('reference', $reference)->exists()) {
                $results[] = [
                    'reference' => $reference,
                    'status' => 'error',
                    'message' => 'Order already exists'
                ];
                continue;
            }

            try {
                $response = $this->processPayment($reference, null);
                Log::info('response in loop', [$response]);

                $results[] = [
                    'reference' => $reference,
                    'status' => 'success',
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'reference' => $reference,
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    public function remarking($application, $metadata, $reference)
    {
        $order = $application->createOrder(
            $metadata->user_id,
            remarkingDescription($metadata->results),
            count($metadata->results),
            ['results' => $metadata->results]
        );

        foreach ($metadata->results as $result_id) {
            RemarkResult::firstOrCreate([
                'result_id' => $result_id
            ]);
        }

        $order->update(['charges' => $metadata->charges ?? 0, 'reference' => $reference]);
        return $order;
    }

    public function referral($application, $metadata, $reference)
    {
        $exam = Application::find($metadata->application_id);

        $order = $application->createOrder(
            $metadata->user_id,
            referralDescription($metadata->courses, $exam->name),
            count($metadata->courses),
            ['exam' => $exam->name, 'exam_id' => $exam->id, 'courses' => $metadata->courses]
        );

        foreach ($metadata->courses as $course_id) {
            Result::firstOrCreate([
                'student_id' => $metadata->student_id,
                'application_id' => $exam->id,
                'level_id' => $exam->level_id,
                'course_id' => $course_id
            ]);
        }

        $order->update(['charges' => $metadata->charges ?? 0, 'reference' => $reference]);
        return $order;
    }

    public function exam($application, $metadata, $reference)
    {
        $order = $application->createOrder(
            $metadata->user_id,
            $application->name
        );

        foreach ($application->courses as $course) {
            Result::firstOrCreate([
                'student_id' => $metadata->student_id,
                'application_id' => $application->id,
                'level_id' => $application->level_id,
                'course_id' => $course->id
            ]);
        }

        $order->update(['charges' => $metadata->charges ?? 0, 'reference' => $reference]);
        return $order;
    }

    public function entrance($application, $metadata, $reference)
    {
        $order = null;
        if (!Cred::where('email', $metadata->email)->exists()) {
            $cred = Cred::create((array)$metadata);
            $order = $cred->createOrder();

            $order->update(['charges' => $metadata->charges ?? 0, 'reference' => $reference]);

            $credRepo = new CredRepository();
            $credRepo->createPin($order->user);
        }
        return $order;
    }

    public function handlePCTopUp($application, $metadata)
    {
        // Create a special order for the top-up
        $order = $application->createOrder(
            $metadata->user_id,
            'PC Fee Top-up Payment (3500 GHC)',
            1,
            ['top_up' => true]
        );

        $order->update(['charges' => $metadata->charges ?? 0, 'reference' => $this->reference]);
        return $order;
    }

    /**
     * Handle voucher purchase payment via API
     */
    private function handleVoucherPurchase($metadata, $reference)
    {
        $voucher = \App\Models\EVoucher::find($metadata->voucher_id);

        if (!$voucher) {
            return ['message' => 'Voucher not found'];
        }

        $voucherRepo = new \App\Repository\VoucherRepository();
        $success = $voucherRepo->processVoucherPurchase($voucher, $reference);

        return ['message' => $success ? 'success' : 'failed'];
    }

    /**
     * Handle application fee payment with voucher via API
     */
    private function handleExamFeeWithVoucher($metadata, $reference)
    {
        $voucher = \App\Models\EVoucher::find($metadata->voucher_id);

        if (!$voucher) {
            return ['message' => 'Voucher not found'];
        }

        $voucherRepo = new \App\Repository\VoucherRepository();
        $cred = $voucherRepo->processExamFeePayment($voucher, $reference);

        return ['message' => $cred ? 'success' : 'failed'];
    }

    public function hookResponse()
    {
        return response()->json(["message" => "received"], 200);
    }

    public function verifiedHookResponse()
    {
        return response()->json(["message" => "success"], 200);
    }

    public function batchCallback(Request $request)
    {
        $references = $request->json('references');

        if (!is_array($references)) {
            return response()->json(['error' => 'Invalid input: references must be an array'], 400);
        }


        $results = $this->processPayments($references);

        return response()->json($results);
    }

    public function batchCallbackFromCsv(Request $request)
    {
        if (!$request->hasFile('csv_file')) {
            return response()->json(['error' => 'No CSV file provided'], 400);
        }

        $file = $request->file('csv_file');

        try {
            $csv = Reader::createFromPath($file->getPathname(), 'r');
            $csv->setHeaderOffset(0);

            $references = [];
            foreach ($csv as $record) {
                $references[] = $record['reference'];
            }

            Log::info('references check :', $references);
            $results = $this->processPayments($references);

            return response()->json($results);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Error parsing CSV file: ' . $e->getMessage()], 400);
        }
    }
}
