<?php

use App\Models\Application;
use App\Models\Course;
use App\Models\Level;
use App\Models\Result;
use App\Models\User;
use Illuminate\Support\Facades\Log;

define('DEFAULT_REMARK_PERIOD', 30);
define('DEFAULT_REMARK_AMOUNT', 1500);

function currentApplication($level = 4)
{
    return Application::whereDate('start_date', '<=', now())
        ->whereDate('end_date', '>=', now())
        ->where('level_id', $level)
        ->first();
}

function remarkApplicationActive()
{
    return Application::whereDate('start_date', '<=', now())
        ->whereDate('end_date', '>=', now())
        ->where('name', 'Remarking')
        ->exists();
}

function mainExamApplicationAvailable($level = 4)
{
    return Application::whereDate('start_date', '<=', now())
        ->where('transaction_id', '!=', 3) // not a supplementary application
        ->whereDate('end_date', '>=', now())
        ->where('level_id', $level)
        ->orderBy('id', 'desc')
        ->first();
}

function supplementaryApplicationAvailable($level = 4)
{
    return Application::whereDate('start_date', '<=', now())
        ->where('transaction_id', 3) // is a supplementary application
        ->whereDate('end_date', '>=', now())
        ->where('level_id', $level)
        ->orderBy('id', 'desc')
        ->first();
}

function activeApplication(Application $application)
{
    return now()->between($application->start_date, $application->end_date);
}

function plcCourses($course)
{
    $list = [
        'Part 1' => [
            'Civil procedure', 'Law of evidence', 'Alternative dispute resolution', 'Company and commercial law practice', 'Law practice management and legal accountancy', 'Criminal procedure',
        ],
        'Part 2' => [
            'Conveyancing and drafting', 'Family law and practice', 'Interpretation of deeds and statutes', 'Advocacy and legal ethics',
        ],
        'Post-Call' => [
            'Civil procedure', 'Constitutional law of Ghana', 'Law of evidence', 'Family law and practice', 'Ghana legal systems', 'Interpretation of deeds and statutes', 'Criminal procedure',
        ],
        'Entrance' => []
    ];

    return $list[$course] ?? [];
}

function getLevel($level_id)
{
    return Level::find($level_id);
}

function remarkPeriod()
{
    return (int) env('REMARK_PERIOD', DEFAULT_REMARK_PERIOD); // Use the environment variable or default to 30
}

function remarkDate()
{
    return now()->subDays(remarkPeriod());
}

function remarkAmount($count = 0)
{
     // Use the environment variable or default to the constant
     $amount = (int) env('REMARK_AMOUNT', DEFAULT_REMARK_AMOUNT);
     return $amount * $count;
}

function getResults($user, $level)
{
    $results = $user->examResults()
        // ->isPublished()
        ->where('level_id', $level)
        ->with('course')
        ->get()
        ->groupBy('course_id') ?? null;

    return $results ? getMaxScoresForGroups($results) : null;
}

function getNextLevel($level, $results)
{
    if ($level === 1 && $results && $results['meta']->failed < 3) return $level + 1;
    return null;
}

function levelCourses($level_id)
{
    $level = getLevel($level_id);
    return $level ? $level->courses : null;
}

function money($amount)
{
    return "Ghc " . numberFormat($amount);
}

function numberFormat($amount)
{
    return number_format($amount, 2, '.', ',');
}

function transactionType($type)
{
    return match ($type) {
        1 => 'entrance',
        2 => 'exam',
        3 => 'referral',
        4 => 'remarking',
        5 => 'retallying',
        default => null
    };
}

// function remarks($score)
// {
//     return passed($score) ? 'Passed' : 'Referred';
// }

// function passed($score)
// {
//     return $score >= 50;
// }

// function failed($score)
// {
//     return !passed($score);
// }

function getRemark($courseType, $failCount, $courseCount = null)
{
    Log::info('Getting remark', [
        'courseType' => $courseType, 
        'failCount' => $failCount, 
        'courseCount' => $courseCount
    ]);
    
    $isPostCall = str_contains($courseType, 'Post-Call');
    $isPLC2 = str_contains($courseType, 'PLC 2');
    if ($isPLC2) {
        return !$failCount ? "PASSED" : ($failCount < 2 ? "REFERRED" : "FAIL");
    } else {
        return ($isPostCall && $courseCount && $courseCount > 5)
            ? ($failCount < 4 ? "REFERRED" : "FAIL")
            : (!$failCount ? "PASSED" : ($failCount < 3 ? "REFERRED" : "FAIL"));
    }
}

function resit($failCount, $applicationType = 'PLC 2', $courseCount = null)
{
    $remark = getRemark($applicationType, $failCount, $courseCount);
    return $remark === 'FAIL';
}

function retake($failCount, $applicationType = 'PLC 2', $courseCount = null)
{
    $remark = getRemark($applicationType, $failCount, $courseCount);
    return $remark === 'REFERRED';
}

function getApplication($id)
{
    return Application::find($id);
}

function getMaxScoresForGroups($data)
{
    $result = [];
    $courses = [];
    $hasSanctionedResults = false;

    foreach ($data as $group => $items) {
        $highestScoreItem = null;
        $scores = [];

        foreach ($items as $item) {
            $scores[] = $item['finalScore'];
            if($item->sanction_id) $hasSanctionedResults = true;

            if ($highestScoreItem === null || $item['finalScore'] > $highestScoreItem['finalScore']) {
                $highestScoreItem = $item;
            }
        }

        $result[] = $highestScoreItem;

        // Create or update the "courses" sub-index
        $courses[$group] = $scores;
    }

    // Calculate meta values
    $count = count($result);
    if (!$count) return null;

    $absent = count(array_filter($result, function ($item) {
        return !$item->wrote_exam && !$item->is_exempted;
    }));
    $passed = count(array_filter($result, function ($item) {
        return $item->didPass();
    }));
    $failed = $count - $passed - $absent;
    $notPassed = $failed + $absent;

    $shouldRetake = retake($notPassed, $result[0]?->application->name, $count);
    $shouldResit = resit($notPassed, $result[0]?->application->name, $count);

    // Create the final result object with "items," "meta," and "courses" as properties
    $groupedObject = [
        'items' => $result,
        'meta' => (object)[
            'count' => $count,
            'passed' => $passed,
            'failed' => $failed,
            'notPassed' => $notPassed,
            'absent' => $absent,
            'resit' => $shouldResit,
            'retake' => $shouldRetake,
            'expel' => toBeExpelled($courses),
            'hasSanctionedResults' => $hasSanctionedResults
        ],
        'courses' => $courses,
    ];

    return $groupedObject;
}

// TODO: prevent 3 resits from registering
function canRegisterLevel(Application $application, User $user)
{
    $start_level = $user->start_level;
    $level_id = $application->level_id;

    if ($start_level > $level_id || !$application->isActive()) return false;

    $result = getResults($user, $start_level);

    if ($start_level == $level_id) {
        return (!$result || $result['meta']->resit);
    };

    if ($start_level == 1 && $level_id == 2) {
        if ($result && (!$result['meta']->resit || !$result['meta']->retake)) {
            $result = getResults($user, $start_level + 1);
            return (!$result || $result['meta']->resit);
        }
    }

    return false;
}

function toBeExpelled($courses)
{
    $failedCourses = 0;
    
    foreach ($courses as $id => $scores) {
        $course = Course::find($id);
        $courseFailed = false;

        // Check if the course was failed in any attempt
        foreach ($scores as $score) {
            if (!$course->didPass($score)) {
                $courseFailed = true;
                break; // No need to check other attempts if already failed once
            }
        }
        
        if ($courseFailed) {
            $failedCourses++;
            if ($failedCourses >= 3) {
                return true; // Expel if 3 or more unique courses failed
            }
        }
    }

    return false;
}

function studentStatus($user)
{
    $start_level = $user->start_level;
    $start_results = getResults($user, $start_level);
    $expelled = $start_results['meta']->expel ?? null;
    $has_retake = $start_results['meta']->retake ?? null;
    $has_resit = $start_results['meta']->resit ?? null;
    $has_sanctioned_results = $start_results && $start_results['meta']?->hasSanctionedResults;
    $has_postcall_resit = $user->isPostCallStudent() && $has_resit;    

    $next_level = null;
    $next_results = null;

    if ($start_level === 1 && $start_results && !$expelled && !$has_resit) {
        $next_results = getResults($user, $start_level + 1) ?? null;
        $next_level = getNextLevel($start_level, $start_results);
    }

    return [
        'start_results' => $start_results,
        'start_level' => $start_level,
        'next_results' => $next_results,
        'next_level' => $next_level,
        'expelled' => $expelled,
        'has_sanctioned_results' => $has_sanctioned_results,
        'has_postcall_resit' => $has_postcall_resit
    ];
}

function randomPassword()
{
    $random_characters = 5;

    $lower_case = "abcdefghijklmnopqrstuvwxyz";
    $upper_case = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    $numbers = "1234567890";
    $symbols = "!@#$%^&*";

    $lower_case = str_shuffle($lower_case);
    $upper_case = str_shuffle($upper_case);
    $numbers = str_shuffle($numbers);
    $symbols = str_shuffle($symbols);

    $random_password = substr($lower_case, 0, $random_characters);
    $random_password .= substr($upper_case, 0, $random_characters);
    $random_password .= substr($numbers, 0, $random_characters);
    $random_password .= substr($symbols, 0, $random_characters);

    return str_shuffle($random_password);
}

function levelsFromSlug($slug)
{
    $slug = strtolower(str_replace(' ', '', $slug));
    return levelSlugs()[$slug];
}

function levelSlugs()
{
    return array_flip([1 => 'plc1', 'plc2', 'postcall', 'entrance']);
}

function levelNames()
{
    return [1 => 'PLC 1', 'PLC 2', 'Post Call', 'Entrance'];
}

function venueSlugs()
{
    return array_flip([1 => 'Accra', 'Kumasi']);
}

function examinerType(User $user): ?string
{
    return $user->hasRole('marker') ? 'marker' : (
    $user->hasRole('moderator') ? 'moderator' : (
    $user->hasRole('iec') ? 'iec' : null)
    );
}

function remarkingDescription(array $ids)
{
    $results = Result::whereIn('id', $ids)->get();
    $description = "";

    foreach ($results as $result) {
        $description .= $result->application->name . " - " . $result->course->name . "\r\n";
    }

    return $description;
}

function referralDescription(array $ids, $exam)
{
    $courses = Course::whereIn('id', $ids)->get();
    $description = "";

    foreach ($courses as $course) {
        $description .= $exam . " - " . $course->name . "\r\n";
    }

    return $description;
}

function getRemarkedCourses()
{
    return Course::join('results', 'results.course_id', '=', 'courses.id')->join('remark_results', 'remark_results.result_id', '=', 'results.id')->select('courses.*')->distinct();
}

function disabilityList()
{
    return ["Visual impairment", "Hearing impairment", "Physically challenged"];
}

function disabilities()
{
    $array = [];
    foreach (disabilityList() as $value) {
        $array[$value] = $value;
    }
    return $array;
}

function nationalityList()
{
    return [
        'Afghan', 'Albanian', 'Algerian', 'American', 'Andorran', 'Angolan', 'Antiguans', 'Argentinean', 'Armenian', 'Australian', 'Austrian', 'Azerbaijani',
        'Bahamian', 'Bahraini', 'Bangladeshi', 'Barbadian', 'Barbudans', 'Batswana', 'Belarusian', 'Belgian', 'Belizean', 'Beninese', 'Bhutanese', 'Bolivian', 'Bosnian', 'Brazilian', 'British', 'Bruneian', 'Bulgarian', 'Burkinabe', 'Burmese', 'Burundian',
        'Cambodian', 'Cameroonian', 'Canadian', 'Cape Verdean', 'Central African', 'Chadian', 'Chilean', 'Chinese', 'Colombian', 'Comoran', 'Congolese', 'Costa Rican', 'Croatian', 'Cuban', 'Cypriot', 'Czech',
        'Danish', 'Djibouti', 'Dominican', 'Dutch',
        'East Timorese', 'Ecuadorean', 'Egyptian', 'Emirian', 'Equatorial Guinean', 'Eritrean', 'Estonian', 'Ethiopian',
        'Fijian', 'Filipino', 'Finnish', 'French',
        'Gabonese', 'Gambian', 'Georgian', 'German', 'Ghanaian', 'Greek', 'Grenadian', 'Guatemalan', 'Guinea-Bissauan', 'Guinean', 'Guyanese',
        'Haitian', 'Herzegovinian', 'Honduran', 'Hungarian',
        'I-Kiribati', 'Icelander', 'Indian', 'Indonesian', 'Iranian', 'Iraqi', 'Irish', 'Israeli', 'Italian', 'Ivorian',
        'Jamaican', 'Japanese', 'Jordanian',
        'Kazakhstani', 'Kenyan', 'Kittian and Nevisian', 'Kuwaiti', 'Kyrgyz',
        'Laotian', 'Latvian', 'Lebanese', 'Liberian', 'Libyan', 'Liechtensteiner', 'Lithuanian', 'Luxembourger',
        'Macedonian', 'Malagasy', 'Malawian', 'Malaysian', 'Maldivan', 'Malian', 'Maltese', 'Marshallese', 'Mauritanian', 'Mauritian', 'Mexican', 'Micronesian', 'Moldovan', 'Monacan', 'Mongolian', 'Moroccan', 'Mosotho', 'Motswana', 'Mozambican',
        'Namibian', 'Nauruan', 'Nepalese', 'New Zealander', 'Ni-Vanuatu', 'Nicaraguan', 'Nigerian', 'Nigerien', 'North Korean', 'Northern Irish', 'Norwegian',
        'Omani',
        'Pakistani', 'Palauan', 'Panamanian', 'Papua New Guinean', 'Paraguayan', 'Peruvian', 'Polish', 'Portuguese',
        'Qatari',
        'Romanian', 'Russian', 'Rwandan',
        'Saint Lucian', 'Salvadoran', 'Samoan', 'San Marinese', 'Sao Tomean', 'Saudi', 'Scottish', 'Senegalese', 'Serbian', 'Seychellois', 'Sierra Leonean', 'Singaporean', 'Slovakian', 'Slovenian', 'Solomon Islander', 'Somali', 'South African', 'South Korean', 'Spanish', 'Sri Lankan', 'Sudanese', 'Surinamer', 'Swazi', 'Swedish', 'Swiss', 'Syrian',
        'Taiwanese', 'Tajik', 'Tanzanian', 'Thai', 'Togolese', 'Tongan', 'Trinidadian or Tobagonian', 'Tunisian', 'Turkish', 'Tuvaluan',
        'Ugandan', 'Ukrainian', 'Uruguayan', 'Uzbekistani',
        'Venezuelan', 'Vietnamese',
        'Welsh',
        'Yemenite',
        'Zambian', 'Zimbabwean'
    ];
}

function nationalities()
{
    $array = [];
    foreach (nationalityList() as $value) {
        $array[$value] = $value;
    }
    return $array;
}

function institutionList()
{
    return [
        'Central University',
        'Ghana Institute Of Management And Public Administration (GIMPA)',
        'Greenfield College (Formerly College Of Science, Arts And Education)',
        'Kaaf University College',
        'Kings University College - University Of Cape Of Coast',
        'Kwame Nkrumah University Of Science And Technology (KNUST)',
        'Lancaster University Ghana',
        'Mountcrest University College',
        'Pentecost University',
        'Presbyterian University College',
        'Simon Diedong Dombo University of Integrated Development Studies (SDD-UBIDS)',
        'University Of Cape Coast (UCC)',
        'University Of Ghana (UG) School Of Law',
        'University Of Professional Studies (UPSA)',
        'Wisconsin International University College (WIUC)',
        'Zenith University College',
    ];
}

function institutions()
{
    $array = [];
    foreach (institutionList() as $value) {
        $array[$value] = $value;
    }
    // Add the "Other" option
    $array['-Other-'] = '-Other-';
    return $array;
}

function charges()
{
    return getSettingValue('charges') / 100;
}

function amountWithCharges($amount)
{
    return $amount * (1 + charges());
}

function getSettingValue($slug)
{
    return \App\Models\Setting::where('slug', $slug)->first()->value ?? 0;
}

function getEligibilityImportCsvExample()
{
    $headers = [
        'student_id',
        'first_name',
        'last_name',
        'email',
        'venue',
        'is_eligible',
        'eligibility_notes'
    ];
    
    $rows = [
        [
            'STU12345',
            'John',
            'Doe',
            '<EMAIL>',
            'Accra',
            '1',
            'Passed entrance exam'
        ],
        [
            'STU67890',
            'Jane',
            'Smith',
            '<EMAIL>',
            'Kumasi',
            '1',
            'Completed requirements'
        ]
    ];
    
    $output = fopen('php://temp', 'r+');
    fputcsv($output, $headers);
    foreach ($rows as $row) {
        fputcsv($output, $row);
    }
    rewind($output);
    $csv = stream_get_contents($output);
    fclose($output);
    
    return $csv;
}
