<?php

namespace App\Notifications;

use App\Models\Cred;
use App\Notifications\Channels\SmsChannel;
use App\Repository\SMSNotify;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class VoucherPaymentNotification extends Notification implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable, SerializesModels;

    public Cred $cred;
    public string $pin;

    /**
     * Create a new notification instance.
     */
    public function __construct(Cred $cred, string $pin)
    {
        $this->cred = $cred;
        $this->pin = $pin;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', SmsChannel::class];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Your Voucher details')
            ->markdown('mail.voucher-mail', [
                'cred' => $this->cred,
                'pin' => $this->pin,
                'application' => $this->cred->application,
            ]);
    }

    /**
     * Get the SMS representation of the notification.
     */
    public function toSms($notifiable): SMSNotify
    {
        return new SMSNotify(
            $this->cred->phone,
            "Thank you for applying to write the exam. Serial Number: {$this->cred->serial} and your Pin: {$this->pin}. Use these to complete your application. - IEC"
        );
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'serial' => $this->cred->serial,
            'application_id' => $this->cred->application_id,
        ];
    }
}
