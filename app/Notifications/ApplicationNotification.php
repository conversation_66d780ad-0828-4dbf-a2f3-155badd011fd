<?php

namespace App\Notifications;

use App\Notifications\Channels\SmsChannel;
use App\Repository\SMSNotify;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\HtmlString;

class ApplicationNotification extends Notification implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable, SerializesModels;

    /**
     * Create a new notification instance.
     */
    public function __construct()
    {
        //
    }

    public $tries = 3;

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', SmsChannel::class];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return $notifiable->status ? $this->successfulCandidate($notifiable) : $this->rejectedCandidate($notifiable);
    }

    public function toSms($notifiable): SMSNotify
    {
        return (new SMSNotify($notifiable->phone, "This is to inform you that your application to write the GSL Entrance Examination was " . ($notifiable->status ? "successful. Check your IEC portal account for details." : "unsuccessful.")));
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    public function successfulCandidate(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Application Status: Law School Entrance Examination')
            ->greeting("Dear $notifiable->first_name,")
            ->line("We are delighted to inform you that your application for the Entrance Examination into the Ghana School of Law on 6th September 2024  has been successful. Congratulations!")
            ->line("Information regarding the examination schedule, venue, and any other pertinent details you may need to prepare for the exam will be communicated to in due course.")
            ->line("Additionally, we encourage you to reach out to us if you have any questions or require further assistance leading up to the examination date.")
            ->line("Regards,")
            ->line("Independent Examinations Committee");
    }

    public function rejectedCandidate(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Application Status: Law School Entrance Examination')
            ->greeting("Dear $notifiable->first_name,")
            ->line("We hope this message finds you well.")
            ->line("Thank you for taking the time to apply for the upcoming Law School Entrance Exam. We appreciate your interest in our program and the effort you put into your application.")
            ->line("After careful consideration, we regret to inform you that your application for the entrance examination has not been successful. We understand that this news may be disappointing, but please know that our decision was made after thorough review and consideration of all applicants.")
            ->line("We encourage you to continue pursuing your academic and career goals in the field of law. There are various paths and opportunities available to you, and we believe that with perseverance and dedication, you will achieve success.")
            ->line("We appreciate your interest, and we wish you all the best in your future endeavors. Should you have any questions or require further information, please do not hesitate to contact us.");
    }
}
