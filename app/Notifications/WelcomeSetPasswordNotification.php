<?php

namespace App\Notifications;

use App\Notifications\Channels\SmsChannel;
use App\Repository\SMSNotify;
use Filament\Facades\Filament;
use Illuminate\Auth\Notifications\ResetPassword as BaseNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;


class WelcomeSetPasswordNotification extends BaseNotification implements ShouldQueue
{
    use Queueable;

    public $user;

    protected function buildMailMessage($url)
    {
        $count = config('auth.passwords.' . config('auth.defaults.passwords') . '.expire');

        return (new MailMessage)
            ->subject('Welcome to the IEC portal')
            ->greeting("Dear {$this->user->full_name},")
            ->line($this->getIntro())
            ->line("To get started, please click on the button below to set your password and access your account:")
            ->action('Set Password', $url)
            ->line("This password reset link will expire in {$count} minutes.")
            ->line("Upon setting your password, you'll be able to log in using your email {$this->user->email} along with the password you've chosen.");
    }

    protected function resetUrl($notifiable)
    {
        $admin_url = Filament::getResetPasswordUrl($this->token, $this->user);
        return $this->isAdmin() ? $admin_url : parent::resetUrl($notifiable);
    }

    public function via($notifiable)
    {
        return ['mail', SmsChannel::class];
    }

    public function toSms($notifiable): SMSNotify
    {
        return (new SMSNotify($notifiable->phone, "An account has been created for you on the IEC Portal. Check your email for further instructions."));
    }

    protected function getIntro()
    {
        $admin_intro = "We are thrilled to extend to you an invitation to access our admin portal, where you will have specific privileges tailored to your role. Whether you've been assigned administrative, accounting, marker, moderator, or other responsibilities, our portal provides the tools you need to effectively manage your tasks.";

        $student_intro = "We hope this email finds you well. We're excited to inform you that an account has been created for you on our examination score portal. You can now access your examination scores and other related information through our platform.";

        return $this->isAdmin() ? $admin_intro : $student_intro;
    }

    protected function isAdmin()
    {
        return (bool)$this->user->roles()->count();
    }
}
