<?php

namespace App\Notifications;

use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use App\Repository\SMSNotify;

class CustomResetPassword extends Notification
{
    protected $password;
    protected $phone;

    public function __construct($password, $phone)
    {
        $this->password = $password;
        $this->phone = $phone;
    }

    public function via($notifiable)
    {
        return ['mail', 'sms'];
    }

    public function toMail($notifiable)
    {
        // Temporary logging (remove in production!)
        Log::info('Password reset for user: ' . $notifiable->email . ', New password: ' . $this->password);
        $somethingElse = "somethinElse";

        return (new MailMessage)
            ->subject(Lang::get('Password Reset Notification'))
            ->line('Your password has been reset.')
            ->line("Your nw password is: {$this->password}.")
            ->line("You neeed to check: {$somethingElse}.")
            ->line('Please change your password after logging in.');
    }

    public function toSms($notifiable): SMSNotify
    {
        return new SMSNotify(
            $notifiable->phone,
            Lang::get('Your new password is: :password. Please change it after logging in.', ['password' => $this->password])
        );
    }
}
