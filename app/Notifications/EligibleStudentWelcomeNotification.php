<?php

namespace App\Notifications;

use App\Notifications\Channels\SmsChannel;
use App\Repository\SMSNotify;
use Filament\Facades\Filament;
use Illuminate\Auth\Notifications\ResetPassword as BaseNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\Lang;

class EligibleStudentWelcomeNotification extends BaseNotification implements ShouldQueue
{
    use Queueable;

    public $user;

    /**
     * Create a notification instance.
     *
     * @param  string  $token
     * @return void
     */
    public function __construct($token)
    {
        parent::__construct($token);
        $this->token = $token;
    }

    protected function buildMailMessage($url)
    {
        $count = config('auth.passwords.' . config('auth.defaults.passwords') . '.expire');

        return (new MailMessage)
            ->subject('Ghana School of Law Examination Registration')
            ->greeting("Dear {$this->user->full_name},")
            ->line("We are pleased to inform you that you have been marked as eligible for this year's Ghana School of Law examination.")
            ->line("To complete your registration, please click on the button below to set your password and access your account:")
            ->action('Set Password', $url)
            ->line("This password reset link will expire in {$count} minutes.")
            ->line("Upon setting your password, you'll be able to log in using your email {$this->user->email} along with the password you've chosen.")
            ->line("Please ensure you complete your registration promptly to secure your place in the upcoming examination.");
    }

    /**
     * Get the reset URL for the given notifiable.
     *
     * @param  mixed  $notifiable
     * @return string
     */
    protected function resetUrl($notifiable)
    {
        if ($this->isAdmin()) {
            return Filament::getResetPasswordUrl($this->token, $this->user);
        }
        
        // Make sure we're using the email from the notifiable object
        $email = $notifiable->getEmailForPasswordReset();
        
        return url(route('password.reset', [
            'token' => $this->token,
            'email' => $email,
        ], false));
    }

    public function via($notifiable)
    {
        return ['mail', SmsChannel::class];
    }

    public function toSms($notifiable): SMSNotify
    {
        return new SMSNotify(
            $notifiable->phone, 
            "You have been marked eligible for the Ghana School of Law examination. Please check your email to set your password and complete your registration."
        );
    }

    protected function isAdmin()
    {
        return (bool)$this->user->roles()->count();
    }
}