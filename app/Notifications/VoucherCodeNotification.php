<?php

namespace App\Notifications;

use App\Models\EVoucher;
use App\Notifications\Channels\SmsChannel;
use App\Repository\SMSNotify;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class VoucherCodeNotification extends Notification implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable, SerializesModels;

    public EVoucher $voucher;

    /**
     * Create a new notification instance.
     */
    public function __construct(EVoucher $voucher)
    {
        $this->voucher = $voucher;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', SmsChannel::class];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Your E-voucher Code - IEC Examination')
            ->markdown('mail.voucher-code-mail', [
                'voucher' => $this->voucher,
            ]);
    }

    /**
     * Get the SMS representation of the notification.
     */
    public function toSms($notifiable): SMSNotify
    {
        $examFee = $this->voucher->application ? $this->voucher->application->amount : 550;

        return new SMSNotify(
            $this->voucher->phone,
            "Your E-voucher code is: {$this->voucher->voucher_code}. Use this code to pay the application fee of GHS {$examFee}. Valid for current application period only. - IEC"
        );
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'voucher_code' => $this->voucher->voucher_code,
            'amount' => $this->voucher->amount,
            'application_id' => $this->voucher->application_id,
        ];
    }
}
