<?php

namespace App\Notifications;

use App\Notifications\Channels\SmsChannel;
use App\Repository\SMSNotify;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class EntranceRequirementsNotification extends Notification implements ShouldQueue
{
    use Queueable, InteractsWithQueue, Dispatchable, SerializesModels;

    /**
     * Create a new notification instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $examinationCenter = $notifiable->venue?->location === 'Kumasi'
            ? 'Kwame Nkrumah University of Science and Technology'
            : 'University of Ghana';

        return (new MailMessage)
            ->subject("Ghana School of Law Entrance Examination - Best Wishes and Important Information")
            ->greeting("Dear $notifiable->first_name,")
            ->line('The IEC would like to wish you all the best for the upcoming Entrance Examination.')
            ->line('Please come along with the following:')
            ->line("1. You will need the IEC Examination Identification (ID) Number {$notifiable->student_id} for the examination.")
            ->line('2. The ONLY ACCEPTED personal ID which is your Ghana Card.')
            ->line('3. You should be seated by 11:30 am.')
            ->line('4. The time of the Examination is 12:00 noon.')
            ->line('')
            ->line("Your Examination Centre will be the {$examinationCenter} and,")
            ->line("Your Examination Hall is {$notifiable->venue?->name}.");
    }

    public function toSms($notifiable): SMSNotify
    {
        return (new SMSNotify($notifiable->phone, "Your Ghana School of Law Entrance Examination application submission has been received successfully. You status will be communicated to you duly. IEC"));
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
