<?php

namespace App\Jobs;

use App\Notifications\WelcomeSetPasswordNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class Queued<PERSON>elcomeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user;

    public $tries = 3;

    public function __construct($user)
    {
        $this->user = $user;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $token = app('auth.password.broker')->createToken($this->user);
        $notification = new WelcomeSetPasswordNotification($token);
        $notification->user = $this->user;
        $this->user->notify($notification);
    }
}
