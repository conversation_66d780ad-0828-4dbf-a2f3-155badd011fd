<?php

namespace App\Jobs;

use App\Notifications\EligibleStudentWelcomeNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class QueuedEligibleStudentWelcomeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user;

    public $tries = 3;
    
    /**
     * The number of seconds to wait before retrying the job.
     */
    public $backoff = 5;

    public function __construct($user)
    {
        $this->user = $user;
    }

    /**
     * Get the middleware the job should pass through.
     */
    public function middleware(): array
    {
        return [
        ];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Add a small sleep to avoid resend rate limits
        usleep(500000); // 500ms
        
        // Create token and send notification
        $token = app('auth.password.broker')->createToken($this->user);
        $notification = new EligibleStudentWelcomeNotification($token);
        $notification->user = $this->user;
        $this->user->notify($notification);
    }
}