<?php

namespace App\Policies;

use App\Models\Application;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ApplicationPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function update(User $user): bool
    {
        return $user->hasRole('admin');
    }

    public function viewAny(User $user): bool
    {
        return $user->hasRole(['admin', 'iec', 'marker', 'moderator', 'gsl', 'glc']);
    }

    public function create(User $user): bool
    {
        return $user->hasRole('admin');
    }
}
