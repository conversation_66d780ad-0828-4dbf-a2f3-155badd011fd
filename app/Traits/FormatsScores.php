<?php

namespace App\Traits;

trait FormatsScores
{
    /**
     * Format score to hide .00 decimals but show actual decimal values
     * 
     * Examples:
     * - 5.00 -> "5"
     * - 5.50 -> "5.5" 
     * - 12.75 -> "12.75"
     * - 0.25 -> "0.25"
     * - 10.10 -> "10.1"
     * 
     * @param mixed $score
     * @return string|null
     */
    public function formatScore($score)
    {
        if ($score === null || $score === '') {
            return null;
        }
        
        // Convert to float to handle string inputs
        $score = (float) $score;
        
        // If it's a whole number, show without decimals
        if ($score == floor($score)) {
            return (string) (int) $score;
        }
        
        // Otherwise, show with minimal decimal places (remove trailing zeros)
        return rtrim(rtrim(number_format($score, 2, '.', ''), '0'), '.');
    }

    /**
     * Format score for display in tables and forms
     * Same as formatScore but with additional null handling
     * 
     * @param mixed $score
     * @return string
     */
    public function displayScore($score)
    {
        $formatted = $this->formatScore($score);
        return $formatted ?? '';
    }
}
