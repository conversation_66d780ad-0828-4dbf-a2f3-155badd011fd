<?php

namespace App\Traits;

use App\Models\Application;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

trait HasExam
{
    public function markPresent()
    {
        return $this->update(['wrote_exam' => 1]);
    }

    public function markAbsent()
    {
        return $this->update(['wrote_exam' => 0]);
    }

    public function marker()
    {
        return $this->belongsTo(User::class, 'marker_id');
    }

    public function remarker()
    {
        return $this->belongsTo(User::class, 'remarker_id');
    }

    public function moderator()
    {
        return $this->belongsTo(User::class, 'moderator_id');
    }

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    public function scopeIsPublished(Builder $query): void
    {
        $query->where('isPublished', 1);
    }
}
