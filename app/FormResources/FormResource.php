<?php

namespace App\FormResources;

use App\Models\Venue;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;

class FormResource
{
    public static function entrance(Form $form, array $fields = []): Form
    {
        return $form
            ->schema([
//                Forms\Components\Hidden::make('application_id'),
                Forms\Components\Section::make('Personal Data')
                    ->schema([
                        Forms\Components\Grid::make(3)->schema([
                            TextInput::make('family_name')->maxLength(255)->required()->placeholder('Enter family name'),
                            TextInput::make('first_name')->maxLength(255)->required()->placeholder('Enter first name'),
                            TextInput::make('middle_name')->maxLength(255)->placeholder('Enter middle name'),
                        ]),
                        Forms\Components\Grid::make(3)->schema([
                            Forms\Components\Select::make('title')->options([
                                "Mr" => "Mr",
                                "Ms" => "Ms",
                                "Miss" => "Miss",
                                "Mx" => "Mx",
                                "Mrs" => "Mrs",
                                "Dr" => "Dr",
                                "Prof" => "Prof",
                                "Rev" => "Rev",
                                "Sir" => "Sir",
                            ])->placeholder('Select Title')->required(),
                            Forms\Components\Select::make('gender')->options([
                                'Male' => 'Male',
                                'Female' => 'Female'
                            ])->placeholder('Select Gender')->required(),
                            DatePicker::make('dob')
                                ->required()
                                ->label('Date of Birth'),
                        ]),
                        Forms\Components\Select::make('venue_id')
                            ->options(Venue::query()->whereNull('hall')->pluck('location', 'id')->unique())
                            ->placeholder('Select Examination Centre')
                            ->label('Examination Centre')
                            ->required(),
                        // TextInput::make('ghana_card')->maxLength(255)->required()->placeholder('Ghana Card'),
                        Forms\Components\Select::make('nationality')
                            ->options(nationalities())
                            ->placeholder('Select your nationality')
                            ->required()
                            ->searchable()
                            ->default('Ghanaian'),
                        TextInput::make('email')->email()->maxLength(255)->required()->placeholder('Enter email'),
                        PhoneInput::make('phone')->required()->validateFor('auto')->initialCountry('GH'),
                    ]),

                Forms\Components\Section::make('Academic Info')
                    ->schema([
                        Forms\Components\Select::make('institution')
                            ->options(institutions())
                            ->placeholder('Select your LLB institution')
                            ->required()
                            ->live()
                            ->searchable(),
                        TextInput::make('institution_other')
                            ->label('Please specify your institution')
                            ->placeholder('Enter your institution name')
                            ->maxLength(255)
                            ->required()
                            ->hidden(fn(Forms\Get $get) => $get('institution') !== '-Other-'),
                        Forms\Components\Toggle::make('disability')
                            ->inline(false)
                            ->live()
                            ->label('Do you have any disability?'),
                        Forms\Components\Fieldset::make('Disability Information')
                            ->columns(2)
                            ->hidden(fn(Forms\Get $get) => !$get('disability'))
                            ->schema([
                                Forms\Components\Select::make('pwd_type')
                                    ->label('Select Disability')
                                    ->options([...disabilities(), "Other" => "Other"])
                                    ->live()
                                    ->placeholder('Select Disability')
                                    ->required(),
                                Forms\Components\Textarea::make('pwd_details')
                                    ->label('What is your disability?')
                                    ->placeholder('describe your disability')
                                    ->rows(3)
                                    ->hidden(fn(Forms\Get $get) => $get('pwd_type') !== "Other")
                                    ->required(),
                                Forms\Components\SpatieMediaLibraryFileUpload::make('Disability Support Docs')
                                    ->label('Upload Supporting Documents')
                                    ->downloadable()
                                    ->maxSize(10240)
                                    ->multiple()
                                    ->collection('Disability Support Docs')
                                    ->required()
                                    ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\SpatieMediaLibraryFileUpload::make('LLB Certificate')
                                    ->label('Certified true copy of LLB Certificate')
                                    ->downloadable()
                                    ->maxSize(10240)
                                    ->collection('LLB Certificate')
                                    ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'])
                                    ->required(),
                                Forms\Components\SpatieMediaLibraryFileUpload::make('Transcript')
                                    ->label('True Copy of LLB Transcript')
                                    ->downloadable()
                                    ->maxSize(10240)
                                    ->collection('Transcript')
                                    ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'])
                                    ->required(),
                            ]),
                    ]),

                Forms\Components\Section::make('Required Documents')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\SpatieMediaLibraryFileUpload::make('Passport Pic')
                                    ->label('Recent passport picture')
                                    ->helperText('Upload a clear passport-style photo. Accepted formats: JPG, PNG, GIF, WebP. Max size: 2MB.')
                                    ->downloadable()
                                    ->maxSize(10240)
                                    ->collection('Passport Pic')
                                    ->acceptedFileTypes(['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'])
                                    ->required(),
                                Forms\Components\SpatieMediaLibraryFileUpload::make('Ghana Card')
                                    ->label('Copy of Ghana card')
                                    ->helperText('Upload a clear copy of your Ghana Card. Accepted formats: PDF, JPG, PNG, GIF, WebP. Max size: 2MB.')
                                    ->downloadable()
                                    ->maxSize(10240)
                                    ->collection('Ghana Card')
                                    ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'])
                                    ->required(),
                                Forms\Components\SpatieMediaLibraryFileUpload::make('Declaration Form')
                                    ->label('Declaration form (download and fill)')
                                    ->downloadable()
                                    ->maxSize(10240)
                                    ->collection('Declaration Form')
                                    ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'])
                                    ->required(),
                                // Forms\Components\SpatieMediaLibraryFileUpload::make('Other Documents')
                                //     ->label('Other (e.g. academic certificates)')
                                //     ->downloadable()
                                //     ->maxSize(10240)
                                //     ->collection('Other Documents')
                                //     ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'])
                                //     ->multiple()
                            ]),
                    ]),
                ...$fields
            ]);
    }

    public static function cred(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('first_name')->maxLength(255)->required()->placeholder('Enter first name(s)'),
                TextInput::make('last_name')->maxLength(255)->required()->placeholder('Enter last name'),
                PhoneInput::make('phone')->initialCountry('GH')->required()->validateFor('auto'),
                TextInput::make('email')->email()->maxLength(255)->required()->unique(ignoreRecord: true)->placeholder('Email address'),
            ]);
    }

    public static function credLogin(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('serial')->maxLength(255)->required()->placeholder('Enter Serial Number'),
                TextInput::make('pin')->maxLength(255)->required()->placeholder('Enter Pin'),
            ]);
    }

    public static function voucherPurchase(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('first_name')
                    ->label('First Name')
                    ->required()
                    ->maxLength(255)
                    ->placeholder('Enter first name'),
                TextInput::make('last_name')
                    ->label('Last Name')
                    ->required()
                    ->maxLength(255)
                    ->placeholder('Enter last name'),
                TextInput::make('email')
                    ->label('Email Address')
                    ->email()
                    ->required()
                    ->maxLength(255)
                    ->placeholder('Enter email address'),
                PhoneInput::make('phone')
                    ->label('Phone Number')
                    ->required()
                    ->initialCountry('GH')
                    ->validateFor('auto'),
            ]);
    }

    public static function voucherRedemption(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('voucher_code')
                    ->label('Voucher Code')
                    ->required()
                    ->placeholder('Enter your E-voucher code (e.g., EV12345678)')
                    ->maxLength(20)
                    ->rule('regex:/^EV[A-Z0-9]{8}$/'),
            ]);
    }
}
