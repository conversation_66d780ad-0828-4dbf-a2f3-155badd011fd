<?php

namespace App\Enums;

enum EntranceSection: string
{
    case SECTION_A = 'Section A';
    case SECTION_B = 'Section B';

    public function label(): string
    {
        return match ($this) {
            self::SECTION_A => 'A',
            self::SECTION_B => 'B',
        };
    }

    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
        // return array_map(fn (self $role) => $role->value, self::cases());
    }

    public static function getKeyValues(): array
    {
        return array_column(self::cases(), 'value', 'value');
        // return array_map(fn (self $role) => $role->value, self::cases());
    }
}
