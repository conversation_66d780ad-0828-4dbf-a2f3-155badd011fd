<?php

namespace App\Enums;

enum RemarkRetallyStatus: string
{
    case CANCELLED = 'cancelled';
    case CANCEL_REQUESTED = 'cancel_requested';
    case PENDING = 'pending';
    case PUBLISHED = 'published';

    public function label(): string
    {
        return match ($this) {
            self::CANCELLED => 'Cancelled',
            self::CANCEL_REQUESTED => 'Cancel Requested',
            self::PENDING => 'Pending',
            self::PUBLISHED => 'Published',
        };
    }

    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function getKeyValues(): array
    {
        return array_column(self::cases(), 'value', 'value');
    }
}
