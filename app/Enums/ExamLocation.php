<?php

namespace App\Enums;

enum ExamLocation: string
{
    case ACCRA = 'Accra';
    case KUMASI = 'Kumasi';

    public function label(): string
    {
        return match ($this) {
            self::ACCRA => 'Accra',
            self::KUMASI => 'Kumasi',
        };
    }

    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
        // return array_map(fn (self $role) => $role->value, self::cases());
    }

    public static function getKeyValues(): array
    {
        return array_column(self::cases(), 'value', 'value');
        // return array_map(fn (self $role) => $role->value, self::cases());
    }
}
