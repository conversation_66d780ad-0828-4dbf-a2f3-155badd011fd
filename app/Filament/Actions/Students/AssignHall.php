<?php

namespace App\Filament\Actions\Students;

use App\Models\Venue;
use App\Models\User;
use App\Models\Submission;
use App\Notifications\EntranceRequirementsNotification;
use Filament\Actions\Concerns\CanCustomizeProcess;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Support\Facades\Notification;

class AssignHall extends BulkAction
{
    use CanCustomizeProcess;

    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->modalDescription("Assign hall to selected student")
            ->form([
                Select::make('venue_id')
                    ->options(Venue::whereNotNull('hall')->pluck('name', 'id'))
                    ->label('Select Hall')
                    ->name('venue_id')
                    ->placeholder('Select a hall')
                    ->searchable()
                    ->required(),
            ])
            ->modalWidth(MaxWidth::ExtraSmall);

        $this->action(function (array $arguments, Form $form): void {
            $venueId = $form->getState()['venue_id'];
            $notifiedSubmissions = collect();

            $this->process(static function($records) use ($venueId, &$notifiedSubmissions) {
                return $records->each(function($record) use ($venueId, &$notifiedSubmissions) {
                    // Update record's venue
                    $record->update(['venue_id' => $venueId]);

                    // Handle different record types
                    if ($record instanceof User) {
                        // If it's a User, find related submissions
                        $submissions = $record->submissions()
                            ->whereNotNull('status') // Only approved submissions
                            ->get();

                        foreach ($submissions as $submission) {
                            // Update submission's venue to match user's venue
                            $submission->update(['venue_id' => $venueId]);

                            // Add to notification collection if not already notified
                            if (!$submission->notified) {
                                $notifiedSubmissions->push($submission);
                            }
                        }
                    } elseif ($record instanceof Submission) {
                        // If it's a Submission, handle it directly
                        if (!$record->notified && $record->status !== null) {
                            $notifiedSubmissions->push($record);
                        }
                    }
                });
            });

            // Send entrance requirements notification to all affected submissions
            if ($notifiedSubmissions->isNotEmpty()) {
                Notification::send($notifiedSubmissions, new EntranceRequirementsNotification());

                // Mark submissions as notified
                $notifiedSubmissions->each(function($submission) {
                    $submission->update(['notified' => true]);
                });
            }

            $notificationMessage = "Students added to hall";
            if ($notifiedSubmissions->isNotEmpty()) {
                $notificationMessage .= " and {$notifiedSubmissions->count()} entrance requirement notifications sent";
            }

            $this->successNotificationTitle($notificationMessage);
            $this->success();
        });
    }
}
