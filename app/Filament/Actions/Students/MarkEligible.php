<?php

namespace App\Filament\Actions\Students;

use Filament\Tables\Actions\BulkAction;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Database\Eloquent\Collection;

class MarkEligible extends BulkAction
{
    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->label('Update Eligibility')
            ->modalDescription("Update eligibility status for selected students")
            ->form([
                Toggle::make('is_eligible')
                    ->label('Mark as Eligible')
                    ->default(true)
                    ->required(),
                Textarea::make('eligibility_notes')
                    ->label('Notes')
                    ->placeholder('Optional notes about eligibility')
                    ->maxLength(255),
            ])
            ->modalWidth(MaxWidth::ExtraSmall);

        $this->action(function (array $data, Collection $records): void {
            $records->each(function ($record) use ($data) {
                $record->update([
                    'is_eligible' => $data['is_eligible'],
                    'eligibility_notes' => $data['eligibility_notes'],
                    'eligibility_date' => $data['is_eligible'] ? now() : null,
                ]);
            });

            $this->success();
        });
    }
}