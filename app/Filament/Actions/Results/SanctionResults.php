<?php

namespace App\Filament\Actions\Results;

use App\Models\Audit;
use App\Models\Result;
use App\Models\Sanction;
use Filament\Actions\Action;
use Filament\Actions\Concerns\CanCustomizeProcess;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\BulkAction;

class SanctionResults extends BulkAction
{
    use CanCustomizeProcess;

    protected function setUp(): void
    {
        parent::setUp();

        $this->modalDescription("Apply a sanction to the selected results.")
        ->form([
            Select::make('sanction_id')
                ->label('Select Sanction')
                ->options(Sanction::all()->pluck('name', 'id')) // Fetch all sanctions
                ->required(),
        ])
        ->action(function (array $data) {
            $sanctionId = $data['sanction_id'];

            // Apply the sanction to the selected results
            $this->process(static function ($records) use ($sanctionId) {
                $records->each(function (Result $result) use ($sanctionId) {
                    $result->sanction_id = $sanctionId; // Update the sanction_id
                    $result->save(); // Save the changes
                });
            });

            $this->successNotificationTitle("Sanction applied successfully.");
            $this->success();
        });
    }
}
