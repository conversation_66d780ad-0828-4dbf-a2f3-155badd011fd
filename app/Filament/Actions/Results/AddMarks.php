<?php

namespace App\Filament\Actions\Results;

use App\Models\Audit;
use App\Models\Result;
use Filament\Actions\Action;
use Filament\Actions\Concerns\CanCustomizeProcess;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\BulkAction;

class AddMarks extends BulkAction
{
    use CanCustomizeProcess;

    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->modalDescription("Adds marks to each person's score")
            ->form([
                TextInput::make('mark')
                    ->label('Marks to add')
                    ->numeric()
                    ->maxValue(100)
                    ->required(),
            ])
            ->modalWidth(MaxWidth::ExtraSmall);

        $this->action(function (array $arguments, Form $form): void {
            $add_mark = $form->getState()['mark'];

            $this->process(static function ($records) use ($add_mark) {
                $records->each(fn($record) => $record->update(['iec_score' => min(($record->moderator_score ?? $record->marker_score) + $add_mark, 100)]));
                Audit::create(['application_id' => $records->first()->application_id, 'description' => $add_mark . " marks added to " . count($records) . " results"]);
            });

            $this->successNotificationTitle("Marks added successfully");
            $this->success();
        });
    }
}
