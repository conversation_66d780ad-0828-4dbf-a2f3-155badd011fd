<?php

namespace App\Filament\Actions\Results;

use App\Models\Result;
use App\Models\User;
use Filament\Actions\Concerns\CanCustomizeProcess;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\BulkAction;

class AssignMarker extends BulkAction
{
    use CanCustomizeProcess;

    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->modalDescription("Assign marker to selected student and course")
            ->form([
                Select::make('marker_id')
                    ->options(User::role('marker')->get()->pluck('full_name', 'id'))
                    ->label('Select Marker')
                    ->name('marker_id')
                    ->placeholder('Select a Marker')
                    ->searchable()
                    ->required(),
            ])
            ->modalWidth(MaxWidth::ExtraSmall);

        $this->action(function (array $arguments, Form $form): void {
            $this->process(static fn($records) => $records->each(fn($record) => $record->update([
                'marker_id' => $form->getState()['marker_id']
            ])));

            $this->successNotificationTitle("Marker added");
            $this->success();
        });
    }
}
