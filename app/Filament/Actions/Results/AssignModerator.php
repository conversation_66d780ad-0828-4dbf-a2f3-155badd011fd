<?php

namespace App\Filament\Actions\Results;

use App\Models\Result;
use App\Models\User;
use Filament\Actions\Concerns\CanCustomizeProcess;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\BulkAction;

class AssignModerator extends BulkAction
{
    use CanCustomizeProcess;

    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->modalDescription("Assign moderator to selected student and course")
            ->form([
                Select::make('moderator_id')
                    ->options(User::role('moderator')->get()->pluck('full_name', 'id'))
                    ->label('Select Moderator')
                    ->name('moderator_id')
                    ->placeholder('Select a Moderator')
                    ->required(),
            ])
            ->modalWidth(MaxWidth::ExtraSmall);

        $this->action(function (array $arguments, Form $form): void {
            $this->process(static fn($records) => $records->each(fn($record) => $record->update([
                'moderator_id' => $form->getState()['moderator_id']
            ])));

            $this->successNotificationTitle("Moderator added");
            $this->success();
        });
    }
}
