<?php

namespace App\Filament\Actions\Submission;

use App\Models\Level;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Support\Enums\MaxWidth;

class NotifyEntrance extends Action
{

    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->label('Notify')
            ->color('success')
            ->icon('heroicon-o-envelope')
            ->modalDescription("Notify Entrance Applicants")
            ->form([
                Select::make('id')
                    ->options(Level::where('abbr', 'Entrance')->first()->applications()->pluck('name', 'id'))
                    ->label('Application')
                    ->name('name')
                    ->placeholder('Select Entrance Application')
                    ->required(),
            ])
            ->modalWidth(MaxWidth::ExtraSmall);

        $this->action(function (array $arguments, Form $form): void {
            $application_id = $form->getState()['id'];
//            dispatch notification
            $this->successNotificationTitle("Applicants successfully notified");
            $this->success();
        });
    }
}
