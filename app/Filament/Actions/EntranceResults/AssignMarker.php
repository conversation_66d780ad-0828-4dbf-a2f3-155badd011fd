<?php

namespace App\Filament\Actions\EntranceResults;

use App\Enums\EntranceSection;
use App\Models\Result;
use App\Models\User;
use Filament\Actions\Concerns\CanCustomizeProcess;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\BulkAction;

class AssignMarker extends BulkAction
{
    use CanCustomizeProcess;

    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->modalDescription("Assign marker to selected candidates")
            ->form(function (Form $form) {
                return [
                    Select::make('marker_id')
                        ->options(function () {
                            $section = $this->getLivewire()->getTableFilterState('section');
                            return User::role('marker')
                                ->when($section, function ($query) use ($section) {
                                    $query->where(function ($q) use ($section) {
                                        $q->where('section', $section);
                                    });
                                })
                                ->get()
                                ->pluck('full_name', 'id');
                        })
                        ->label('Select Marker')
                        ->placeholder('Select a Marker')
                        ->searchable()
                        ->required(),
                ];
            })
            ->modalWidth(MaxWidth::ExtraSmall);

        $this->action(function (array $arguments, Form $form): void {
            $formState = $form->getState();
            $markerId = $formState['marker_id'];
            $section = $this->getLivewire()->getTableFilterState('section');
            $sectionValue = $section['section'] ?? null;

            $this->process(static fn($records) => $records->each(fn($record) => $record->update([
                "marker_id" => $markerId,
                "section" => $sectionValue
            ])));

            $this->successNotificationTitle("Marker added");
            $this->success();
        });
    }
}
