<?php

namespace App\Filament\Actions\EntranceResults;

use Filament\Actions\Concerns\CanCustomizeProcess;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\BulkAction;

class ReactivateMarks extends BulkAction
{
    use CanCustomizeProcess;

    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->modalDescription("Reactivate selected markers and moderators")
            ->form(function (Form $form) {
                return [
                    Select::make('roles_to_reactivate') // Multi-select for roles
                        ->options([
                            'marker' => 'Marker',
                            'moderator' => 'Moderator',
                        ])
                        ->label('Select Roles to Reactivate')
                        ->placeholder('Select roles')
                        ->multiple() // Allow multiple selections
                        ->required(),
                ];
            })
            ->modalWidth(MaxWidth::ExtraSmall);

        $this->action(function (array $arguments, Form $form): void {
            $formState = $form->getState();
            $rolesToReactivate = $formState['roles_to_reactivate'];

            $this->process(static fn($records) => $records->each(fn($record) => $record->update([
                "reactivate_marker" => in_array('marker', $rolesToReactivate), // Set to true if 'marker' is selected
                "reactivate_moderator" => in_array('moderator', $rolesToReactivate), // Set to true if 'moderator' is selected
            ])));

            $this->successNotificationTitle("Selected roles reactivated");
            $this->success();
            $this->deselectRecordsAfterCompletion();
        });
    }
}
