<?php

namespace App\Filament\Actions\TableActions;

use App\Models\Audit;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;

class AcceptModeratorResults extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->label('Accept Results')
            ->color('gray')
            ->requiresConfirmation()
            ->modalDescription("Record Moderator results under IEC for onward updates.")
            ->modalWidth(MaxWidth::ExtraSmall);

        $this->action(function (array $arguments): void {
            $results = $this->arguments['application']->results;
            $iecScore = 0;

            foreach ($results as $result) {
                $iecScore = !$result->wrote_exam ? 0 : $result->totalScore();
                $result->update(['iec_score' => $iecScore]);
            }

            Audit::create(['application_id' => $this->arguments['application']->id, 'description' => 'Moderator scores accepted']);

            $this->successNotificationTitle("Moderator scores accepted.");
            $this->success();
        });
    }
}
