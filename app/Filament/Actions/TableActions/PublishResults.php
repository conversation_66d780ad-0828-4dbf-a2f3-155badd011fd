<?php

namespace App\Filament\Actions\TableActions;

use App\Notifications\ApplicationNotification;
use App\Notifications\ResultsPublishedNotification;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\Notification;

class PublishResults extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->label('Publish Results')
            ->color('warning')
            ->icon('heroicon-o-paper-airplane')
            ->requiresConfirmation()
            ->modalDescription("Publish results.")
            ->modalIconColor('warning')
            ->modalWidth(MaxWidth::ExtraSmall);

        $this->action(function (array $arguments): void {
            $application = $this->arguments['application'];
            $application->exam_published_date = now();
            $application->save();

            // Notification::send($records->filter(), new ResultsPublishedNotification($application));

            $this->successNotificationTitle("Results published successfully");
            $this->success();
        });
    }


}
