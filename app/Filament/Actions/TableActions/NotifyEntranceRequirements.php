<?php

namespace App\Filament\Actions\TableActions;

use App\Notifications\EntranceRequirementsNotification;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\Notification;

class NotifyEntranceRequirements extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->label('Notify Requirements')
            ->color('success')
            ->icon('heroicon-o-envelope')
            ->requiresConfirmation()
            ->modalDescription("Notify Entrance Applicants. Notifies applicant's on exam details and requirements.")
            ->modalIconColor('warning')
            ->modalWidth(MaxWidth::ExtraSmall);

        $this->action(function (array $arguments): void {
            $submissions = $this->arguments['application']->submissions()->whereNull("notified")->whereNotNull('status')->get();

            Notification::send($submissions, new EntranceRequirementsNotification());

            $this->successNotificationTitle("Applicants successfully notified");
            $this->success();
        });
    }


}
