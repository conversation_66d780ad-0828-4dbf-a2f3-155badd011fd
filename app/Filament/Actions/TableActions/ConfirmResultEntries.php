<?php

namespace App\Filament\Actions\TableActions;

use App\Models\Audit;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;

class ConfirmResultEntries extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->label('Confirm Results')
            ->color('orange')
            ->requiresConfirmation()
            ->modalDescription("Record Marker results for onward updates.")
            ->modalWidth(MaxWidth::ExtraSmall);

        $this->action(function (array $arguments): void {
            $results = $this->arguments['application']->results;

            foreach ($results as $result) $result->update(['iec_score' => $result->moderator_score ?? $result->marker_score]);

            Audit::create(['application_id' => $this->arguments['application']->id, 'description' => 'Moderator scores accepted']);

            $this->successNotificationTitle("Moderator scores accepted.");
            $this->success();
        });
    }


}
