<?php

namespace App\Filament\Actions\TableActions;

use App\Notifications\ApplicationNotification;
use App\Notifications\ResultsPublishedNotification;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\Notification;

class NotifyPublishedResults extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->label('Notify')
            ->color('success')
            ->icon('heroicon-o-envelope')
            ->requiresConfirmation()
            ->modalDescription("Notify candidates about published results.")
            ->modalIconColor('warning')
            ->modalWidth(MaxWidth::ExtraSmall);

        $this->action(function (array $arguments): void {
            $type = $this->arguments['type'];
            $application = $this->arguments['application'];
            if ($type == 'entrance') $records = $application->submissions()->whereNull("notified")->whereNotNull('status')->get();
            if ($type == 'students') $records = $application->users();

            Notification::send($records->filter(), new ResultsPublishedNotification($application));

            $this->successNotificationTitle("Applicants successfully notified");
            $this->success();
        });
    }


}
