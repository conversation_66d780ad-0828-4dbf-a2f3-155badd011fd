<?php

namespace App\Filament\Actions\TableActions;

use App\Models\Audit;
use App\Models\RemarkResult;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;

class AcceptRemarkModeratorResults extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->label('Accept Results')
            ->color('gray')
            ->requiresConfirmation()
            ->modalDescription("Record Moderator results under IEC for onward updates.")
            ->modalWidth(MaxWidth::ExtraSmall);

        $this->action(function (array $arguments): void {
            $results = RemarkResult::query()->get();
            $iecScore = NULL;

            foreach ($results as $remarkResult) {
                $iecScore = $remarkResult->result->remark_score ?? $remarkResult->moderator_score ?? $remarkResult->marker_score;
                $remarkResult->result->update(['remark_score' => $iecScore]);
            }

            $applicationId = $results->first()?->result?->application_id;
            Audit::create(['application_id' => $applicationId, 'description' => 'Moderator scores accepted']);

            $this->successNotificationTitle("Moderator scores accepted.");
            $this->success();
        });
    }
}
