<?php

namespace App\Filament\Actions\TableActions;

use App\Notifications\ApplicationNotification;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\Notification;

class NotifyEntranceApplicants extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->label('Notify')
            ->color('success')
            ->icon('heroicon-o-envelope')
            ->requiresConfirmation()
            ->modalDescription("Notify Entrance Applicants. This will skip those who have already been notified")
            ->modalIconColor('warning')
            ->modalWidth(MaxWidth::ExtraSmall);

        $this->action(function (array $arguments): void {
            $submissions = $this->arguments['application']->submissions()->whereNull("notified")->whereNotNull('status')->get();

            Notification::send($submissions, new ApplicationNotification());

            $this->successNotificationTitle("Applicants successfully notified");
            $this->success();
        });
    }


}
