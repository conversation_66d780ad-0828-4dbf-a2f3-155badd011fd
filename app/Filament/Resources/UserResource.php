<?php

namespace App\Filament\Resources;

use App\Filament\Exports\UserExporter;
use App\Filament\Imports\UserImporter;
use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ExportBulkAction;
use Filament\Tables\Columns\Column;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static bool $isDiscovered = false;
    protected static ?string $navigationLabel = 'Students';

    protected static ?string $navigationGroup = 'User Accounts';

    protected static ?string $label = 'Student';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('first_name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('last_name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('email')
                    ->email()
                    ->required()
                    ->maxLength(255),
                PhoneInput::make('phone')->required()->validateFor('auto')->initialCountry('GH'),
                Forms\Components\Hidden::make('password')
                    ->default(bcrypt(Str::random(40))),
                Forms\Components\TextInput::make('student_id')
                    ->maxLength(255)
                    ->required(),
                Forms\Components\Select::make('start_level')
                    ->options(array_flip(levelSlugs()))
                    ->required()
            ]);
    }

    public static function table(Table $table): Table
    {
        $user = request()->user();
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('#')
                    ->rowIndex(),
                Tables\Columns\TextColumn::make('first_name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('last_name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                // Tables\Columns\TextColumn::make('email_verified_at')
                //     ->formatStateUsing(fn(string $state): string => self::statusInfo($state))
                //     ->label('Email verification')
                //     ->default('Pending')
                //     ->sortable(),
                Tables\Columns\TextColumn::make('student_id')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            // ->filters([
            //     Tables\Filters\TrashedFilter::make(),
            // ])
            ->actions([
                Tables\Actions\EditAction::make(),
                // Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                ExportBulkAction::make()
                    ->exporter(UserExporter::class)
            ])
            ->headerActions([
                Tables\Actions\ImportAction::make()
                    ->importer(UserImporter::class)
                    ->hidden(!$user->hasAnyRole('iec'))
            ])
            ->recordAction(null)
            ->modifyQueryUsing(fn(Builder $query) => $query->students());
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            // 'create' => Pages\CreateCourse::route('/create'),
            // 'view' => Pages\ViewCourse::route('/{record}'),
            // 'edit' => Pages\EditCourse::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    protected static function statusInfo($email_verified_at = null)
    {
        return $email_verified_at ?? "Pending";
    }

}
