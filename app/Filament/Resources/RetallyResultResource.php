<?php

namespace App\Filament\Resources;

use App\Enums\RemarkRetallyStatus;
use App\Filament\Resources\RetallyResultResource\Pages;
use App\Models\RetallyResult;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;

class RetallyResultResource extends Resource
{
    protected static ?string $model = RetallyResult::class;

    protected static ?string $navigationGroup = 'Examination Management';
    protected static ?string $label = "Retally Requests";

    public static function getNavigationBadge(): ?string
    {
        return (string) RetallyResult::where('status', 'cancel_requested')->count(); // Count of canceled remark results
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'danger';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('status')
                    ->options(RemarkRetallyStatus::getKeyValues()),
                Forms\Components\Select::make('result_id')
                    ->label('Course')
                    ->required()
                    ->options(function ($record, $state) {
                        return $record->result->application->courses()->pluck('name', 'id');
                    })
                    ->afterStateUpdated(function ($state, $record, Set $set) {
                        $selectedResult = $record->result->user->results->where('course_id', $state)->first();
                        $set('result_id', $selectedResult->id);
                    })
            ])->columns(1);
    }


    public static function table(Table $table): Table
    {
        $user = request()->user();
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('#')
                    ->rowIndex(),
                Tables\Columns\TextColumn::make('result.student_id')
                    ->label('Student ID')
                    ->searchable()
                    ->copyable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('result.application.name')
                    ->label('Exam'),
                Tables\Columns\TextColumn::make('result.course.name')
                    ->label('Course')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'cancel_requested' => 'warning',
                        'cancelled' => 'danger',
                        'pending' => 'gray',
                        'confirmed' => 'success',
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('result.iec_score')
                    ->label('Original Score'),
                Tables\Columns\TextInputColumn::make('score')
                    ->label('New Score')
                    ->disabled(fn($record) => !canEditRetally($record))
                    ->rules(['required', 'numeric', 'max:100']),
                Tables\Columns\ToggleColumn::make('isPublished')
                    ->label('Accepted')
                    ->hidden(!$user->hasAnyRole('gsl'))
                    ->disabled(fn($record) => !(bool)$record->score)
                    ->afterStateUpdated(function ($record, $state) {
                        $record->result->update(['retally_score' => $state ? $record->score : NULL]);
                    })
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(RemarkRetallyStatus::getKeyValues()), // Use enum for filter options
                Tables\Filters\SelectFilter::make('level')
                    ->relationship('result.level', 'name'),
                Tables\Filters\SelectFilter::make('course')
                    ->relationship('result.course', 'name', function ($livewire, $query) {
                        $levelFilter = $livewire->getTableFilterState('level');
                        if($levelFilter && !empty($levelFilter['value'])) {
                            return $query->where('level_id', $levelFilter['value']);
                        }
                        return $query;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->headerActions([
                Tables\Actions\Action::make('confirm_scores') // Renamed to confirm_scores for generic use
                    ->label('Confirm Results')
                    ->action(function () {
                        $user = request()->user(); // Get the logged-in user
                        $records = getRecords();

                        Log::info('Confirming scores for user: ' . $user->id, ['records_count' => $records->count()]);

                        $confirmCount = 0;
                        $unconfirmedRecords = [];
                        foreach ($records as $record) {
                            if ($user->hasAnyRole('admin')) {
                                if (!is_null($record->score)) {
                                    $record->retally_score_confirmed = true;
                                    $record->reactivate_retally = false; // Reset the reactivation flag
                                    $record->save();
                                    $confirmCount++;
                                } else {
                                    $unconfirmedRecords[] = $record->result->student_id;
                                }
                            }
                        }
                        if(!empty($unconfirmedRecords)) {
                            Notification::make()
                            ->title('Fail count: ' . count($unconfirmedRecords) . '. Please fill entry for student IDs: ' . implode(', ', $unconfirmedRecords))
                            ->danger()
                            ->send();
                        }
                        if($confirmCount > 0) {
                            Notification::make()
                                ->title('Results confirmation successful. Total confirmed: ' . $confirmCount)
                                ->success()
                                ->send();
                        }
                    })
                    ->requiresConfirmation()
                    ->color('success')
                    ->hidden(fn() => !request()->user()->hasAnyRole('admin')), // Show only for markers
            ])
            ->bulkActions([
                ExportBulkAction::make()->exports([
                    ExcelExport::make()
                        ->fromTable()
                        ->except(['#'])
                        ->withFilename("RemarkRequests"),
                ])
                ->deselectRecordsAfterCompletion(),
                Tables\Actions\BulkAction::make('reactivate_retally')
                    ->hidden(!$user->hasAnyRole('iec')) //Show only for IEC
                    ->label('Reactivate Retally')
                    ->icon('heroicon-o-arrow-uturn-left')
                    ->deselectRecordsAfterCompletion()
                    ->requiresConfirmation()
                    ->action(function (Collection $records) {
                        $records->each->update(['reactivate_retally' => true]);
                        Notification::make()
                            ->title('Retally reactivation successful.')
                            ->success()
                            ->icon('heroicon-o-check')
                            ->send();
                    }),

            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageRetallyResults::route('/'),
        ];
    }

    public static function canViewForRecord($ownerRecord, string $pageClass): bool
    {
        return request()->user()->hasAnyRole('admin', 'iec');
    }

    public static function canViewAny(): bool
    {
        return request()->user()->hasAnyRole('iec', 'admin');
    }
}


function canEditRetally($record): bool
{
        return request()->user()->hasAnyRole('iec') ||
            $record->reactivate_retally ||
            !$record->retally_score_confirmed;
}

function getRecords($query = null)
{
    return $query ?? RetallyResult::query()->get();
}