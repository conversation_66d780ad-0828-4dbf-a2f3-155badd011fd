<?php

namespace App\Filament\Resources\RetallyResultResource\Pages;

use App\Filament\Resources\RetallyResultResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageRetallyResults extends ManageRecords
{
    protected static string $resource = RetallyResultResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}
