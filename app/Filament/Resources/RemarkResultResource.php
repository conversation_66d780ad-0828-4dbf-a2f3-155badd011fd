<?php

namespace App\Filament\Resources;

use App\Enums\RemarkRetallyStatus;
use App\Filament\Actions\RemarkResults\AddMarks;
use App\Filament\Actions\Results\AssignMarker;
use App\Filament\Actions\Results\AssignModerator;
use App\Filament\Actions\TableActions\AcceptRemarkModeratorResults;
use App\Filament\Resources\RemarkResultResource\Pages;
use App\Models\RemarkResult;
use App\Models\Setting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use Tapp\FilamentValueRangeFilter\Filters\ValueRangeFilter;

class RemarkResultResource extends Resource
{
    protected static ?string $model = RemarkResult::class;

    protected static ?string $navigationGroup = 'Examination Management';
    protected static ?string $label = "Remark Requests";

    public static function getNavigationBadge(): ?string
    {
        return (string) RemarkResult::where('status', 'cancel_requested')->count(); // Count of canceled remark results
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'danger';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('status')
                    ->options(RemarkRetallyStatus::getKeyValues()),
                Forms\Components\Select::make('result_id')
                    ->label('Course')
                    ->required()
                    ->options(function ($record, $state) {
                        return $record->result->application->courses()->pluck('name', 'id');
                    })
                    ->afterStateUpdated(function ($state, $record, Set $set) {
                        $selectedResult = $record->result->user->results->where('course_id', $state)->first();
                        $set('result_id', $selectedResult->id);
                    })
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        $user = request()->user();
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('#')
                    ->rowIndex(),
                Tables\Columns\TextColumn::make('result.student_id')
                    ->label('Student ID')
                    ->searchable()
                    ->copyable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('result.application.name')
                    ->sortable()
                    ->label('Examination'),
                Tables\Columns\TextColumn::make('result.course.name')
                    ->label('Course')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'cancel_requested' => 'warning',
                        'cancelled' => 'danger',
                        'pending' => 'gray',
                        'confirmed' => 'success',
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('marker.full_name')
                    ->label('Marker Name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('moderator.full_name')
                    ->label('Moderator Name')
                    ->hidden($user->hasAnyRole('marker'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('result.iec_score')
                    ->label('Original Score'),
                ($user->hasAnyRole('marker') ?
                    Tables\Columns\TextInputColumn::make('marker_score')
                        ->label('Marker Score')
                        ->sortable()
                        ->disabled(fn($record) => !canEditMarker($record))
                        ->rules(['required', 'numeric', 'max:100']) :
                    Tables\Columns\TextColumn::make('marker_score')
                        ->sortable()
                        ->label('Marker Score')),
                ($user->hasAnyRole('moderator') ?
                    Tables\Columns\TextInputColumn::make('moderator_score')
                        ->label('Moderator Score')
                        ->hidden($user->hasAnyRole('marker'))
                        ->sortable()
                        ->disabled(fn($record) => !canEditModerator($record))
                        ->rules(['required', 'numeric', 'max:100']) :
                    Tables\Columns\TextColumn::make('moderator_score')
                    ->sortable()
                    ->hidden($user->hasAnyRole('marker'))
                    ->label('Moderator Score')),
                $user->hasAnyRole('iec') ?
                    Tables\Columns\TextInputColumn::make('iec_score')
                    // ->disabled(fn($record) => $record->result->isPublished || !$user->hasAnyRole('iec'))
                    ->extraAttributes(['class' => 'small-input'])
                    ->label('Final Score')
                    ->state(function ($record) {
                        return $record->result->remark_score;
                    })
                    ->updateStateUsing(function ($record, $state) {
                        return null;
                    })
                    ->beforeStateUpdated(function ($record, $state) {
                        // $message = is_null($record->getOriginal('iec_score')) ?
                        //     "set to " :
                        //     "changed from " . $record->getOriginal('iec_score') . " to ";

                        // Audit::create([
                        //     'application_id' => $record->application_id,
                        //     'description' => $record->student_id . " — final score " . $message . $state
                        // ]);
                        if ($state) {
                            $record->result->update(['remark_score' => $state]);
                        }
                    })
                    ->rules(['required', 'numeric', 'max:100']) :
                    Tables\Columns\TextColumn::make('iec_score')
                    ->state(function ($record) {
                        return $record->result->remark_score;
                    })
                    ->extraAttributes(['class' => 'small-input'])
                    ->label('Final Score'),
                Tables\Columns\ToggleColumn::make('isPublished')
                    ->label('Accepted')
                    ->hidden(!$user->hasAnyRole('gsl'))
                    ->disabled(fn($record) => !(bool)$record->score)
                    ->afterStateUpdated(function ($record, $state) {
                        $record->result->update(['remark_score' => $state ? $record->score : NULL]);
                    })
            ])
            ->headerActions([
                AcceptRemarkModeratorResults::make('Accept Moderator Results')
                    ->hidden(!$user->hasAnyRole('iec'))
                    ->arguments([]),
                Tables\Actions\Action::make('confirm_scores') // Renamed to confirm_scores for generic use
                    ->label('Confirm Results')
                    ->action(function () {
                        $user = request()->user(); // Get the logged-in user
                        $records = getFilteredRecords($user)->get(); // Execute the query here

                        Log::info('Confirming scores for user: ' . $user->id, ['records_count' => $records->count()]);

                        $confirmCount = 0;
                        $unconfirmedRecords = [];
                        foreach ($records as $record) {
                            if ($user->hasAnyRole('marker')) {
                                if (!is_null($record->marker_score)) {
                                    $record->marker_score_confirmed = true;
                                    $record->reactivate_marker = false; // Reset the reactivation flag
                                    $record->save();
                                    $confirmCount++;
                                } else {
                                    $unconfirmedRecords[] = $record->result->student_id;
                                }
                            } elseif ($user->hasAnyRole('moderator')) {
                                if (!is_null($record->moderator_score)) {
                                    $record->moderator_score_confirmed = true;
                                    $record->reactivate_moderator = false; // Reset the reactivation flag
                                    $record->save();
                                    $confirmCount++;
                                } else {
                                    $unconfirmedRecords[] = $record->result->student_id;
                                }
                            }
                        }
                        if(!empty($unconfirmedRecords)) {
                            Notification::make()
                            ->title('Fail count: ' . count($unconfirmedRecords) . '. Please fill entry for student IDs: ' . implode(', ', $unconfirmedRecords))
                            ->danger()
                            ->send();
                        }
                        if($confirmCount > 0) {
                            Notification::make()
                                ->title('Results confirmation successful. Total confirmed: ' . $confirmCount)
                                ->success()
                                ->send();
                        }
                    })
                    ->requiresConfirmation()
                    ->color('success')
                    ->hidden(fn() => !request()->user()->hasAnyRole('marker', 'moderator')), // Show only for markers and moderators
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options(RemarkRetallyStatus::getKeyValues()) // Use enum for filter options
                    ->default('pending'),
                Tables\Filters\SelectFilter::make('level')
                    ->relationship('result.level', 'name'),
                Tables\Filters\SelectFilter::make('course')
                    ->relationship('result.course', 'name', function ($livewire, $query) {
                        $levelFilter = $livewire->getTableFilterState('level');
                        if($levelFilter && !empty($levelFilter['value'])) {
                            return $query->where('level_id', $levelFilter['value']);
                        }
                        return $query;
                    }),
                ValueRangeFilter::make('marker_score'),
                ValueRangeFilter::make('moderator_score'),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->hidden(fn ($record) => !request()->user()->hasAnyRole('iec', 'admin')),
            ])
            ->bulkActions([
                AssignMarker::make('Assign marker')
                    ->hidden(!$user->hasRole('admin'))
                    ->label('Assign Marker')
                    ->color('success')
                    ->icon('heroicon-o-arrow-uturn-right')
                    ->deselectRecordsAfterCompletion(),
                AssignModerator::make('Assign moderator')
                    ->hidden(!$user->hasRole('admin'))
                    ->label('Assign Moderator')
                    ->color('gray')
                    ->deselectRecordsAfterCompletion()
                    ->icon('heroicon-o-arrow-uturn-right'),
                ExportBulkAction::make()->exports([
                    ExcelExport::make()
                        ->fromTable()
                        ->except(['#'])
                        ->withFilename("RemarkRequests"),
                    ])
                    ->deselectRecordsAfterCompletion(),
                Tables\Actions\BulkAction::make('reactivate_marker')
                    ->hidden(!$user->hasRole('admin'))
                    ->label('Reactivate Marker')
                    ->icon('heroicon-o-arrow-uturn-left')
                    ->deselectRecordsAfterCompletion()
                    ->requiresConfirmation()
                    ->action(function (Collection $records) {
                        $records->each->update(['reactivate_marker' => true]);
                        Notification::make()
                            ->title('Marker reactivation successful.')
                            ->success()
                            ->icon('heroicon-o-check')
                            ->send();
                    }),
                Tables\Actions\BulkAction::make('reactivate_moderator')
                    ->hidden(!$user->hasRole('admin'))
                    ->label('Reactivate Moderator')
                    ->icon('heroicon-o-arrow-uturn-left')
                    ->deselectRecordsAfterCompletion()
                    ->requiresConfirmation()
                    ->action(function (Collection $records) {
                        $records->each->update(['reactivate_moderator' => true]);
                        Notification::make()
                            ->title('Moderator reactivation successful.')
                            ->success()
                            ->icon('heroicon-o-check')
                            ->send();
                    }),
                AddMarks::make('Add marks')
                    ->color('gray')
                    ->hidden(!($user->hasAnyRole('iec')))
                    ->deselectRecordsAfterCompletion(),
            ])
            ->checkIfRecordIsSelectableUsing(fn ($record) => $record->status === 'pending')
            ->modifyQueryUsing(function (Builder $query) {
                return getFilteredRecords(request()->user(), $query); // Return the query builder
            });
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageRemarkResults::route('/'),
        ];
    }

    public static function canViewForRecord($ownerRecord, string $pageClass): bool
    {
        return request()->user()->hasAnyRole('admin', 'iec', 'marker', 'moderator');
    }
}

function canEditMarker($record): bool
{
    $hasModeratorEdits = !is_null($record->moderator_score);

    return $record->reactivate_marker || (!$hasModeratorEdits
        && !$record->marker_score_confirmed);
}

function canEditModerator($record): bool
{
    $hasMarkerEdits = !is_null($record->marker_score);

    return $record->reactivate_moderator || (
        $hasMarkerEdits
        // && isMarkerScoreWithinThreshold($record)
        && $record->marker_score_confirmed
        && !$record->moderator_score_confirmed
    );
}

function getFilteredRecords($user, $query = null)
{
    $filteredRecords = $query ?? RemarkResult::query();

    if ($user->hasAnyRole('marker')) {
        $filteredRecords->where('marker_id', $user->id);
    } elseif ($user->hasAnyRole('moderator')) {
        $filteredRecords->where('moderator_id', $user->id);
    }

    return $filteredRecords; // Return the query builder instead of executing it
}

function isMarkerScoreWithinThreshold($record)
{
    $score = $record->marker_score;
    if(!$score) return false;
    $thresholdSlugs = [
        'default' => 'moderator_score_thresholds',
    ];

    $thresholdSlug = $thresholdSlugs['default'];
    $thresholdsSetting = Setting::where('slug', $thresholdSlug)->first();
    $thresholdsValue = $thresholdsSetting ? $thresholdsSetting->value : '';
    $thresholds = array_map('trim', explode(',', $thresholdsValue));
    if(!$thresholds) return false;
    $thresholds = array_map(function($range) {
        return array_map('intval', explode('-', $range));
    }, $thresholds);

    foreach ($thresholds as $range) {
        if ($score >= $range[0] && $score <= $range[1]) {
            return true;
        }
    }
    return false;
}