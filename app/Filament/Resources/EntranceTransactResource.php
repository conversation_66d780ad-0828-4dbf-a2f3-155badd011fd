<?php

namespace App\Filament\Resources;

use App\Filament\Exports\EntranceTransactionExporter;
use App\Filament\Exports\OrderExporter;
use App\Filament\Resources\OrderResource\Pages;
use App\Filament\Resources\OrderResource\RelationManagers;
use App\Models\Application;
use App\Models\Order;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;

class EntranceTransactResource extends Resource
{
    protected static ?string $model = Order::class;

    public static function canCreate(): bool
    {
        return false;
    }

    protected static ?string $label = "Entrance Examination";

    protected static ?string $navigationGroup = 'Transactions';

    public static function table(Table $table): Table
    {
        ini_set('memory_limit', '256M');
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('#')
                    ->rowIndex(),
                Tables\Columns\TextColumn::make('cred.full_name')->label('Full name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('application.name'),
                Tables\Columns\TextColumn::make('cred.email')
                    ->label("email")
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('reference')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('amount')
                    ->numeric(2)
                    ->formatStateUsing(fn($state) => number_format($state, 2, '.', ''))
                    ->sortable(),
                Tables\Columns\TextColumn::make('charges')
                    ->numeric(2)
                    ->formatStateUsing(fn($state) => number_format($state, 2, '.', ''))
                    ->sortable(),
                Tables\Columns\TextColumn::make('description')
                    ->searchable()
                    ->formatStateUsing(fn(string $state) => nl2br($state))
                    ->html(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Date')
                    ->date()
                    ->sortable(),
            ])
            ->modifyQueryUsing(fn(Builder $query) => $query->entranceTransactions())
            ->filters([
                Tables\Filters\SelectFilter::make('application_id')
                    ->label('Application')
                    ->options(fn(): array => Application::where('transaction_id', 1)->latest('year')->pluck('name', 'id')->all())
            ])
            ->bulkActions([
                // ExportBulkAction::make()->exports([
                //     ExcelExport::make()
                //         ->fromTable()
                //         ->modifyQueryUsing(fn ($query) => Order::where('application_id', 6))
                //         ->except('#')
                //         ->withFilename("EntranceTransactions")
                // ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageEntranceTransactions::route('/'),
        ];
    }

    public static function canViewAny(): bool
    {
        return request()->user()->hasAnyRole('iec', 'accountant');
    }
}
