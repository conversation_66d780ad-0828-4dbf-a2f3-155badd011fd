<?php

namespace App\Filament\Resources\RemarkResultResource\Pages;

use App\Filament\Resources\RemarkResultResource;
use Filament\Resources\Pages\ManageRecords;

class ManageRemarkResults extends ManageRecords
{
    protected static string $resource = RemarkResultResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}
