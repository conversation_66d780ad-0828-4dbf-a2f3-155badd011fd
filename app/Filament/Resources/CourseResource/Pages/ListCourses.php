<?php

namespace App\Filament\Resources\CourseResource\Pages;

use App\Filament\Resources\CourseResource;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListCourses extends ListRecords
{
    protected static string $resource = CourseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        return [
            'PLC 1' => Tab::make()
                ->modifyQueryUsing(fn(Builder $query) => $query->where('level_id', 1)),
            'PLC 2' => Tab::make()
                ->modifyQueryUsing(fn(Builder $query) => $query->where('level_id', 2)),
            'Post-Call' => Tab::make()
                ->modifyQueryUsing(fn(Builder $query) => $query->where('level_id', 3)),
            // 'Entrance' => Tab::make()
            //     ->modifyQueryUsing(fn(Builder $query) => $query->where('level_id', 4)),
            'all' => Tab::make(),
        ];
    }
}
