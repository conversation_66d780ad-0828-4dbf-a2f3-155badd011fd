<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SubmissionResource\Pages;
use App\Filament\Resources\SubmissionResource\RelationManagers;
use App\FormResources\FormResource;
use App\Infolists\Components\FileList;
use App\Models\Application;
use App\Models\Submission;
use Filament\Forms;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Form;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use function PHPUnit\Framework\isNull;

class SubmissionResource extends Resource
{
    protected static ?string $model = Submission::class;

    protected static ?string $label = 'Applicants';

    protected static bool $isDiscovered = false;

    protected static ?string $navigationIcon = 'heroicon-o-document-check';

    protected static ?string $recordTitleAttribute = 'full_name';

    protected static ?string $navigationGroup = 'Entrance';

    protected static function statusInfo($id = null)
    {
        return ['Rejected', 'Approved'][$id] ?? 'Pending';
    }

    protected static function statusColors($id)
    {
        return ['danger', 'success'][$id] ?? 'gray';
    }

    public static function form(Form $form): Form
    {
        return FormResource::entrance($form)
            ->columns(2);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('family_name')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('first_name')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('middle_name')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('full_name'),
                Tables\Columns\TextColumn::make('application.year')
                    ->label('Year')
                    ->sortable(),
                Tables\Columns\TextColumn::make('gender'),
                Tables\Columns\TextColumn::make('dob')
                    ->label('Date of Birth')
                    ->date(),
                Tables\Columns\TextColumn::make('nationality')
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('phone')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('institution')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('status')
                    ->formatStateUsing(fn(string $state): string => self::statusInfo($state))
                    ->default('Pending')
                    ->badge()
                    ->color(fn(string $state): string => self::statusColors($state)),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('application_id')
                    ->label('Application')
                    ->options(fn(): array => Application::query()->where('transaction_id', 1)->latest('year')->pluck('name', 'id')->all())
                    ->default(Application::where('transaction_id', 1)->latest('year')->first()->id)
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
//                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('Approve')
                        ->requiresConfirmation()
                        ->action(fn(Collection $records) => $records->each->approve())
                        ->deselectRecordsAfterCompletion(),
                    Tables\Actions\BulkAction::make('Reject')
                        ->requiresConfirmation()
                        ->action(fn(Collection $records) => $records->each->reject())
                        ->deselectRecordsAfterCompletion(),
                    Tables\Actions\BulkAction::make('Make Pending')
                        ->requiresConfirmation()
                        ->action(fn(Collection $records) => $records->each->markAsPending())
                        ->deselectRecordsAfterCompletion()
                ])->label('Approve Action'),
                Tables\Actions\BulkActionGroup::make([
                    ExportBulkAction::make()
                ])
            ])
            ->emptyStateActions([
//                Tables\Actions\CreateAction::make(),
            ])
            ->defaultSort('application.year', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make()
                    ->schema([
                        Infolists\Components\TextEntry::make('full_name'),
                        Infolists\Components\TextEntry::make('email'),
                        Infolists\Components\TextEntry::make('gender'),
                        Infolists\Components\TextEntry::make('dob')
                            ->date(),
                        Infolists\Components\TextEntry::make('nationality'),
                        Infolists\Components\TextEntry::make('email'),
                        Infolists\Components\TextEntry::make('phone'),
                        Infolists\Components\TextEntry::make('institution'),
                        Infolists\Components\TextEntry::make('status')
                            ->formatStateUsing(fn(string $state): string => self::statusInfo($state))
                            ->color(fn(string $state): string => self::statusColors($state))
                            ->badge()
                            ->default('Pending'),
                        Infolists\Components\Actions::make([
                            Infolists\Components\Actions\Action::make('approve')
                                ->color('success')
                                ->requiresConfirmation()
                                ->hidden(fn(Submission $record) => $record->status === 1)
                                ->action(fn(Submission $record) => $record->approve()),
                            Infolists\Components\Actions\Action::make('reject')
                                ->color('danger')
                                ->requiresConfirmation()
                                ->hidden(fn(Submission $record) => $record->status === 0)
                                ->action(fn(Submission $record) => $record->reject()),
                            Infolists\Components\Actions\Action::make('Make pending')
                                ->color('gray')
                                ->requiresConfirmation()
                                ->hidden(fn(Submission $record) => is_null($record->status))
                                ->action(fn(Submission $record) => $record->markAsPending()),
                        ])
                            ->verticallyAlignEnd()
                    ])
                    ->columnSpan(1)
                    ->columns(2),
                Infolists\Components\Section::make()
                    ->schema([
                        FileList::make('files')->label('Uploaded Files')
                    ])->label('ll')
                    ->columnSpan(1),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubmissions::route('/'),
            'create' => Pages\CreateSubmission::route('/create'),
            'view' => Pages\ViewSubmission::route('/{record}'),
//            'edit' => Pages\EditSubmission::route('/{record}/edit'),
        ];
    }
}
