<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Pages\ListRecords\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->hidden(!request()->user()->hasAnyRole('iec')),
        ];
    }

    public function getTabs(): array
    {
        return [
            // 'all' => Tab::make(),
            // 'PLC 1' => Tab::make()
            //     ->modifyQueryUsing(fn(Builder $query) => $query->where('level_id', 1)),
            // 'PLC 2' => Tab::make()
            //     ->modifyQueryUsing(fn(Builder $query) => $query->where('level_id', 2)),
            // 'Post-Call' => Tab::make()
            //     ->modifyQueryUsing(fn(Builder $query) => $query->where('level_id', 3)),
        ];
    }
}
