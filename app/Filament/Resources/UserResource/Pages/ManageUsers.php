<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Imports\UserImporter;
use App\Filament\Resources\UserResource;
use App\Notifications\WelcomeSetPasswordNotification;
use Closure;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

class ManageUsers extends ManageRecords
{
    protected static string $resource = UserResource::class;

    // protected function getHeaderActions(): array
    // {
    //     return [
    //         Actions\CreateAction::make()
    //             ->hidden(!request()->user()->hasAnyRole('iec'))
    //             ->mutateFormDataUsing(function (array $data): array {
    //                 return [...$data, 'password' => bcrypt(Str::random(60))];
    //             })
    //             ->after(function ($action, $record) {
    //                 $token = app('auth.password.broker')->createToken($record);
    //                 $notification = new WelcomeSetPasswordNotification($token);
    //                 $notification->user = $record;
    //                 $record->notify($notification);
    //             }),
    //         Actions\ImportAction::make()
    //             ->importer(UserImporter::class)
    //             ->label('Import Students')
    //     ];
    // }
}
