<?php

namespace App\Filament\Resources\EVoucherResource\Pages;

use App\Filament\Resources\EVoucherResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListVouchers extends ListRecords
{
    protected static string $resource = EVoucherResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            \pxlrbt\FilamentExcel\Actions\Pages\ExportAction::make()
                ->exports([
                    \pxlrbt\FilamentExcel\Exports\ExcelExport::make()
                        ->fromTable()
                        ->except('#')
                        ->withFilename('EVouchers'),
                ]),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All Vouchers'),
            'pending' => Tab::make('Pending')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'pending'))
                ->badge(fn () => \App\Models\EVoucher::where('status', 'pending')->count()),
            'paid' => Tab::make('Paid')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'paid'))
                ->badge(fn () => \App\Models\EVoucher::where('status', 'paid')->count()),
            'redeemed' => Tab::make('Redeemed')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'redeemed'))
                ->badge(fn () => \App\Models\EVoucher::where('status', 'redeemed')->count()),
            'expired' => Tab::make('Expired')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'expired'))
                ->badge(fn () => \App\Models\EVoucher::where('status', 'expired')->count()),
        ];
    }
}
