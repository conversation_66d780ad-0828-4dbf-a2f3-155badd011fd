<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EntranceResource\Pages;
use App\Filament\Resources\EntranceResource\RelationManagers;
use App\Filament\Resources\ResourceUtilities\ResourceUtilities;
use App\Models\Application;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class EntranceResource extends Resource
{
    protected static ?string $model = Application::class;
    protected static ?string $navigationGroup = "Examination Management";

    protected static ?string $label = "Entrance Examination";
    protected static ?string $pluralLabel = "Entrance Examination";

    public static function form(Form $form): Form
    {
        return ResourceUtilities::applicationResource($form);
    }

    public static function table(Table $table): Table
    {
        return ResourceUtilities::applicationTable($table);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageEntrance::route('/'),
            'view' => Pages\ViewEntrance::route('/{record}'),
        ];
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\SubmissionsRelationManager::class,
            RelationManagers\CandidateRelationManager::class,
            RelationManagers\PwdsRelationManager::class,
            RelationManagers\AssignRelationManager::class,
            // RelationManagers\ResultsRelationManager::class,
            RelationManagers\ScriptsRelationManager::class,
            RelationManagers\EntranceResultsRelationManager::class,
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->where('transaction_id', 1);
    }
}
