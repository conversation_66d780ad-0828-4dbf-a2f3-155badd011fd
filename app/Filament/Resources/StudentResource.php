<?php

namespace App\Filament\Resources;

use App\Filament\Actions\Students\AssignHall;
use App\Filament\Actions\Students\MarkEligible;
use App\Filament\Resources\StudentResource\Pages;
use App\Models\User;
use App\Models\Venue;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use STS\FilamentImpersonate\Tables\Actions\Impersonate;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;

class StudentResource extends Resource
{
    protected static ?string $model = User::class;
    protected static ?string $navigationLabel = 'Student Management';
    protected static ?string $modelLabel = "Student";
    // protected static ?int $navigationSort = -11;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        $password = bcrypt(Str::random(40));
        return $form
            ->schema([
                Forms\Components\TextInput::make('first_name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('last_name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('email')
                    ->email()
                    ->required()
                    ->maxLength(255),
                PhoneInput::make('phone')->required()->validateFor('auto')->initialCountry('GH'),
                Forms\Components\Select::make('venue_id')->options(fn() => Venue::pluck('name', 'id'))->placeholder('Select Center')->label('Center')->required(),
                Forms\Components\TextInput::make('ghana_card')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Toggle::make('is_eligible')
                    ->label('Eligible for Exams')
                    ->helperText('Mark student as eligible for upcoming exams'),
                Forms\Components\Textarea::make('eligibility_notes')
                    ->maxLength(255)
                    ->columnSpanFull(),
                // Forms\Components\Hidden::make('password')
                //     ->default($password),
                Forms\Components\TextInput::make('student_id')
                    ->maxLength(255)
                    ->required(),
                Forms\Components\Select::make('start_level')
                    ->options(levelNames())
                    ->required()
            ]);
    }

    public static function table(Table $table): Table
    {
        $user = request()->user();
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('student_id')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('first_name')
                    ->hidden(!$user->hasAnyRole('iec'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('last_name')
                    ->hidden(!$user->hasAnyRole('iec'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('start_level')
                    ->formatStateUsing(fn(string $state) => levelNames()[$state] ?? '')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->searchable(),
                Tables\Columns\TextColumn::make('ghana_card')
                    ->searchable(),
                Tables\Columns\ToggleColumn::make('is_eligible')
                    ->label('Eligible')
                    ->disabled()
                    ->sortable()
                    ->afterStateUpdated(function ($record, $state) {
                        if ($state) {
                            $record->eligibility_date = now();
                        } else {
                            $record->eligibility_date = null;
                        }
                        $record->save();
                    }),
                Tables\Columns\TextColumn::make('eligibility_date')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('venue.location')
                    ->label('Location')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('venue.name')
                    ->label('Center')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('venue_location')
                ->label('Location')
                ->options(Venue::query()->pluck('location', 'location')->unique())
                ->query(function ($query, $data) {
                    if ($data['value'] === null) {
                        return $query;
                    }

                    return $query->whereHas('venue', function ($venueQuery) use ($data) {
                        $venueQuery->where('location', $data['value']);
                    });
                })
                ->placeholder('All Locations'),
                
                // Add the eligibility filter
                Tables\Filters\SelectFilter::make('is_eligible')
                    ->label('Exam Eligibility Status')
                    ->options([
                        '1' => 'Eligible',
                        '0' => 'Not Eligible',
                    ])
                    ->placeholder('All Students')
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Impersonate::make()
            ])
            ->bulkActions([
                ExportBulkAction::make(),
                AssignHall::make('assign-hall')
                   ->deselectRecordsAfterCompletion(),
                MarkEligible::make('mark-eligible')
                   ->deselectRecordsAfterCompletion(),
            ])
            ->modifyQueryUsing(fn(Builder $query) => $query->students());
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageStudents::route('/'),
        ];
    }

    public static function canViewAny(): bool
    {
        return request()->user()->hasAnyRole('iec', 'admin');
    }

    // public static function getEloquentQuery(): Builder
    // {
    //     return parent::getEloquentQuery()->where('student_id')->exists();
    // }
}
