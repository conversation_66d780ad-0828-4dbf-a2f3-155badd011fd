<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ApplicationResource\Pages;
use App\Filament\Resources\ApplicationResource\RelationManagers;
use App\Filament\Resources\ResourceUtilities\ResourceUtilities;
use App\Models\Application;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ApplicationResource extends Resource
{
    protected static ?string $model = Application::class;
    protected static ?string $label = "PLC/Post-Call Examination";
    protected static ?string $pluralLabel = "PLC/Post-Call Examination";

    // protected static ?string $navigationIcon = 'heroicon-o-adjustments-horizontal';
    protected static ?string $navigationGroup = 'Examination Management';

    public static function form(Form $form): Form
    {
        return ResourceUtilities::applicationResource($form);
    }

    public static function table(Table $table): Table
    {
        return ResourceUtilities::applicationTable($table);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageApplications::route('/'),
            'view' => Pages\ViewApplication::route('/{record}'),
        ];
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ExamCandidatesRelationManager::class,
            RelationManagers\ExamPwdsRelationManager::class,
            RelationManagers\ExamAssignRelationManager::class,
            RelationManagers\ScriptsRelationManager::class,
            RelationManagers\ExamResultRelationManager::class,
            RelationManagers\SanctionedResultsRelationManager::class,
            RelationManagers\AuditsRelationManager::class
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->where('transaction_id', '!=', 1);
    }
}
