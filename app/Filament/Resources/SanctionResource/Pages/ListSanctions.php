<?php

namespace App\Filament\Resources\SanctionResource\Pages;

use App\Filament\Resources\SanctionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSanctions extends ListRecords
{
    protected static string $resource = SanctionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
