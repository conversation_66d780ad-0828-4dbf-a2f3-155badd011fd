<?php

namespace App\Filament\Resources\StudentResource\Pages;

use App\Filament\Imports\CourseExemptionImporter;
use App\Filament\Imports\EligibilityImporter;
use App\Filament\Imports\UserImporter;
use App\Filament\Resources\StudentResource;
use App\Notifications\WelcomeSetPasswordNotification;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Support\Str;

class ManageStudents extends ManageRecords
{
    protected static string $resource = StudentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                // ->hidden(!request()->user()->hasAnyRole('iec'))
                ->mutateFormDataUsing(function (array $data): array {
                    return [...$data, 'password' => bcrypt(Str::random(60))];
                })
                ->after(function ($action, $record) {
                    $token = app('auth.password.broker')->createToken($record);
                    $notification = new WelcomeSetPasswordNotification($token);
                    $notification->user = $record;
                    $record->notify($notification);
                }),
            Actions\ImportAction::make('importStudents')
                ->importer(UserImporter::class)
                ->label('Import Students')
                // ->hidden(!request()->user()->hasAnyRole('iec'))
                ,
            Actions\ImportAction::make('importEligibleStudents')
                ->importer(EligibilityImporter::class)
                ->label('Import Eligible Students')
                ->modalHeading('Import Eligible Students')
                ->modalDescription('Upload a CSV file with student IDs and eligibility status.'),
                // ->hidden(!request()->user()->hasAnyRole('iec')),
            Actions\ImportAction::make('importCourseExemptions')
                ->label('Import Course Exemptions')
                ->importer(CourseExemptionImporter::class)
                ->chunkSize(100)
                ->maxRows(1000)
        ];
    }
}
