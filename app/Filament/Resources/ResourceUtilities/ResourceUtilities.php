<?php

namespace App\Filament\Resources\ResourceUtilities;

use Filament\Tables\Actions\BulkAction;
use App\Enums\EntranceSection;
use App\Filament\Actions\EntranceResults\AssignMarker;
use App\Filament\Actions\Results\AddMarks;
use App\Filament\Imports\ResultImporter;
use App\Infolists\Components\FileList;
use App\Infolists\Components\ImageEntry;
use App\Models\Submission;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Infolists\Infolist;
use Filament\Infolists;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use App\Filament\Actions\EntranceResults\AssignModerator;
use App\Filament\Actions\Students\AssignHall;
use Illuminate\Support\Facades\Log;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use Illuminate\Support\Facades\Storage;
use App\Models\Script;
use Filament\Tables\Actions\DeleteBulkAction;

class ResourceUtilities
{
    protected static function statusInfo($id = null)
    {
        return ['Rejected', 'Approved'][$id] ?? 'Pending';
    }

    protected static function statusColors($id)
    {
        return ['danger', 'success'][$id] ?? 'gray';
    }

    public static function EntranceInfolist(Infolist $infolist, $front = false)
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make()
                    ->schema([
                        Infolists\Components\TextEntry::make('title'),
                        Infolists\Components\TextEntry::make('family_name'),
                        Infolists\Components\TextEntry::make('first_name'),
                        Infolists\Components\TextEntry::make('middle_name'),
                        Infolists\Components\TextEntry::make('gender'),
                        Infolists\Components\TextEntry::make('dob')
                            ->date(),
                        Infolists\Components\TextEntry::make('nationality'),
                        Infolists\Components\TextEntry::make('email'),
                        Infolists\Components\TextEntry::make('venue.name')
                            ->label('Center'),
                        Infolists\Components\TextEntry::make('phone'),
                        Infolists\Components\TextEntry::make('institution'),
                        Infolists\Components\TextEntry::make('disability')
                            ->formatStateUsing(fn($state) => $state ? "Yes" : "No"),
                        Infolists\Components\TextEntry::make('pwd_type')
                            ->hidden(fn($record) => !$record->disability)
                            ->label('Disability Type'),
                        Infolists\Components\TextEntry::make('pwd_details')
                            ->hidden(fn($state, $record) => !(bool)$state || !$record->disability)
                            ->label('Disability Details'),
                        Infolists\Components\TextEntry::make('status')
                            ->formatStateUsing(fn(string $state): string => self::statusInfo($state))
                            ->color(fn(string $state): string => self::statusColors($state))
                            ->hidden($front)
                            ->badge()
                            ->default('Pending'),
                        Infolists\Components\Actions::make([
                            Infolists\Components\Actions\Action::make('approve')
                                ->color('success')
                                ->hidden(fn(Submission $record) => $record->status === 1)
                                ->action(fn(Submission $record) => $record->approve()),
                            Infolists\Components\Actions\Action::make('reject')
                                ->color('danger')
                                ->hidden(fn(Submission $record) => $record->status === 0)
                                ->action(fn(Submission $record) => $record->reject()),
                            Infolists\Components\Actions\Action::make('Make pending')
                                ->color('gray')
                                ->hidden(fn(Submission $record) => is_null($record->status))
                                ->action(fn(Submission $record) => $record->markAsPending()),
                        ])->hidden($front)
                            ->verticallyAlignEnd()
                    ])
                    ->columnSpan(2)
                    ->columns(2),
                Infolists\Components\Section::make('Uploaded Documents')
                    ->schema([
                        FileList::make('files')->label(''),
                    ])
                    ->columnSpan(2),
            ])->columns(2);
    }

    public static function adminInfolist()
    {

    }

    public static function applicationResource(Form $form, array $fields = []): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('transaction_id')
                    ->label('Transaction')
                    ->required()
                    ->relationship('transaction', 'name'),
                Forms\Components\Select::make('level_id')
                    ->label('Level')
                    ->required()
                    ->relationship('level', 'name'),
                Forms\Components\TextInput::make('year')
                    ->required()
                    ->length(4),
                Forms\Components\DatePicker::make('start_date')
                    ->label('Registration Open')
                    ->required(),
                Forms\Components\DatePicker::make('end_date')
                    ->label('Registration Closed')
                    ->required(),
                Forms\Components\DatePicker::make('exam_start_date')
                    ->label('Examination Start Date')
                    ->required(),
                Forms\Components\DatePicker::make('exam_end_date')
                    ->label('Examination End Date'),
                Forms\Components\TextInput::make('amount')
                    ->label('Application Fee (GHS)')
                    ->required()
                    ->numeric()
                    ->step(0.01)
                    ->prefix('GHS'),
                Forms\Components\TextInput::make('voucher_amount')
                    ->label('E-Voucher Amount (GHS)')
                    ->required(fn (Forms\Get $get) => $get('transaction_id') == 1)
                    ->numeric()
                    ->step(0.01)
                    ->prefix('GHS')
                    ->helperText('Amount for purchasing E-vouchers for this application')
                    ->visible(fn (Forms\Get $get) => $get('transaction_id') == 1),
            ]);
    }

    public static function assignHallsTable(Table $table, $query, $columns = [], $filters = []): Table
    {
        $id_label = "Student ID";

        $checkSection = function ($livewire, $column) {
            $filterState = $livewire->tableFilters;
            $selectedSection = $filterState['section']['section'] ?? null;

            if ($selectedSection === 'A' && str_contains($column, 'sec_b')) {
                return false;
            }
            if ($selectedSection === 'B' && str_contains($column, 'sec_a')) {
                return false;
            }
            return true;
        };

        return $table
            ->columns([
                Tables\Columns\TextColumn::make('student_id')
                    ->label($id_label)
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('venue.location')
                    ->searchable()
                    ->sortable(),
                ...$columns,
                Tables\Columns\TextColumn::make('venue.hall')
                    ->label('Hall')
                    ->searchable()
                    ->visible(fn ($livewire, $data) => $checkSection($livewire, $data['venue.location'])),
            ])
            ->modifyQueryUsing(function (Builder $query) {
                    return $query->where("student_id")->exists();
            })
            ->defaultSort('student_id', 'asc')
            ->filters([
                ...$filters,
                Tables\Filters\Filter::make('venue.location')
                    ->form([
                        Forms\Components\Select::make('venue.location')
                            ->options([
                                'Accra' => 'Accra',
                                'Kumasi' => 'Kumasi',
                            ])
                            ->default('Accra')
                            ->label('Location'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['venue.location'],
                            fn (Builder $query, $location) => $query->where('venue.location', $location)
                        );
                    }),
                Tables\Filters\SelectFilter::make('venue_id')
                    ->label('Hall')
                    ->relationship('venue', 'hall'),
            ])
            ->bulkActions([
                AssignHall::make('Assign Hall')
                    ->color('success')
                    ->deselectRecordsAfterCompletion()
                    ->icon('heroicon-o-arrow-uturn-right'),
                ExportBulkAction::make()->exports([
                    ExcelExport::make()
                        ->fromTable()
                        ->withFilename("StudentsHallAssignment")
                ]),
            ])
            ->emptyStateHeading('No students available.');
    }

    public static function assignEntranceExaminersTable(Table $table, $query, $columns = [], $filters = []): Table
    {
        $id_label = "Student ID";

        $checkSection = function ($livewire, $column) {
            $filterState = $livewire->tableFilters;
            $selectedSection = $filterState['section']['section'] ?? null;

            if ($selectedSection === 'A' && str_contains($column, 'sec_b')) {
                return false;
            }
            if ($selectedSection === 'B' && str_contains($column, 'sec_a')) {
                return false;
            }
            return true;
        };

        return $table
            ->columns([
                Tables\Columns\TextColumn::make('student_id')
                    ->label($id_label)
                    ->searchable()
                    ->sortable(),
                // Tables\Columns\TextColumn::make('name')
                //     ->searchable()
                //     ->sortable(),
                Tables\Columns\TextColumn::make('section')
                    ->searchable()
                    ->sortable(),
                ...$columns,
                Tables\Columns\TextColumn::make('marker.name')
                    ->label('Marker Name')
                    ->searchable()
                    ->visible(fn ($livewire) => $checkSection($livewire, 'marker')),
                Tables\Columns\TextColumn::make('moderator.name')
                    ->label('Moderator Name')
                    ->searchable()
                    ->visible(fn ($livewire) => $checkSection($livewire, 'moderator')),
            ])
            ->defaultSort('student_id', 'asc')
            ->filters([
                ...$filters,
                Tables\Filters\Filter::make('section')
                    ->form([
                        Forms\Components\Select::make('section')
                            ->options([
                                'A' => 'A',
                                'B' => 'B',
                            ])
                            ->default('A')
                            ->label('Section'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['section'],
                            fn (Builder $query, $section) => $query->where('section', $section)
                        );
                    }),
                Tables\Filters\SelectFilter::make('marker_id')
                    ->label('Markers')
                    ->relationship('marker', 'full_name'),
                Tables\Filters\SelectFilter::make('moderator_id')
                    ->label('Moderators')
                    ->relationship('moderator', 'full_name'),
            ])
            ->bulkActions([
                AssignMarker::make('Assign marker')
                    ->color('success')
                    ->deselectRecordsAfterCompletion()
                    ->icon('heroicon-o-arrow-uturn-right'),
                // AssignModerator::make('Assign moderator')
                //     ->color('gray')
                //     ->deselectRecordsAfterCompletion()
                //     ->icon('heroicon-o-arrow-uturn-right'),
                ExportBulkAction::make()->exports([
                    ExcelExport::make()
                        ->fromTable()
                        ->withFilename("Scripts")
                ]),
            ])
            ->emptyStateHeading('No scripts available.');
    }

    public static function assignExaminersTable(Table $table, $query, $columns = [], $filters = []): Table
    {
        $id_label = $table->getModelLabel() === "submission" ? "Candidate ID" : "Student ID";
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('student_id')
                    ->label($id_label)
                    ->searchable()
                    ->sortable(),
                ...$columns,
                Tables\Columns\TextColumn::make('marker.full_name')
                    ->label('Marker Name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('moderator.full_name')
                    ->label('Moderator Name')
                    ->searchable(),
            ])
            ->filters([
                ...$filters,
                Tables\Filters\SelectFilter::make('marker_id')
                    ->label('Markers')
                    ->options(function () use ($query) {
                        return $query->with('marker')->get()->pluck('marker.full_name', 'marker.id')->filter(function ($value, $key) {
                            return $value != "";
                        })->toArray();
                    }),
                Tables\Filters\SelectFilter::make('moderator_id')
                    ->label('Moderators')
                    ->options(function () use ($query) {
                        return $query->with('moderator')->get()->pluck('moderator.full_name', 'moderator.id')->filter(function ($value, $key) {
                            return $value != "";
                        })->toArray();
                    }),
            ])
            ->bulkActions([
                AssignMarker::make('Assign marker')
                    ->color('success')
                    ->deselectRecordsAfterCompletion()
                    ->icon('heroicon-o-arrow-uturn-right'),
                AssignModerator::make('Assign moderator')
                    ->color('gray')
                    ->deselectRecordsAfterCompletion()
                    ->icon('heroicon-o-arrow-uturn-right'),
                ExportBulkAction::make()->exports([
                    ExcelExport::make()
                        ->fromTable()
                        ->withFilename("Results")
                ]),
                BulkAction::make('deleteSelectedScripts')
                    ->label('Delete Selected')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->action(function ($records, $action) {
                        $blocked = collect();
                        $deleted = collect();
                        $user = request()->user();
                        foreach ($records as $record) {
                            $hasMarks = $record->questions()->where(function($q) {
                                $q->where('marker_score', '>', 0)->orWhere('moderator_score', '>', 0);
                            })->exists();
                            if ($hasMarks && $user->email !== '<EMAIL>') {
                                $blocked->push($record->student_id);
                            } else {
                                $record->delete();
                                $deleted->push($record->student_id);
                            }
                        }
                        if ($blocked->isNotEmpty()) {
                            \Filament\Notifications\Notification::make()
                                ->title('Cannot delete scripts for student IDs: ' . $blocked->implode(', ') . ' (marks already entered).')
                                ->danger()
                                ->send();
                        }
                        if ($deleted->isNotEmpty()) {
                            \Filament\Notifications\Notification::make()
                                ->title('Deleted scripts for student IDs: ' . $deleted->implode(', '))
                                ->success()
                                ->send();
                        }
                    })
                    ->visible(fn() => request()->user()->hasAnyRole('admin', 'iec')),
            ])
            ->emptyStateHeading('Candidates are yet to be marked as present.');
    }

    public static function candidatesTable(Table $table, $headerActions = [], $filters = []): Table
    {
        return $table
            ->recordTitleAttribute('full_name')
            ->bulkActions([
                AssignHall::make('Assign Hall')
                    ->deselectRecordsAfterCompletion(),
                Tables\Actions\BulkAction::make('Present')
                    ->requiresConfirmation()
                    ->color('success')
                    ->action(fn(Collection $records) => $records->each->markPresent())
                    ->deselectRecordsAfterCompletion(),
                Tables\Actions\BulkAction::make('Absent')
                    ->requiresConfirmation()
                    ->color('danger')
                    ->action(fn(Collection $records) => $records->each->markAbsent())
                    ->deselectRecordsAfterCompletion(),
                Tables\Actions\BulkAction::make('Exempt')
                    ->requiresConfirmation()
                    ->color('warning')
                    ->icon('heroicon-o-shield-check')
                    ->form([
                        Forms\Components\Textarea::make('exemption_reason')
                            ->label('Reason for Exemption')
                            ->required()
                            ->maxLength(255),
                    ])
                    ->action(function (Collection $records, array $data): void {
                        $records->each(function ($record) use ($data) {
                            $record->is_exempted = true;
                            $record->exemption_reason = $data['exemption_reason'];
                            $record->wrote_exam = false; // Exempted candidates didn't write
                            $record->save();
                        });
                        \Filament\Notifications\Notification::make()
                            ->title('Candidates Exempted')
                            ->success()->send();
                    })
                    ->deselectRecordsAfterCompletion(),
                ExportBulkAction::make()->exports([
                    ExcelExport::make()
                        ->fromTable()
                        ->withFilename("Candidates")
                ])
            ])
            ->checkIfRecordIsSelectableUsing(function ($record) {
                return !$record->is_exempted;
            })
            ->headerActions([ ...$headerActions])
            ->defaultSort('student_id', 'asc')
            ->filters([...$filters]);
    }

    public static function applicationTable(Table $table): Table
    {
        $user = request()->user();

        return $table
            ->columns([
                Tables\Columns\TextColumn::make('#')
                    ->rowIndex(),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('transaction.name')
                    ->formatStateUsing(fn(string $state): string => ucwords($state))
                    ->sortable(),
                Tables\Columns\TextColumn::make('level.name')
                    ->sortable(),
                Tables\Columns\TextColumn::make('year')
                    ->sortable(),
                ...self::applicationDetails($user)
            ])
            ->defaultSort('year', 'desc')
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->hidden(!$user->hasAnyRole('iec'))
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function applicationDetails($user): array
    {
        return $user->hasRole('admin') ? [
            Tables\Columns\TextColumn::make('start_date')
                ->label('Reg. Open')
                ->date()
                ->sortable(),
            Tables\Columns\TextColumn::make('end_date')
                ->label('Reg. Closed')
                ->date()
                ->sortable(),
            Tables\Columns\TextColumn::make('exam_start_date')
                ->label('Examination Start')
                ->date()
                ->sortable(),
            Tables\Columns\TextColumn::make('exam_end_date')
                ->label('Examination End')
                ->date()
                ->sortable(),
            Tables\Columns\TextColumn::make('amount')
                ->label('Application Fee')
                ->money('GHS')
                ->sortable(),
            Tables\Columns\TextColumn::make('voucher_amount')
                ->label('Voucher Amount')
                ->money('GHS')
                ->sortable(),
                // ->visible(fn ($livewire, $data) => $data['transaction_id'] == 1),
                // ->visible(fn ($record) => $record && $record->transaction_id == 1),
        ] : [];
    }

    public static function sanctionedResultsTable(Table $table, $query, $columns, $filters = [], $bulkActions = [], string $canPublish = 'gsl'): Table
    {
        $user = request()->user();
        $role = examinerType($user);

        $id_label = $table->getModelLabel() === "submission" ? "Candidate ID" : "Student ID";

        return $table
            ->modifyQueryUsing(function (Builder $query) use ($user, $role) {
                if ($role && in_array($role, ['moderator', 'marker'])) {
                    return $query->where($role . "_id", $user->id);
                } else {
                    return $query;
                }
            })
            ->columns([
                Tables\Columns\TextColumn::make('student_id')
                    ->label($id_label)
                    ->searchable()
                    ->sortable(),
                ...$columns,
            ])
            ->filters([...$filters])
            ->bulkActions([
                ...$bulkActions,
                Tables\Actions\BulkAction::make('Publish')
                    ->requiresConfirmation()
                    ->action(function (Collection $records) {
                        if ($records->first()->getTable() === 'results') {
                            $records = $records->whereNotNull('iec_score');
                        }
                        $records->each->update(["isPublished" => 1]);
                    })
                    ->color("success")
                    ->hidden(!$user->hasAnyRole($canPublish))
                    ->deselectRecordsAfterCompletion(),
                Tables\Actions\BulkAction::make('Unpublish')
                    ->requiresConfirmation()
                    ->action(fn(Collection $records) => $records->each->update(["isPublished" => 0]))
                    ->color("info")
                    ->hidden(!$user->hasAnyRole($canPublish))
                    ->deselectRecordsAfterCompletion(),
                ExportBulkAction::make()->exports([
                    ExcelExport::make()
                        ->fromTable()
                        ->withFilename("Results")
                ]),
            ])->emptyStateHeading('No results or papers sanctioned yet');
    }

    public static function resultsTable(Table $table, $query, $columns, $filters = [], $bulkActions = [], string $canPublish = 'gsl'): Table
    {
        $user = request()->user();
        $role = examinerType($user);

        $id_label = $table->getModelLabel() === "submission" ? "Candidate ID" : "Student ID";

        return $table
            ->modifyQueryUsing(function (Builder $query) use ($user) {
                if ($user->hasRole('marker')) {
                    $query->where('marker_id', $user->id);
                }
        
                if ($user->hasRole('moderator')) {
                    $query->orWhere('moderator_id', $user->id);
                }
        
                return $query;
            })
            ->columns([
                Tables\Columns\TextColumn::make('student_id')
                    ->label($id_label)
                    ->searchable()
                    ->sortable(),
                // Tables\Columns\TextColumn::make('user.full_name')
                //     ->searchable()
                //     ->sortable(),
                ...$columns,
                Tables\Columns\IconColumn::make('isPublished')
                    ->hidden(!$user->hasAnyRole('admin', 'iec', 'gsl', 'glc'))
                    ->label("Accepted")
                    ->alignCenter()
                    ->default("")
                    ->icon(fn(string $state): string => match ($state) {
                        '1' => 'heroicon-o-check-circle',
                        default => "heroicon-o-ellipsis-horizontal"
                    })->color(fn(string $state): string => match ($state) {
                        '1' => 'success',
                        default => ''
                    }),
            ])
            ->filters([...$filters])
            ->bulkActions([
                ...$bulkActions,
                Tables\Actions\BulkAction::make('Publish')
                    ->requiresConfirmation()
                    ->action(function (Collection $records) {
                        if ($records->first()->getTable() === 'results') {
                            $records = $records->whereNotNull('iec_score');
                        }
                        $records->each->update(["isPublished" => 1]);
                    })
                    ->color("success")
                    ->hidden(!$user->hasAnyRole($canPublish))
                    ->deselectRecordsAfterCompletion(),
                Tables\Actions\BulkAction::make('Unpublish')
                    ->requiresConfirmation()
                    ->action(fn(Collection $records) => $records->each->update(["isPublished" => 0]))
                    ->color("info")
                    ->hidden(!$user->hasAnyRole($canPublish))
                    ->deselectRecordsAfterCompletion(),
                ExportBulkAction::make()->exports([
                    ExcelExport::make()
                        ->fromTable()
                        ->withFilename("Results")
                ]),
            ])->emptyStateHeading('No results or papers assigned yet');
    }
}
