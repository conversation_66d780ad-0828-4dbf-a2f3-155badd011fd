<?php

namespace App\Filament\Resources\AdminResource\Pages;

use App\Filament\Resources\AdminResource;
use App\Models\User;
use App\Notifications\WelcomeSetPasswordNotification;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Str;

class ManageAdmins extends ManageRecords
{
    protected static string $resource = AdminResource::class;

    public function getTitle(): string|Htmlable
    {
        $title = null;
        if ($request = request('tableFilters')) {
            $title = $request['roles']['value'] ?? null;
        };
        return $title ? Str::plural(ucfirst($title)) : parent::getTitle(); // TODO: Change the autogenerated stub
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->mutateFormDataUsing(function (array $data): array {
                    return [...$data, 'password' => bcrypt(Str::random(60))];
                })
                ->after(function ($action, $record) {
                    $token = app('auth.password.broker')->createToken($record);
                    $notification = new WelcomeSetPasswordNotification($token);
                    $notification->user = $record;
                    $record->notify($notification);
                }),
        ];
    }
}
