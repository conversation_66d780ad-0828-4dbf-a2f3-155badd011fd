<?php

namespace App\Filament\Resources;

use App\Enums\EntranceSection;
use App\Filament\Resources\AdminResource\Pages;
use App\Filament\Resources\AdminResource\RelationManagers;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use STS\FilamentImpersonate\Tables\Actions\Impersonate;
use Spatie\Permission\Models\Role;

class AdminResource extends Resource
{
    protected static ?string $model = User::class;
    protected static ?string $label = 'Admin';

    protected static ?string $navigationGroup = 'System Admins';
    // protected static bool $shouldRegisterNavigation = false;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {

        $currentUser = request()->user(); // Get the currently authenticated user
        return $form
            ->schema([
                Forms\Components\TextInput::make('first_name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('last_name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('phone')
                    ->required()
                    ->maxLength(12),
                Forms\Components\TextInput::make('email')
                    ->email()
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('section')
                    ->options([
                        'A' => 'Section A',
                        'B' => 'Section B',
                    ]),
                Forms\Components\Select::make('roles') // Role selection for admin
                    ->label('Roles')
                    ->multiple()
                    ->preload()
                    ->required()
                    ->relationship('roles', 'name', function (Builder $query) use ($currentUser) {
                        if ($currentUser->hasRole('admin')) {
                            // If the user is an admin, restrict to only Marker and Moderator
                            $query->whereIn('name', ['moderator', 'marker']);
                        }
                        // If the user is not an admin, allow all roles
                    })
            ]);
    }

    public static function table(Table $table): Table
    {
        $currentUser = request()->user();
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('#')
                    ->rowIndex(),
                Tables\Columns\TextColumn::make('first_name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('last_name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('roles.name')
                    ->formatStateUsing(fn(string $state): string => ucwords($state))
                    ->searchable(),
                Tables\Columns\TextColumn::make('section')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Impersonate::make(),
                \Filament\Tables\Actions\Action::make('reset_password')
                    ->label('')
                    ->color('warning')
                    ->icon('heroicon-o-key')
                    ->requiresConfirmation()
                    ->modalHeading(fn($record) => 'Reset Password')
                    ->modalDescription(fn($record) => "Are you sure you want to reset the password for {$record->email} to the default 'user@pass' password?")
                    ->modalSubmitActionLabel('Yes, reset password')
                    ->action(function ($record) {
                        $record->password = bcrypt('user@pass');
                        $record->save();
                    })
                    ->visible(fn($record) => $record->id !== auth()->id()),
                // Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->recordAction(null)
            ->filters([
                Tables\Filters\SelectFilter::make('roles')
                    ->options(function() use ($currentUser) {
                        // Check if the current user is an admin
                        if ($currentUser->hasRole('admin')) {
                            return Role::whereIn('name', ['moderator', 'marker'])->pluck('name', 'name')->all();
                        }
                        // If not an admin, return all roles
                        return Role::pluck('name', 'name')->all();
                    })
                    ->modifyQueryUsing(function (Builder $query, $state) {
                        if (!empty($state['value'])) {
                            $query->with('roles')
                                ->whereHas('roles', function ($query) use ($state) {
                                    return $query->whereIn('name', [$state['value']]);
                                });
                        }
                        return $query;
                    }),
            ])
            ->modifyQueryUsing(function (Builder $query) use ($currentUser) {
                if ($currentUser->hasRole('admin')) {
                    // If the user is an admin, show only users with roles 'moderator' or 'marker'
                    // $query->hasAnyRole('moderator', 'marker');
                    $query->whereHas('roles', function (Builder $query) {
                        $query->whereIn('name', ['moderator', 'marker']);
                    });
                } elseif ($currentUser->hasRole('iec')) {
                    $query->admins();
                    // If the user is IEC, show all users
                    // No additional filtering needed
                }

                return $query; // Return the modified query
            });
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageAdmins::route('/'),
        ];
    }

    public static function canViewAny(): bool
    {
        return request()->user()->hasAnyRole('iec', 'admin');
    }

}
