<?php

namespace App\Filament\Resources;

use App\Filament\Exports\EntranceTransactionExporter;
use App\Filament\Exports\OrderExporter;
use App\Filament\Imports\ScholarshipImporter;
use App\Filament\Resources\OrderResource\Pages;
use App\Filament\Resources\OrderResource\RelationManagers;
use App\Models\Application;
use App\Models\Order;
use App\Models\Transaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use pxlrbt\FilamentExcel\Exports\ExcelExport;

class OrderResource extends Resource
{
    protected static ?string $model = Order::class;

    protected static ?string $label = "PLC/Post-call Transaction";

    protected static ?string $navigationGroup = 'Transactions';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('student_id')
                    ->label('Student ID')
                    ->disabled()
                    ->required()
                    ->rules('exists:users,student_id')
                    ->maxLength(255),
                Forms\Components\Select::make('application_id')
                    ->label('Application')
                    ->required()
                    // ->options(fn() => Application::all()->pluck('name', 'id'))
                    ->options(fn(): array => Application::where('transaction_id', '!=', 1)->pluck('name', 'id')->all()),
                Forms\Components\Select::make('transaction_id')
                    ->label('Transaction')
                    ->options(fn() => Transaction::where('id', '!=', 1)->pluck('name', 'id'))
                    ->required(),
                Forms\Components\Textarea::make('notes')
                    ->required()
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // Tables\Columns\TextColumn::make('#')
                //     ->rowIndex(),
                Tables\Columns\TextColumn::make('full_name'),
                Tables\Columns\TextColumn::make('user.student_id')
                    ->searchable()
                    ->label('Student ID'),
                Tables\Columns\TextColumn::make('transaction.name'),
                Tables\Columns\TextColumn::make('application.name'),
                Tables\Columns\TextColumn::make('user.email')
                    ->label("email"),
                Tables\Columns\TextColumn::make('reference'),
                    // ->formatStateUsing(function($state) {
                    //     if ($state === 'scholarship') {
                    //         return 'Scholarship (General)';
                    //     }
                        
                    //     if (str_starts_with($state, 'scholarship:')) {
                    //         $type = str_replace('scholarship:', '', $state);
                    //         return 'Scholarship (' . ucfirst($type) . ')';
                    //     }
                        
                    //     return $state;
                    // }),
                Tables\Columns\TextColumn::make('amount')
                    ->numeric(2)
                    ->formatStateUsing(fn($state) => number_format($state, 2, '.', ''))
                    ->sortable(),
                Tables\Columns\TextColumn::make('charges')
                    ->numeric(2)
                    ->formatStateUsing(fn($state) => number_format($state, 2, '.', ''))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->sortable(),
                Tables\Columns\TextColumn::make('description')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->formatStateUsing(fn(string $state) => nl2br($state))
                    ->html(),
                Tables\Columns\TextColumn::make('notes')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn($state, $record) => match (true) {
                        $record->isScholarship() => 'success',
                        $state === 1 => 'success',
                        default => 'warning',
                    })
                    ->formatStateUsing(fn($state, $record) => $record->isScholarship() ? "Scholarship" : ($state ? "Manual" : "Electronic"))
                    ->label('Method'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Date')
                    ->date()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->sortable(),
            ])
            ->modifyQueryUsing(function (Builder $query) {
                return $query->otherTransactions()
                    ->whereHas('user', function (Builder $userQuery) {
                        $userQuery
                        ->whereNotNull('student_id')
                        ->where('student_id', '!=', '');
                    });
            })
            ->filters([
                Tables\Filters\SelectFilter::make('application_id')
                    ->label('Application')
                    ->options(fn(): array => Application::where('transaction_id', '!=', 1)->pluck('name', 'id')->all()),
                Tables\Filters\SelectFilter::make('scholarship_type')
                    ->label('Scholarship Type')
                    ->options([
                        'scholarship' => 'General Scholarship',
                        'scholarship:government' => 'Government Sponsored',
                        'scholarship:corporate' => 'Corporate Sponsored',
                        'scholarship:foundation' => 'Foundation Sponsored',
                        'scholarship:partial' => 'Partial Scholarship',
                        'scholarship:full' => 'Full Scholarship',
                        'scholarship:other' => 'Other Sponsorship',
                    ])
                    ->query(function (Builder $query, array $data) {
                        if (isset($data['value'])) {
                            $query->where('reference', $data['value']);
                        }
                    }),
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('from'),
                        Forms\Components\DatePicker::make('to'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when($data['to'], fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date));
                    })
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                // ExportBulkAction::make()->exports([
                //     ExcelExport::make()
                //         ->fromTable()
                //         ->withFilename("Transactions")
                // ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageOrders::route('/'),
        ];
    }

    public static function canViewAny(): bool
    {
        return request()->user()->hasAnyRole('iec', 'accountant');
    }
}
