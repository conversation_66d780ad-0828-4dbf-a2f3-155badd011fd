<?php

namespace App\Filament\Resources\ApplicationResource\RelationManagers;

use App\Filament\Resources\ResourceUtilities\ResourceUtilities;
use App\Models\Venue;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;
use Filament\Tables;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\DatePicker;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HtmlString;
use Illuminate\Http\Request;
use App\Filament\Imports\CourseExemptionImporter;
use Filament\Tables\Actions\ImportAction;
use Illuminate\Support\Facades\Log;

class ExamCandidatesRelationManager extends RelationManager
{
    protected static string $relationship = 'results';
    protected static ?string $title = "Candidates";
    
    public function form(\Filament\Forms\Form $form): \Filament\Forms\Form
    {
        return $form->schema([
            \Filament\Forms\Components\TextInput::make('student_id')
                ->label('Student ID')
                ->searchable()
                ->required()
                ->maxLength(255),
            \Filament\Forms\Components\Select::make('level_id')
                ->label('Level')
                ->searchable()
                ->required(),
            \Filament\Forms\Components\Select::make('course_id')
                ->label('Course')
                ->relationship('course', 'name')
                ->searchable()
                ->required(),
            \Filament\Forms\Components\Select::make('application_id')
                ->label('Application')
                ->relationship('application', 'name')
                ->searchable()
                ->required(),
            \Filament\Forms\Components\Toggle::make('wrote_exam')
                ->label('Wrote Exam')
                ->default(true),
        ]);
    }
    
    public function table(Table $table): Table
    {
        $user = request()->user();
        return ResourceUtilities::candidatesTable($table, 
            [
                Tables\Actions\Action::make('addCandidate')
                    ->label('Add Candidate')
                    ->icon('heroicon-o-user-plus')
                    ->visible(fn() => request()->user()->hasAnyRole('admin', 'iec'))
                    ->action(function (array $data, $livewire) {
                        $livewire->mountAction('addCandidate', $data);
                    })
                    ->form([
                        \Filament\Forms\Components\TextInput::make('student_id')
                            ->label('Student ID')
                            ->searchable()
                            ->required()
                            ->maxLength(255),
                        \Filament\Forms\Components\Select::make('level_id')
                            ->label('Level')
                            ->searchable()
                            ->required(),
                        \Filament\Forms\Components\Select::make('course_id')
                            ->label('Course')
                            ->relationship('course', 'name')
                            ->searchable()
                            ->required(),
                        \Filament\Forms\Components\Select::make('application_id')
                            ->label('Application')
                            ->relationship('application', 'name')
                            ->searchable()
                            ->required(),
                        \Filament\Forms\Components\Toggle::make('wrote_exam')
                            ->label('Wrote Exam')
                            ->default(true),
                    ])
                    ->action(function (array $data) {
                        $modelClass = static::getModel();
                        $modelClass::create($data);
                        \Filament\Notifications\Notification::make()
                            ->title('Candidate added successfully!')
                            ->success()
                            ->send();
                    }),
                ImportAction::make('importCourseExemptions')
                    ->label('Import Exemptions')
                    ->importer(CourseExemptionImporter::class)
                    ->chunkSize(100)
                    ->maxRows(1000)
                    ->options(function () {
                        $courses = $this->getOwnerRecord()
                            ->courses()
                            ->pluck('name', 'id')
                            ->toArray();
                        return [
                            'application_id' => $this->getOwnerRecord()->getKey(),
                            'courses' => $courses,
                            'default_exemption_reason' => 'Exempted via CSV import',
                        ];
                    })
            ], 
            [
            Tables\Filters\SelectFilter::make('course_id')
                ->label('Courses')
                ->options(function () use ($user) {
                    return $this->getOwnerRecord()->courses()->pluck('name', 'id');
                })
                ->default($this->getOwnerRecord()->courses()->first()?->id),
            Tables\Filters\SelectFilter::make('venue_location')
                ->label('Location')
                ->options(Venue::query()->pluck('location', 'location')->unique())
                ->query(function ($query, $data) use ($user) {
                    if ($data['value'] === null) {
                        return $query;
                    }

                    return $query->whereHas('user.venue', function ($venueQuery) use ($data) {
                        $venueQuery->where('location', $data['value']);
                    });
                })
                ->placeholder('All Locations'),
            Tables\Filters\TernaryFilter::make('is_exempted')
                ->label('Exemption Status')
                ->placeholder('All Candidates')
                ->trueLabel('Exempted')
                ->falseLabel('Not Exempted')
                ->queries(
                    true: fn (Builder $query) => $query->where('is_exempted', true),
                    false: fn (Builder $query) => $query->where('is_exempted', false),
                ),
            Filter::make('created_at')
                ->form([
                    DatePicker::make('created_from')
                        ->label('From Date'),
                    DatePicker::make('created_until')
                        ->label('Until Date'),
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query
                        ->when(
                            $data['created_from'] ?? null,
                            fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                        )
                        ->when(
                            $data['created_until'] ?? null,
                            fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                        );
                })
        ])
        ->checkIfRecordIsSelectableUsing(function ($record) {
            // Allow all records to be selectable when filtered by is_exempted
            $filters = request()->input('tableFilters', []);
            if (isset($filters['is_exempted'])) {
                return true;
            }
            // Original behavior for non-exempted records
            return !$record->is_exempted;
        })
        ->columns([
            Tables\Columns\TextColumn::make('student_id')
                ->label('Student ID')
                ->sortable()
                ->searchable(),
            Tables\Columns\TextColumn::make('user.full_name')
                ->label('Full Name')
                ->sortable()
                ->searchable(),
            Tables\Columns\TextColumn::make('user.first_name')
                ->label('First Name')
                ->hidden(!$user->hasAnyRole('iec', 'gsl'))
                ->searchable(),
            Tables\Columns\TextColumn::make('user.last_name')
                ->label('Last Name')
                ->hidden(!$user->hasAnyRole('iec', 'gsl'))
                ->searchable(),
            Tables\Columns\TextColumn::make('course.name')
                ->label('Course')
                ->sortable()
                ->searchable(),
            Tables\Columns\TextColumn::make('venue.name')
                ->label('Center')
                ->sortable()
                ->searchable(),
            Tables\Columns\TextColumn::make('wrote_exam')
                ->label('Attendance / Exemption')
                ->badge()
                ->formatStateUsing(function ($state, $record): string {
                    if ($record->is_exempted) {
                        return 'Exempted';
                    }
                    return $state ? 'Attended' : 'Absent';
                })
                ->icon(function ($state, $record): string {
                    if ($record->is_exempted) {
                        return 'heroicon-s-shield-check';
                    }
                    return $state ? 'heroicon-s-check-circle' : 'heroicon-s-minus-circle';
                })
                ->color(function ($state, $record): string {
                    if ($record->is_exempted) {
                        return 'primary';
                    }
                    return $state ? 'success' : 'danger';
                })
                ->tooltip(function ($record): ?string {
                    if ($record->is_exempted) {
                        return $record->exemption_reason ? 'Reason: ' . $record->exemption_reason : 'Exempted';
                    }
                    return $record->wrote_exam ? 'Wrote exam' : 'Absent';
                })
                ->sortable(),
        ]);
        // ->modifyQueryUsing(function (Builder $query): Builder {
        //     // Only show eligible students by default
        //     return $query->whereHas('user', function ($userQuery) {
        //         $userQuery->where('is_eligible', true);
        //     });
        // });
    }

    public static function canViewForRecord($ownerRecord, string $pageClass): bool
    {
        return request()->user()->hasAnyRole(['admin', 'iec']);
    }
}
