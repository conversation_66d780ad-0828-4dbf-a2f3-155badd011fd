<?php

namespace App\Filament\Resources\ApplicationResource\RelationManagers;

use App\Filament\Resources\ResourceUtilities\ResourceUtilities;
use App\Models\Script;
use Filament\Forms\Components\Select;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;

class ExamAssignRelationManager extends RelationManager
{
    protected static string $relationship = 'scripts';
    protected static ?string $title = "Assign Scripts";

    public function table(Table $table): Table
    {
        return ResourceUtilities::assignExaminersTable($table, $this->getOwnerRecord()->scripts(), [
            Tables\Columns\TextColumn::make('course.name')
                ->state(function (Script $record) {
                    return $record->sub_course_id ? 
                        $record->course->name . ' - ' . $record->sub_course->name : $record->course->name;
                })
                ->sortable(),
        ], [
            Tables\Filters\SelectFilter::make('course_or_subcourse')
                ->form([
                    Select::make('course_or_subcourse')
                        ->options(function () {
                            // Get all courses and subcourses
                            $courses = $this->getOwnerRecord()->courses()->with('sub_courses')->get();
                            $options = [];

                            foreach ($courses as $course) {
                                // Add the course itself to the options
                                if($course->sub_courses->isEmpty()) {
                                    $options[$course->id] = $course->name;
                                } else {

                                    // Add subcourses with a prefix to differentiate
                                    foreach ($course->sub_courses as $subCourse) {
                                        $options["sc_{$subCourse->id}"] = "{$course->name} - {$subCourse->name}"; // Prefix subcourse IDs
                                    }
                                }

                            }

                            return $options;
                        })
                        ->label('Course or Subcourse'),
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query->when(
                        $data['course_or_subcourse'],
                        function (Builder $query, $course) {

                            // Check if the selected value is a course or subcourse
                            if (strpos($course, 'sc_') === 0) {
                                // It's a subcourse ID
                                $subCourseId = str_replace('sc_', '', $course); // Remove prefix
                                $query->where('sub_course_id', $subCourseId);
                            } else {
                                // It's a course ID
                                $query->where('course_id', $course);
                            }

                        }
                    );
                }),
        ]);
    }

    public static function canViewForRecord($ownerRecord, string $pageClass): bool
    {
        return request()->user()->hasRole('admin');
    }

}
