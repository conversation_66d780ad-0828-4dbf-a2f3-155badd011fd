<?php

namespace App\Filament\Resources\ApplicationResource\RelationManagers;

use App\Exports\CollectionExport;
use App\Filament\Actions\Results\AddMarks;
use App\Filament\Actions\Results\SanctionResults;
use App\Filament\Actions\TableActions\AcceptModeratorResults;
use App\Filament\Actions\TableActions\NotifyPublishedResults;
use App\Filament\Actions\TableActions\PublishResults;
use App\Filament\Imports\IECResultImporter;
use App\Filament\Imports\MarkerResultsImporter;
use App\Filament\Imports\ModeratorResultsImporter;
use App\Filament\Imports\ResultImporter;
use App\Filament\Resources\ResourceUtilities\ResourceUtilities;
use App\Models\Result; // Added for type hinting
use App\Models\Audit;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;
use Filament\Tables;
use Maatwebsite\Excel\Facades\Excel;

class ExamResultRelationManager extends RelationManager
{
    protected static string $relationship = 'results';
    protected static ?string $title = "Results";

    public function table(Table $table): Table
    {
        $user = request()->user();
        return ResourceUtilities::resultsTable($table, $this->getOwnerRecord()->results(), [
            Tables\Columns\TextColumn::make('course.name'),
            Tables\Columns\TextColumn::make('status')->label('Status')->state(fn(Result $record) => $record->remarksText())->sortable(),
            ...$this->markerEdits(),
            ...$this->moderatorEdits(),
            ...$this->displayScores(),
            ...$this->iecEdits(),
        ], [
            Tables\Filters\SelectFilter::make('course_id')
                ->label('Courses')
                ->options(function () use ($user) {
                    // if ($user->hasAnyRole('moderator', 'marker')) {
                    //     $role = examinerType($user);
                    //     if ($role) $user->assignedCourses($role, $this->getOwnerRecord()->getKey())->all();
                    // }
                    return $this->getOwnerRecord()->courses()->pluck('name', 'id');
                }),
            ...$this->adminResultsFilter($this->getOwnerRecord()->results()),
            Tables\Filters\TernaryFilter::make('is_exempted')->label('Exempted'),
        ], [
            AddMarks::make('Add marks')
                ->color('gray')
                ->hidden(!($user->hasAnyRole('iec')))
                ->deselectRecordsAfterCompletion(),
            SanctionResults::make('Sanction Results')
                ->color('gray')
                ->hidden(!($user->hasAnyRole('iec')))
                ->deselectRecordsAfterCompletion()
        ])->headerActions([
            AcceptModeratorResults::make('Accept Moderator Results')
                ->hidden(!$user->hasAnyRole('iec'))
                ->arguments(['application' => $this->getOwnerRecord()]),
            Tables\Actions\ImportAction::make()
                ->hidden(!$user->hasAnyRole('marker'))
                ->importer(MarkerResultsImporter::class)
                ->options(['application_id' => $this->getOwnerRecord()->getKey(), 'user' => request()->user()]),
            Tables\Actions\ImportAction::make()
                ->hidden(!$user->hasAnyRole('moderator'))
                ->importer(ModeratorResultsImporter::class)
                ->options(['application_id' => $this->getOwnerRecord()->getKey(), 'user' => request()->user()]),
            Tables\Actions\Action::make('Download Composite')
                ->hidden(!$user->hasAnyRole('admin', 'iec'))
                ->action(function () {
                    return Excel::download(new CollectionExport($this->getOwnerRecord()->compositeRefactored()), "composite.xlsx");
                }),
            Tables\Actions\Action::make('Download Merged Composite')
                ->hidden(!$user->hasAnyRole('admin', 'iec'))
                ->action(function () {
                    $fileName = date('Y-m-d') . '_' . $this->getOwnerRecord()->name . '_composite.xlsx';
                    return Excel::download(new CollectionExport($this->getOwnerRecord()->mergedComposite()), $fileName);
                }),
            NotifyPublishedResults::make('Notify')
                ->hidden(!$user->hasAnyRole('gsl'))
                ->arguments(['application' => $this->getOwnerRecord(), 'type' => 'students']),
            PublishResults::make('Publish')
                ->hidden(!request()->user()->hasAnyRole('gsl glc'))
                ->arguments(['application' => $this->getOwnerRecord(), 'type' => 'students']),
        ]);
    }

    public function adminResultsFilter($query)
    {
        return request()->user()->hasAnyRole('admin', 'iec') ? [
            Tables\Filters\SelectFilter::make('wrote_exam')
                ->options([
                    1 => 'Present',
                    0 => 'Absent',
                ])->default(1),
            Tables\Filters\SelectFilter::make('marker_id')
                ->label('Markers')
                ->options(function () use ($query) {
                    return $query->with('marker')->get()->pluck('marker.full_name', 'marker.id')->filter(function ($value, $key) {
                        return $value != "";
                    })->toArray();
                }),
            Tables\Filters\SelectFilter::make('moderator_id')
                ->label('Moderators')
                ->options(function () use ($query) {
                    return $query->with('moderator')->get()->pluck('moderator.full_name', 'moderator.id')->filter(function ($value, $key) {
                        return $value != "";
                    })->toArray();
                }),
        ] : [];
    }

    public function markerEdits(): array
    {
        $user = request()->user();
        return $user->hasAnyRole('marker') ? [
            Tables\Columns\TextInputColumn::make('marker_score')
                ->extraAttributes(['class' => 'small-input'])
                ->label('Marker Score')
                ->sortable()
                ->disabled()
                //->disabled(!$this->canEditMarker($this->getOwnerRecord()))
                ->rules(['required', 'numeric', 'max:100']),
        ] : [];
    }

    public function moderatorEdits(): array
    {
        $user = request()->user();
        return $user->hasAnyRole('moderator') ? [
            Tables\Columns\TextColumn::make('marker_score')
                ->sortable(),
            Tables\Columns\TextInputColumn::make('moderator_score')
                ->extraAttributes(['class' => 'small-input'])
                ->label('Moderator Score')
                ->disabled()
                // ->disabled(fn($record) => !$this->canEditModerator($record))
                ->rules(['required', 'numeric', 'max:100'])
        ] : [];
    }

    public function iecEdits(): array
    {
        $user = request()->user();
        return $user->hasAnyRole('gsl') ? [
            // Tables\Columns\TextColumn::make('iec_score')
            //     ->extraAttributes(['class' => 'small-input'])
            //     ->label('Final Score'),
        ] : [];
    }

    public function displayScores(): array
    {
        $user = request()->user();
        return $user->hasAnyRole('admin', 'iec', 'glc', 'gsl') ? [
            Tables\Columns\TextColumn::make('marker.full_name')
                ->label('Marker Name')
                ->hidden($user->hasAnyRole('glc'))
                ->state(function ($record) {
                    $markers = $record->scripts->pluck('marker.full_name')->filter();
                    return $markers?->count() > 1 ? $markers->implode(', ') : $markers->first();
                })
                ->toggleable(isToggledHiddenByDefault: true)
                ->searchable(),
            Tables\Columns\TextColumn::make('marker_score')
                ->sortable()
                ->state(function (Result $record) {
                    if ($record->is_exempted) {
                        return 'Exempted';
                    }
                    return $record?->scripts?->sum(function ($script) {
                        return $script?->questions?->sum('marker_score');
                    });
                })
                ->icon(fn($record) => $record->scripts->every(function ($script) {
                    return $script->marker_score_confirmed;
                }) ? 'heroicon-s-check-circle' : 'heroicon-s-minus-circle')
                ->extraAttributes(fn($record) => [
                    'title' => $record->scripts->every(function ($script) {
                        return $script->marker_score_confirmed;
                    }) ? 'Score confirmed' : 'Score not confirmed',
                ])
                ->hidden($user->hasAnyRole('glc')),
            Tables\Columns\TextColumn::make('moderator.full_name')
                ->hidden($user->hasAnyRole('glc'))
                ->hidden($user->hasAnyRole('glc'))
                ->state(function ($record) {
                    $moderators = $record->scripts->pluck('moderator.full_name')->filter();
                    return $moderators?->count() > 1 ? $moderators->implode(', ') : $moderators->first();
                })
                ->label('Moderator Name')
                ->toggleable(isToggledHiddenByDefault: true)
                ->searchable(),
            Tables\Columns\TextColumn::make('moderator_score')
                ->sortable()
                ->state(function (Result $record) {
                    if ($record->is_exempted) {
                        return 'Exempted';
                    }
                    if ($record->notModeratable()) { // Assuming notModeratable also implies no score
                        return null;
                    }
                    return $record?->moderatorScore();
                })
                ->icon(fn($record) => $record->scripts->every(function ($script) {
                    return $script->moderator_score_confirmed;
                }) ? 'heroicon-s-check-circle' : 'heroicon-s-minus-circle')
                ->extraAttributes(fn($record) => [
                    'title' => $record->scripts->every(function ($script) {
                        return $script->moderator_score_confirmed;
                    }) ? 'Score confirmed' : 'Score not confirmed',
                ])
                // ->color(fn ($record) => !$record->moderator_score_confirmed ? 'success' : 'danger')
                ->hidden($user->hasAnyRole('glc')),
            $user->hasAnyRole('iec') ?
                Tables\Columns\TextInputColumn::make('iec_score')
                ->disabled(fn(Result $record) => $record->is_exempted || $record->isPublished || !$user->hasAnyRole('iec'))
                ->state(fn(Result $record) => $record->is_exempted ? 'Exempted' : $record->iec_score)
                ->extraAttributes(['class' => 'small-input'])
                ->label('Final Score')
                ->beforeStateUpdated(function (Result $record, $state) {
                    if ($record->is_exempted) {
                        return; // Do not process if exempted
                    }
                    $message = is_null($record->getOriginal('iec_score')) ?
                        "set to " :
                        "changed from " . $record->getOriginal('iec_score') . " to ";

                    Audit::create([
                        'application_id' => $record->application_id,
                        'description' => $record->student_id . " — final score " . $message . $state
                    ]);
                }) // Rules will not apply if disabled. If state is 'Exempted', numeric rules would fail if not disabled.
                ->rules(fn(Result $record) => $record->is_exempted ? [] : ['required', 'numeric', 'max:100']) :
                Tables\Columns\TextColumn::make('iec_score')
                ->state(fn(Result $record) => $record->is_exempted ? 'Exempted' : $record->iec_score)
                ->extraAttributes(['class' => 'small-input'])
                ->label('Final Score'),
        ] : [];
    }

    public static function canViewForRecord($ownerRecord, string $pageClass): bool
    {
        return request()->user()->hasAnyRole('admin', 'iec', 'glc', 'gsl');
    }
}
