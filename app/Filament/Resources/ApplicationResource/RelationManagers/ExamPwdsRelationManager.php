<?php

namespace App\Filament\Resources\ApplicationResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;

class ExamPwdsRelationManager extends RelationManager
{
    protected static string $relationship = 'pwds';

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.student_id')
                    ->label('Student ID'),
                Tables\Columns\TextColumn::make('pwd_type')
                    ->label('PWD Type'),
                Tables\Columns\TextColumn::make('pwd_details')
                    ->label('PWD Details'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make('file')
                    ->label('Permission Letter')
                    ->icon('heroicon-o-document-arrow-down')
                    ->hidden(fn($record) => !($record->getMedia("pwd")[0] ?? null))
                    ->url(function ($record) {
                        $file = $record->getMedia("pwd")[0] ?? [];
                        return $file ? $file->getUrl() : '#';
                    })
                    ->openUrlInNewTab(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    ExportBulkAction::make('export')
                        ->label('Export Selected')
                        ->icon('heroicon-o-arrow-down-tray')
                        ->color('success')
                        ->exports([
                            ExcelExport::make('excel')
                                ->fromTable()
                                ->withFilename(fn () => 'pwd-selected-export-' . date('Y-m-d')),
                        ]),
                ]),
            ]);
    }

    public static function canViewForRecord($ownerRecord, string $pageClass): bool
    {
        return request()->user()->hasAnyRole(['iec', 'admin']);
    }
}
