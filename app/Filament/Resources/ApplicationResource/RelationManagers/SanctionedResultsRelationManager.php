<?php

namespace App\Filament\Resources\ApplicationResource\RelationManagers;

use App\Exports\CollectionExport;
use App\Filament\Actions\Results\AddMarks;
use App\Filament\Actions\TableActions\AcceptModeratorResults;
use App\Filament\Actions\TableActions\NotifyPublishedResults;
use App\Filament\Imports\IECResultImporter;
use App\Filament\Imports\MarkerResultsImporter;
use App\Filament\Imports\ModeratorResultsImporter;
use App\Filament\Imports\ResultImporter;
use App\Filament\Resources\ResourceUtilities\ResourceUtilities;
use App\Models\Audit;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Facades\Excel;

class SanctionedResultsRelationManager extends RelationManager
{
    protected static string $relationship = 'results';
    protected static ?string $title = "Sanctioned Results";

    public function table(Table $table): Table
    {
        $user = request()->user();
        return ResourceUtilities::sanctionedResultsTable($table, $this->getOwnerRecord()->results(), [
            Tables\Columns\TextColumn::make('course.name'),
            Tables\Columns\TextColumn::make('sanction.name'),
        ],
        [
            Tables\Filters\SelectFilter::make('course_id')
                ->label('Courses')
                ->options(function () use ($user) {
                    if ($user->hasAnyRole('moderator', 'marker')) {
                        $role = examinerType($user);
                        if ($role) $user->assignedCourses($role, $this->getOwnerRecord()->getKey())->all();
                    }
                    return $this->getOwnerRecord()->courses()->pluck('name', 'id');
                }),
            ...$this->adminResultsFilter($this->getOwnerRecord()->results())
        ], [
        ])
        ->headerActions([
            AcceptModeratorResults::make('Accept Moderator Results')
                ->hidden(!$user->hasAnyRole('iec'))
                ->arguments(['application' => $this->getOwnerRecord()]),
        ])
        ->modifyQueryUsing(fn(Builder $query) => $query->whereNotNull('sanction_id'));
        ;
    }

    public function adminResultsFilter($query)
    {
        return request()->user()->hasAnyRole('admin', 'iec') ? [
            Tables\Filters\SelectFilter::make('sanction_id')
                ->label('Sanctions')
                ->options(function () use ($query) {
                    return $query->with('sanction')->get()->pluck('sanction.name', 'sanction.id')->filter(function ($value, $key) {
                        return $value != "";
                    })->toArray();
                }),
        ] : [];
    }

    public static function canViewForRecord($ownerRecord, string $pageClass): bool
    {
        return request()->user()->hasAnyRole('admin', 'iec');
    }
}
