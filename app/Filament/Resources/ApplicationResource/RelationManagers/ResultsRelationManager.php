<?php

namespace App\Filament\Resources\ApplicationResource\RelationManagers;

use App\Filament\Actions\EntranceResults\AssignMarker;
use App\Filament\Actions\EntranceResults\AssignModerator;
use App\Filament\Imports\ResultImporter;
use App\Models\Application;
use Filament\Actions\ImportAction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class ResultsRelationManager extends RelationManager
{
    protected static string $relationship = 'results';

    // protected static ?string $title = "";

    public function form(Form $form): Form
    {
        $user = request()->user();
        return $form
            ->schema([
                Forms\Components\TextInput::make('marker_score')
                    ->label('Marker Score')
                    ->numeric()
                    ->maxValue(100)
                    ->hidden(!$user->hasPermissionTo('set marker')),
                Forms\Components\TextInput::make('moderator_score')
                    ->hidden(!$user->hasPermissionTo('set moderator'))
                    ->numeric()
                    ->maxValue(100),
                Forms\Components\TextInput::make('iec_score')
                    ->hidden(!$user->hasPermissionTo('set iec'))
                    ->numeric()
                    ->maxValue(100),
            ]);
    }

    public function table(Table $table): Table
    {
        $user = request()->user();
        $role = examinerType($user);

        return $table
            ->modifyQueryUsing(function (Builder $query) use ($user, $role) {
                if ($role && in_array($role, ['moderator', 'marker'])) {
                    return $query->where($role . "_id", $user->id);
                } else {
                    return $query;
                }
            })
            ->recordTitleAttribute('student_id')
            ->columns([
                Tables\Columns\TextColumn::make('student_id')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('course.name')
                    ->label('Subject')
                    ->numeric()
                    ->sortable(),
                ...$this->displayExaminers($user),
                ...$this->displayScores($user),
                Tables\Columns\TextInputColumn::make($role . '_score')
                    ->hidden(!$user->hasPermissionTo('set score'))
                    ->rules(['required', 'numeric', 'max:100']),
                Tables\Columns\IconColumn::make('isPublished')
                    ->label("Accepted")
                    ->alignCenter()
                    ->default("")
                    ->icon(fn(string $state): string => match ($state) {
                        '1' => 'heroicon-o-check-circle',
                        default => "heroicon-o-ellipsis-horizontal"
                    })->color(fn(string $state): string => match ($state) {
                        '1' => 'success',
                        default => ''
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('course_id')
                    ->label('Courses')
                    ->options(function () use ($user) {
                        $role = examinerType($user);
                        if ($role) $user->assignedCourses($role, $this->getOwnerRecord()->getKey())->all();
                        return $this->getOwnerRecord()->courses()->pluck('name', 'id');
                    })
            ])
            ->headerActions([
                Tables\Actions\ImportAction::make()
                    ->hidden(!$user->hasPermissionTo('upload results'))
                    ->importer(ResultImporter::class)
                    ->options(['application_id' => $this->getOwnerRecord()->getKey()])
            ])
            ->actions([
                // Tables\Actions\EditAction::make()->hidden(function ($record) use ($user) {
                //     $role = $user->getFirstRole();
                //     if ($role == "moderator") return empty($record->marker_score);
                //     if ($role == "iec") return empty($record->moderator_score);
                //     return false;
                // })
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    AssignMarker::make('Assign marker')
                        ->hidden(!$user->hasRole('admin'))
                        ->icon('heroicon-o-arrow-uturn-right'),
                    AssignModerator::make('Assign moderator')
                        ->hidden(!$user->hasRole('admin'))
                        ->icon('heroicon-o-arrow-uturn-right'),
                    // AssignIEC::make('Assign IEC')
                    //     ->icon('heroicon-o-arrow-uturn-right')
                ])->label('Assign Result'),
                Tables\Actions\BulkAction::make('Publish')
                    ->hidden(!$user->hasRole('glc'))
                    ->requiresConfirmation()
                    ->action(fn(Collection $records) => $records->each->update(["isPublished" => 1]))
                    ->color("success")
                    ->deselectRecordsAfterCompletion(),
                Tables\Actions\BulkAction::make('Unpublish')
                    ->requiresConfirmation()
                    ->hidden(!$user->hasRole('glc'))
                    ->action(fn(Collection $records) => $records->each->update(["isPublished" => 0]))
                    ->color("info")
                    ->deselectRecordsAfterCompletion(),
                ExportBulkAction::make(),
            ]);
    }

    public function isReadOnly(): bool
    {
        return false; // TODO: Change the autogenerated stub
    }

    public function displayExaminers($user): array
    {
        return $user->hasPermissionTo('assign examiner') ? [
            Tables\Columns\TextColumn::make('marker.full_name')
                ->numeric()
                ->toggleable()
                ->sortable(),
            Tables\Columns\TextColumn::make('moderator.full_name')
                ->numeric()
                ->toggleable()
                ->sortable(),
            // Tables\Columns\TextColumn::make('iec.full_name')
            //     ->numeric()
            //     ->sortable(),
        ] : [];
    }

    public function displayScores($user): array
    {
        $isEntrance = $this->getOwnerRecord()->transaction->name == "entrance";

        return [
            Tables\Columns\TextColumn::make('marker_score')
                ->extraAttributes(['class' => '!w-2'])
                ->label('Marker Score')
                ->hidden(!$user->hasAnyPermission(['see marker'])),
            Tables\Columns\TextColumn::make('moderator_score')
                ->label("Mod. Score")
                ->extraAttributes(['class' => '!w-2'])
                ->hidden(!$user->hasAnyPermission(['see moderator'])),
            Tables\Columns\TextColumn::make('iec_score')
                ->extraAttributes(['class' => '!w-2'])
                ->hidden(!$user->hasAnyPermission(['see iec']) || $isEntrance),
        ];
    }

    public static function canViewForRecord($ownerRecord, string $pageClass): bool
    {
        return $ownerRecord->transaction->name == 'exam';
    }
}
