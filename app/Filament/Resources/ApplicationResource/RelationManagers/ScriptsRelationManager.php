<?php

namespace App\Filament\Resources\ApplicationResource\RelationManagers;

use App\Filament\Actions\EntranceResults\ReactivateMarks;
use App\Models\Result; // Added for type hinting if needed, though Script model should have result relation
use App\Filament\Actions\TableActions\NotifyPublishedResults;
use App\Models\Question;
use App\Models\Script;
use App\Models\Setting;
use App\Traits\FormatsScores;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class ScriptsRelationManager extends RelationManager
{
    use FormatsScores;

    protected const DEFAULT_SCRIPT_SCORE_DENOMMINATOR = 100;
    protected const DEFAULT_SCRIPT_MAX_ANSWERABLE_QUESTIONS = 4;

    protected static string $relationship = 'scripts';

    protected static ?string $title = 'Scripts';
    protected static ?string $modelLabel = 'Scripts';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('student_id')
                    ->disabled()
                    ->required()
                    ->maxLength(255),
                Forms\Components\SpatieMediaLibraryFileUpload::make('script_scan')
                    ->label('Upload Script Scan')
                    ->downloadable()
                    ->maxSize(10240)
                    ->collection('Script_Scans')
                    ->acceptedFileTypes(['image/*'])
                    ->hidden(fn() => !request()->user()->hasAnyRole('iec', 'admin', 'glc', 'gsl')),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('student_id')
                    ->label('Student ID')
                    ->sortable()
                    ->copyable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('course.name')
                    ->state(fn($record) => $record->sub_course_id ? $record->course->name . ' - ' . $record->sub_course->name : $record->course->name),
                ...$this->markerEdits(),
                ...$this->moderatorEdits(),
                ...$this->adminDisplayScores(),
                Tables\Columns\ImageColumn::make('script_scan')
                    ->width(150)
                    ->height(150)
                    ->toggleable(isToggledHiddenByDefault: request()->user()->hasAnyRole('marker', 'moderator')), // Hide by default for markers and moderators
            ])
            ->defaultSort('student_id', 'asc')
            ->filters([
                Tables\Filters\SelectFilter::make('wrote_exam')
                    ->label('Wrote Exam?')
                    ->options([
                        1 => 'Present',
                        0 => 'Absent',
                    ])
                    ->query(function (Builder $query, array $data) {
                        if ($data['value'] === null) {
                            return $query;
                        }
                        return $query->whereHas('result', function (Builder $query) use ($data) {
                            $query->where('wrote_exam', $data['value']);
                        });
                    })
                    ->default(null),
                Tables\Filters\SelectFilter::make('course_or_subcourse')
                    ->form([
                        Select::make('course_or_subcourse')
                            ->options(function () {
                                // Get all courses and subcourses
                                $courses = $this->getOwnerRecord()->courses()->with('sub_courses')->get();
                                $options = [];

                                foreach ($courses as $course) {
                                    // Add the course itself to the options
                                    if ($course->sub_courses->isEmpty()) {
                                        $options[$course->id] = $course->name;
                                    } else {

                                        // Add subcourses with a prefix to differentiate
                                        foreach ($course->sub_courses as $subCourse) {
                                            $options["sc_{$subCourse->id}"] = "{$course->name} - {$subCourse->name}"; // Prefix subcourse IDs
                                        }
                                    }
                                }

                                return $options;
                            })
                            ->label('Course or Subcourse'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['course_or_subcourse'],
                            function (Builder $query, $course) {

                                // Check if the selected value is a course or subcourse
                                if (strpos($course, 'sc_') === 0) {
                                    // It's a subcourse ID
                                    $subCourseId = str_replace('sc_', '', $course); // Remove prefix
                                    $query->where('sub_course_id', $subCourseId);
                                } else {
                                    // It's a course ID
                                    $query->where('course_id', $course);
                                }
                            }
                        );
                    }),
                ...$this->adminResultsFilter(),
                Filter::make('marker_score_unconfirmed')
                    ->label('Unconfirmed Marker Scores')
                    ->query(fn(Builder $query): Builder => $query->where('marker_score_confirmed', false)),
                Filter::make('moderator_score_unconfirmed')
                    ->label('Unconfirmed Moderator Scores')
                    ->query(fn(Builder $query): Builder => $query->where('moderator_score_confirmed', false)),
            ])
            ->headerActions([
                NotifyPublishedResults::make('Notify')
                    ->hidden(!request()->user()->hasAnyRole('glc'))
                    ->arguments(['application' => $this->getOwnerRecord(), 'type' => 'entrance']),
                Tables\Actions\Action::make('confirm_scores') // Renamed to confirm_scores for generic use
                    ->label('Confirm Results')
                    ->action(function () {
                        $user = request()->user(); // Get the logged-in user
                        $records = $this->getFilteredRecords($user)->get(); // Execute the query here

                        Log::info('Confirming scores for user: ' . $user->id, ['records_count' => $records->count()]);

                        $confirmCount = 0;
                        $unconfirmedRecords = [];
                        foreach ($records as $record) {
                            // Skip if the result is exempted
                            if ($record->result?->is_exempted) {
                                continue;
                            }

                            // Check if the record has the necessary fields before confirming
                            if ($user->hasAnyRole('marker')) {
                                // Check if marker questions are provided (not null)
                                if ($record->filledQuestionsCount('marker_score') == $record->maxAnswerableQuestions()) {
                                    if (!$record->marker_score_confirmed) { // Avoid re-confirming
                                        $record->confirmMarkerScore();
                                    }
                                    $confirmCount++;
                                    Log::info('Confirmed marker score for record ID: ' . $record->id);
                                } else {
                                    $unconfirmedRecords[] = $record->student_id;
                                }
                            } elseif ($user->hasAnyRole('moderator')) {
                                if ($record->filledQuestionsCount('moderator_score') == $record->maxAnswerableQuestions()) {
                                    if (!$record->moderator_score_confirmed) { // Avoid re-confirming
                                        $record->confirmModeratorScore();
                                    }
                                    $confirmCount++;
                                    Log::info('Confirmed moderator score for record ID: ' . $record->id);
                                } else {
                                    $unconfirmedRecords[] = $record->student_id;
                                }
                            }
                        }
                        if (!empty($unconfirmedRecords)) {
                            Notification::make()
                                ->title('Fail count: ' . count($unconfirmedRecords) . '. Please fill all required questions for student IDs: ' . implode(', ', $unconfirmedRecords))
                                ->danger()
                                ->send();
                        }
                        Notification::make()
                            ->title('Results confirmation successful. Total confirmed: ' . $confirmCount)
                            ->success()
                            ->send();
                    })
                    ->requiresConfirmation()
                    ->color('success')
                    ->hidden(fn() => !request()->user()->hasAnyRole('marker', 'moderator')), // Show only for markers and moderators
            ])
            ->bulkActions([
                ReactivateMarks::make('Reactivate Score Entries')
                    ->hidden(fn() => !request()->user()->hasAnyRole('iec', 'admin')), // Show only for iec and admin roles
                ExportBulkAction::make(),
                \Filament\Tables\Actions\DeleteBulkAction::make()
                    ->label('Delete Selected')
                    ->requiresConfirmation()
                    ->before(function ($records, $action) {
                        $user = request()->user();
                        $blocked = collect();
                        foreach ($records as $record) {
                            $hasMarks = $record->questions()->whereNotNull('marker_score')->orWhereNotNull('moderator_score')->exists();
                            if ($hasMarks && $user->email !== '<EMAIL>') {
                                $blocked->push($record->student_id);
                            }
                        }
                        if ($blocked->isNotEmpty()) {
                            \Filament\Notifications\Notification::make()
                                ->title('Cannot delete scripts for student IDs: ' . $blocked->implode(', ') . ' (marks already entered).')
                                ->danger()
                                ->send();
                            $action->cancel();
                        }
                    })
                    ->visible(fn() => request()->user()->hasAnyRole('admin', 'iec')),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->hidden(fn() => !request()->user()->hasAnyRole('iec', 'admin', 'glc', 'gsl')),
                Tables\Actions\DeleteAction::make()
                    ->label('Delete')
                    ->requiresConfirmation()
                    ->before(function ($record, $action) {
                        // Prevent deletion if any marks are entered, except for superadmin
                        $user = request()->user();
                        $hasMarks = $record->questions()->whereNotNull('marker_score')->orWhereNotNull('moderator_score')->exists();
                        if ($hasMarks && $user->email !== '<EMAIL>') {
                            \Filament\Notifications\Notification::make()
                                ->title('Cannot delete script with marks entered.')
                                ->danger()
                                ->send();
                            $action->cancel();
                        }
                    })
                    ->visible(fn() => request()->user()->hasAnyRole('admin')),
            ])
            ->modifyQueryUsing(function (Builder $query) {
                return $this->getFilteredRecords(request()->user(), $query); // Return the query builder
            });
    }

    public function markerEdits(): array
    {
        $user = request()->user();
        return $user->hasAnyRole('marker') ? array_merge(
            (function () {
                $firstRecord = $this->getFirstRecordFromFilteredQuery();
                $maxQuestions = $firstRecord ? $firstRecord->maxAnswerableQuestions() : self::DEFAULT_SCRIPT_MAX_ANSWERABLE_QUESTIONS;

                return collect(range(0, $maxQuestions - 1))->map(function ($index) use ($firstRecord) {
                    return Tables\Columns\TextInputColumn::make("questions.$index.marker_score")
                        ->extraAttributes(['class' => 'small-input table-header-wrap'])
                        ->label(fn() => $firstRecord?->markerLabel($index + 1))
                        ->state(function (Script $record) use ($index) { // Added Script type hint
                            if ($record->result?->is_exempted) {
                                return 'Exempted';
                            }
                            $question = Question::firstOrCreate([
                                'script_id' => $record->id,
                                'pass_mark' => 0,
                                'score_denominator' => 25,
                                'question_number' => $index + 1,
                            ]);
                            return $question->marker_score ?? null;
                        })
                        ->disabled(fn(Script $record) => $record->result?->is_exempted || !$record->canEditMarker($index + 1)) // Added Script type hint
                        ->updateStateUsing(function (Script $record, $state) { // Added Script type hint
                            if ($record->result?->is_exempted) {
                                return null;
                            }
                            return null;
                        })
                        ->beforeStateUpdated(function ($record, $state) use ($index) {
                            // Custom logic to save the marker score
                            $scriptId = $record->id;
                            $totalScore = $record?->questions->sum('marker_score');
                            $newScore = $totalScore - ($record->questions->firstWhere('question_number', $index + 1)->marker_score ?? 0) + $state;

                            if ($record->result?->is_exempted) {
                                return; // Do not update if exempted
                            }

                            if ($state > $record->questionScoreDenominator($index + 1)) {
                                Notification::make()
                                    ->title('The score for question ' . ($index + 1) . ' cannot exceed ' . $record->questionScoreDenominator($index + 1))
                                    ->danger()
                                    ->send();
                            } elseif ($newScore > $record->scriptScoreDenominator()) {
                                Notification::make()
                                    ->title('The total score of all questions cannot exceed ' . $record->scriptScoreDenominator())
                                    ->danger()
                                    ->send();
                            } else {
                                if ($scriptId) {
                                    $script = Script::find($scriptId);
                                    if ($script) {
                                        $question = $script->questions->firstWhere('question_number', $index + 1);
                                        $question->marker_score = $state;
                                        $question->save();
                                    }
                                }
                            }
                        })
                        ->rules(['nullable', 'numeric', 'max:' . ($this->questionScoreDenominator($index + 1))]); // Pass $record to the function
                })->toArray();
            })(),
            [
                Tables\Columns\TextColumn::make('marker_score')
                    ->label("Marker Total")
                    ->numeric()
                    ->icon(fn($record) => $record->marker_score_confirmed ? 'heroicon-s-check-circle' : 'heroicon-s-minus-circle')
                    ->extraAttributes(fn($record) => [
                        'title' => $record->marker_score_confirmed ? 'Score confirmed' : 'Score not confirmed',
                    ])
                    ->state(function (Script $record) { // Added Script type hint
                        if ($record->result?->is_exempted) {
                            return 'Exempted';
                        }
                        $total = !$record ? null : $record->questions->sum('marker_score');
                        return $total !== null ? $this->formatScore($total) : null;
                    }),
                Tables\Columns\ToggleColumn::make('wrote_exam')
                    ->state(fn($record) => $record?->result?->wrote_exam)
                    ->disabled()
                    ->label('Wrote Exam'),
            ]
        ) : [];
    }

    public function adminDisplayScores(): array
    {
        $user = request()->user();
        return $user->hasAnyRole('admin', 'iec', 'glc', 'gsl') ? [
            ...$this->markerDisplayScores(),
            ...$this->moderatorDisplayScores(),
            Tables\Columns\ToggleColumn::make('wrote_exam')
                ->state(fn($record): bool => $record?->result?->wrote_exam)
                ->disabled()
                ->label('Wrote Exam'),
        ] : [];
    }

    public function markerDisplayScores(): array
    {
        return array_merge(
            [
                Tables\Columns\TextColumn::make('marker.full_name')
                    ->label('Marker')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
            ],

            (function () {
                $firstRecord = $this->getFirstRecordFromFilteredQuery() ?? $this->getOwnerRecord()->scripts->first();
                $maxQuestions = $firstRecord ? $firstRecord->maxAnswerableQuestions() : self::DEFAULT_SCRIPT_MAX_ANSWERABLE_QUESTIONS;

                return collect(range(0, $maxQuestions - 1))->map(function ($index) use ($firstRecord) {
                    return Tables\Columns\TextColumn::make("questions.{$index}.marker_score")
                        ->label(fn() => $firstRecord?->markerLabel($index + 1))
                        ->toggleable(isToggledHiddenByDefault: true)
                        ->formatStateUsing(function ($state, Script $record) use ($index) {
                            if ($record->result?->is_exempted) {
                                return 'Exempted';
                            }
                            $question = Question::firstOrCreate([
                                'script_id' => $record->id,
                                'pass_mark' => 0,
                                'score_denominator' => 25,
                                'question_number' => $index + 1,
                            ]);
                            $score = $question->marker_score;
                            return $score !== null ? $this->formatScore($score) : null;
                        })
                        ->toggleable(isToggledHiddenByDefault: true);
                })->toArray();
            })(),
            [
                Tables\Columns\TextColumn::make('marker_score')
                    ->label("Marker Total")
                    ->numeric()
                    ->icon(fn($record) => $record->marker_score_confirmed ? 'heroicon-s-check-circle' : 'heroicon-s-minus-circle')
                    ->extraAttributes(fn($record) => [
                        'title' => $record->marker_score_confirmed ? 'Score confirmed' : 'Score not confirmed',
                    ])
                    ->state(function (Script $record) { // Added Script type hint
                        if ($record->result?->is_exempted) {
                            return 'Exempted';
                        }
                        // Calculate the total marker score by summing all question scores
                        $total = !$record ? null : $record->questions->sum('marker_score');
                        return $total !== null ? $this->formatScore($total) : null;
                    }),
            ],
        );
    }

    public function moderatorDisplayScores(): array
    {
        return array_merge(
            [
                Tables\Columns\TextColumn::make('moderator.full_name')
                    ->label('Moderator')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
            ],

            (function () {
                $firstRecord = $this->getFirstRecordFromFilteredQuery() ?? $this->getOwnerRecord()->scripts->first();
                $maxQuestions = $firstRecord ? $firstRecord->maxAnswerableQuestions() : self::DEFAULT_SCRIPT_MAX_ANSWERABLE_QUESTIONS;

                return collect(range(0, $maxQuestions - 1))->map(function ($index) use ($firstRecord) {
                    return Tables\Columns\TextColumn::make("questions.{$index}.moderator_score")
                        ->label(fn() => $firstRecord?->moderatorLabel($index + 1))
                        ->formatStateUsing(function ($state, Script $record) use ($index) {
                            if ($record->result?->is_exempted) {
                                return 'Exempted';
                            }
                            $question = Question::firstOrCreate([
                                'script_id' => $record->id,
                                'pass_mark' => 0,
                                'score_denominator' => 25,
                                'question_number' => $index + 1,
                            ]);
                            $score = $question->moderator_score;
                            return $score !== null ? $this->formatScore($score) : null;
                        })
                        ->toggleable(isToggledHiddenByDefault: true);
                })->toArray();
            })(),
            [
                Tables\Columns\TextColumn::make('moderator_score')
                    ->label("Moderator Total")
                    ->numeric()
                    ->icon(fn($record) => $record->moderator_score_confirmed ? 'heroicon-s-check-circle' : 'heroicon-s-minus-circle')
                    ->extraAttributes(fn($record) => [
                        'title' => $record->moderator_score_confirmed ? 'Score confirmed' : 'Score not confirmed',
                    ])
                    ->state(function (Script $record) { // Added Script type hint
                        if ($record->result?->is_exempted) {
                            return 'Exempted';
                        }
                        $total = (!$record || !$record->isMarkerScoreWithinThreshold()) ? null : $record->questions->sum('moderator_score');
                        return $total !== null ? $this->formatScore($total) : null;
                    }),
            ],
        );
    }


    public function moderatorEdits(): array
    {
        $user = request()->user();

        return $user->hasAnyRole('moderator') ? array_merge(
            (function () {
                $firstRecord = $this->getFirstRecordFromFilteredQuery() ?? $this->getOwnerRecord()->scripts->first();
                $maxQuestions = $firstRecord ? $firstRecord->maxAnswerableQuestions() : self::DEFAULT_SCRIPT_MAX_ANSWERABLE_QUESTIONS;

                return collect(range(0, $maxQuestions - 1))->map(function ($index) use ($firstRecord) {
                    return Tables\Columns\TextInputColumn::make("questions.{$index}.moderator_score")
                        ->extraAttributes(['class' => 'small-input'])
                        ->label(fn() => $firstRecord?->moderatorLabel($index + 1))
                        ->disabled(fn(Script $record) => $record->result?->is_exempted || true) // Always disabled for moderator, check exemption first
                        ->state(function (Script $record) use ($index) { // Added Script type hint
                            if ($record->result?->is_exempted) {
                                return 'Exempted';
                            }
                            $question = Question::firstOrCreate([
                                'script_id' => $record->id,
                                'pass_mark' => 0,
                                'score_denominator' => 25,
                                'question_number' => $index + 1,
                            ]);
                            return $question->marker_score ?? null;
                        })
                        ->rules(['required', 'numeric']);
                })->toArray();
            })(),
            [
                Tables\Columns\TextColumn::make('marker_score')
                    ->label("Marker Total")
                    ->icon(fn($record) => $record->marker_score_confirmed ? 'heroicon-s-check-circle' : 'heroicon-s-minus-circle')
                    ->extraAttributes(fn($record) => [
                        'title' => $record->marker_score_confirmed ? 'Score confirmed' : 'Score not confirmed',
                    ])
                    ->numeric()
                    ->state(function (Script $record) { // Added Script type hint
                        if ($record->result?->is_exempted) {
                            return 'Exempted';
                        }
                        $total = !$record ? null : $record->questions->sum('marker_score');
                        return $total !== null ? $this->formatScore($total) : null;
                    }),
                Tables\Columns\ToggleColumn::make('wrote_exam')
                    ->state(fn($record) => $record?->result?->wrote_exam)
                    ->disabled()
                    ->label('Wrote Exam'),
            ],
            (function () {
                $firstRecord = $this->getFirstRecordFromFilteredQuery() ?? $this->getOwnerRecord()->scripts->first();
                $maxQuestions = $firstRecord ? $firstRecord->maxAnswerableQuestions() : self::DEFAULT_SCRIPT_MAX_ANSWERABLE_QUESTIONS;

                return collect(range(0, $maxQuestions - 1))->map(function ($index) use ($firstRecord) {
                    return Tables\Columns\TextInputColumn::make("questions.{$index}.moderator_score")
                        ->extraAttributes(['class' => 'small-input'])
                        ->label(fn() => $firstRecord?->moderatorLabel($index + 1)) // Label for each question
                        ->state(function (Script $record) use ($index) { // Added Script type hint
                            if ($record->result?->is_exempted) {
                                return 'Exempted';
                            }
                            $question = Question::firstOrCreate([
                                'script_id' => $record->id,
                                'pass_mark' => 0,
                                'score_denominator' => 25,
                                'question_number' => $index + 1,
                            ]);
                            return $question->moderator_score ?? null;
                        })
                        ->disabled(fn(Script $record) => $record->result?->is_exempted || !$record->canEditModerator($index + 1)) // Added Script type hint
                        ->updateStateUsing(function (Script $record, $state) use ($index) { // Added Script type hint
                            if ($record->result?->is_exempted) {
                                return null;
                            }
                            return null;
                        })
                        ->beforeStateUpdated(function (Script $record, $state) use ($index) { // Added Script type hint
                            // Custom logic to save the marker score
                            $scriptId = $record->id;
                            $totalScore = $record?->questions->sum('moderator_score');
                            $newScore = $totalScore - ($record->questions->firstWhere('question_number', '>', $index + 1)->moderator_score ?? 0) + $state;
                            if ($record->result?->is_exempted) {
                                return; // Do not update if exempted
                            }

                            if ($state > $record->questionScoreDenominator($index + 1)) {
                                Notification::make()
                                    ->title('The score for question ' . ($index + 1) . ' cannot exceed ' . $record->questionScoreDenominator($index + 1))
                                    ->danger()
                                    ->send();
                            } elseif ($newScore > $record->scriptScoreDenominator()) {
                                Notification::make()
                                    ->title('The total score of all questions cannot exceed ' . $record->scriptScoreDenominator())
                                    ->danger()
                                    ->send();
                            } else {
                                if ($scriptId) {
                                    $script = Script::find($scriptId);
                                    if ($script) {
                                        $question = $script->questions->firstWhere('question_number', $index + 1);
                                        $question->moderator_score = $state;
                                        $question->save();
                                    }
                                }
                            }
                        })
                        ->rules(['required', 'numeric', 'max:' . ($this->questionScoreDenominator($index + 1))]);
                })->toArray();
            })(),
            [
                Tables\Columns\TextColumn::make('moderator_score')
                    ->label("Moderator Total")
                    ->numeric()
                    ->icon(fn($record) => $record->moderator_score_confirmed ? 'heroicon-s-check-circle' : 'heroicon-s-minus-circle')
                    ->extraAttributes(fn($record) => [
                        'title' => $record->moderator_score_confirmed ? 'Score confirmed' : 'Score not confirmed',
                    ])
                    ->state(function (Script $record) { // Added Script type hint
                        if ($record->result?->is_exempted) {
                            return 'Exempted';
                        }
                        $total = !$record ? null : $record->questions->sum('moderator_score');
                        return $total !== null ? $this->formatScore($total) : null;
                    }),
            ]
        ) : [];
    }


    public function adminResultsFilter()
    {
        return request()->user()->hasAnyRole('admin', 'iec') ? [
            Tables\Filters\SelectFilter::make('section')
                ->options([
                    'A' => 'Section A',
                    'B' => 'Section B',
                ]),
            Tables\Filters\SelectFilter::make('marker_id')
                ->label('Markers')
                ->relationship('marker', 'full_name'),
            Tables\Filters\SelectFilter::make('moderator_id')
                ->label('Moderators')
                ->relationship('moderator', 'full_name'),
        ] : [];
    }

    public function isReadOnly(): bool
    {
        return false;
    }

    public static function canViewForRecord($ownerRecord, string $pageClass): bool
    {
        return $ownerRecord->transaction->name !== 'entrance';
    }


protected function getFirstRecordFromFilteredQuery()
{
    $user = request()->user();
    $query = $this->getOwnerRecord()->scripts();

    // Apply the same filters as the table
    $filters = $this->getTableFilters();

    if (!empty($filters['course_or_subcourse']['course_or_subcourse'])) {
        $course = $filters['course_or_subcourse']['course_or_subcourse'];
        if (strpos($course, 'sc_') === 0) {
            $subCourseId = str_replace('sc_', '', $course);
            $query->where('sub_course_id', $subCourseId);
        } else {
            $query->where('course_id', $course);
        }
    }

    // Add user-specific filtering for markers and moderators
    if ($user->hasAnyRole('marker')) {
        $query->where('marker_id', $user->id);
    } elseif ($user->hasAnyRole('moderator')) {
        $query->where('moderator_id', $user->id);
    }

    return $query->first();
}
    protected function getFilteredRecords($user, $query = null)
    {
        $query = $query ?? $this->getOwnerRecord()->scripts(); // Start with the related scripts query

        $filteredRecords = $query->where('application_id', $this->getOwnerRecord()->id);

        if ($user->hasAnyRole('marker')) {
            $filteredRecords->where('marker_id', $user->id);
        } elseif ($user->hasAnyRole('moderator')) {
            $filteredRecords->where('moderator_id', $user->id);
        }

        return $filteredRecords; // Return the query builder instead of executing it
    }

    protected function questionLabel($questionNumber)
    {
        $labels = [
            // 1 => 'Knowledge of Facts & Correct Analysis of Issues Involved',
            // 2 => 'Clarity & Organisation of Arguments',
            // 3 => 'Clarity of Expression/Diction',
            // 4 => 'Presentation Including Appearance',
            1 => 'Knowledge of Facts',
            2 => 'Clarity Arguments',
            3 => 'Clarity of Expression',
            4 => 'Presentation',
        ];

        return 'Q' . $questionNumber;
        // return $this->isPracticalScript() ? 'Q' . $questionNumber . ' ' . $labels[$questionNumber] : 'Q' . $questionNumber;
    }

    public function getAdvocacyQuestionScoreDenominator($questionNumber)
    {
        $questionScoreDenominator = [
            1 => 28,
            2 => 14,
            3 => 14,
            4 => 14,
        ];

        return $questionScoreDenominator[$questionNumber] ?? 14; // Default to 0 for other questions
    }

    public function getPracticalsQuestionScoreDenominator($questionNumber)
    {
        $questionScoreDenominator = [
            1 => 10,
            2 => 7,
            3 => 7,
            4 => 6,
        ];

        return $questionScoreDenominator[$questionNumber] ?? 15; // Default to 0 for other questions
    }

    public function questionScoreDenominator($questionNumber)
    {
        // if ($this->isAdvocacyScript()) {
        //     return $this->getAdvocacyQuestionScoreDenominator($questionNumber);
        // } elseif ($this->isPracticalScript()) {
        //     return $this->getPracticalsQuestionScoreDenominator($questionNumber);
        // }

        return 50; // Default score for non-advocacy scripts
    }


}
