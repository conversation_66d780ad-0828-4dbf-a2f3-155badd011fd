<?php

namespace App\Filament\Resources\ApplicationResource\Pages;

use App\Filament\Resources\ApplicationResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;

class ViewApplication extends ViewRecord
{
    protected static string $resource = ApplicationResource::class;

    protected static string $view = "filament.resources.pages.view-relation-manager-only";

    public function getTitle(): string|Htmlable
    {
        return $this->record->name;
    }
}
