<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EVoucherResource\Pages;
use App\Models\EVoucher;
use App\Models\Application;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use pxlrbt\FilamentExcel\Actions\Pages\ExportAction;
use App\Filament\Exports\EVoucherExporter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EVoucherResource extends Resource
{
    protected static ?string $model = EVoucher::class;

    protected static ?string $navigationIcon = 'heroicon-o-ticket';
    
    protected static ?string $navigationLabel = 'E-Vouchers';
    
    protected static ?string $pluralModelLabel = 'E-Vouchers';
    
    protected static ?string $navigationGroup = 'Transactions';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('voucher_code')
                    ->label('Voucher Code')
                    ->disabled()
                    ->required(),
                Forms\Components\TextInput::make('first_name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('last_name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('email')
                    ->email()
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('phone')
                    ->tel()
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('amount')
                    ->numeric()
                    ->prefix('GHS')
                    ->required(),
                Forms\Components\Select::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'paid' => 'Paid',
                        'redeemed' => 'Redeemed',
                        'expired' => 'Expired',
                    ])
                    ->required(),
                Forms\Components\Select::make('application_id')
                    ->label('Application')
                    ->relationship('application', 'name')
                    ->required(),
                Forms\Components\TextInput::make('payment_reference')
                    ->maxLength(255),
                Forms\Components\DateTimePicker::make('paid_at'),
                Forms\Components\DateTimePicker::make('redeemed_at'),
                Forms\Components\TextInput::make('redeemed_by')
                    ->label('Redeemed By (Cred ID)')
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('voucher_code')
                    ->label('Voucher Code')
                    ->searchable()
                    ->sortable()
                    ->copyable(),
                Tables\Columns\TextColumn::make('full_name')
                    ->label('Customer')
                    ->searchable(['first_name', 'last_name'])
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->copyable(),
                Tables\Columns\TextColumn::make('phone')
                    ->searchable(),
                Tables\Columns\TextColumn::make('application.name')
                    ->label('Application')
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->money('GHS')
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'warning' => 'pending',
                        'success' => 'paid',
                        'primary' => 'redeemed',
                        'danger' => 'expired',
                    ]),
                Tables\Columns\TextColumn::make('payment_reference')
                    ->label('Payment Ref')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('paid_at')
                    ->label('Paid Date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('redeemed_at')
                    ->label('Redeemed Date')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'paid' => 'Paid',
                        'redeemed' => 'Redeemed',
                        'expired' => 'Expired',
                    ]),
                SelectFilter::make('application_id')
                    ->label('Application')
                    ->relationship('application', 'name'),
                Filter::make('paid_vouchers')
                    ->label('Paid Vouchers')
                    ->query(fn (Builder $query): Builder => $query->where('status', 'paid')),
                Filter::make('redeemed_vouchers')
                    ->label('Redeemed Vouchers')
                    ->query(fn (Builder $query): Builder => $query->where('status', 'redeemed')),
                Filter::make('recent')
                    ->label('Last 7 Days')
                    ->query(fn (Builder $query): Builder => $query->where('created_at', '>=', now()->subDays(7))),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVouchers::route('/'),
            'create' => Pages\CreateVoucher::route('/create'),
            'view' => Pages\ViewVoucher::route('/{record}'),
            'edit' => Pages\EditVoucher::route('/{record}/edit'),
        ];
    }

    public static function canViewAny(): bool
    {
        return request()->user()->hasAnyRole('admin', 'accountant', 'iec');
    }
}
