<?php

namespace App\Filament\Resources\EntranceResource\Pages;

use App\Filament\Resources\EntranceResource;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;

class ViewEntrance extends ViewRecord
{
    protected static string $resource = EntranceResource::class;

    protected static string $view = "filament.resources.pages.view-relation-manager-only";

    public function getTitle(): string|Htmlable
    {
        return $this->record->name;
    }
}
