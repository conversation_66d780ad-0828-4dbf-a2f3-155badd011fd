<?php

namespace App\Filament\Resources\EntranceResource\RelationManagers;

use App\Exports\CollectionExport;
use App\Filament\Actions\EntranceResults\ReactivateMarks;
use App\Filament\Actions\TableActions\NotifyPublishedResults;
use App\Filament\Actions\TableActions\PublishResults;
use App\Filament\Resources\ResourceUtilities\ResourceUtilities;
use App\Models\Script;
use App\Models\Setting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class EntranceResultsRelationManager extends RelationManager
{
    protected static string $relationship = 'scripts';

    protected static ?string $title = 'Results';
    protected static ?string $modelLabel = 'Scripts';

    public function getTableRecordKey(Model $record): string
    {
        return $record->student_id;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('student_id')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('student_id')
                  ->label('Student ID')
                  ->sortable()
                  ->copyable()
                  ->searchable(),
                ...$this->markerDisplayScores(),
                ...$this->moderatorDisplayScores(),
                Tables\Columns\TextColumn::make('final_score')
                    ->label('Final Score')
                    ->state(function ($record) {
                        if(!$record || (is_null($record->sec_a_moderator_score) || is_null($record->sec_b_moderator_score))) return null;
                        return ($record->sec_a_moderator_score ?? 0) + ($record->sec_b_moderator_score ?? 0);
                    }),
                ...$this->iecScores(),
                Tables\Columns\TextColumn::make('final_score_remarks')
                    ->label('Remarks')
                    ->state(function ($record) {
                        if(!$record || (is_null($record->sec_a_moderator_score) || is_null($record->sec_b_moderator_score))) return null;
                        return (($record->sec_a_moderator_score ?? 0) + ($record->sec_b_moderator_score ?? 0)) >= 50 ? 'Passed' : 'Failed';
                    }),
            ])
            ->defaultSort('student_id', 'asc')
            ->filters([
                ...$this->adminResultsFilter()
            ])
            ->headerActions([
                NotifyPublishedResults::make('Notify')
                    ->hidden(!request()->user()->hasAnyRole('glc'))
                    ->arguments(['application' => $this->getOwnerRecord(), 'type' => 'entrance']),
                PublishResults::make('Publish')
                    ->hidden(!request()->user()->hasAnyRole('gsl'))
                    ->arguments(['application' => $this->getOwnerRecord(), 'type' => 'entrance']),
                Tables\Actions\Action::make('Download Composite')
                    ->hidden(!request()->user()->hasAnyRole('admin', 'iec'))
                    ->action(function () {
                        return Excel::download(new CollectionExport($this->getOwnerRecord()->sectionComposite()), "composite.xlsx");
                    }),
            ])
            ->bulkActions([
                ExportBulkAction::make(),
            ])
            ->modifyQueryUsing(function (Builder $query) {
                return $this->getFilteredRecords(request()->user(), $query) // Return the query builder
                  ->selectRaw('scripts.student_id,
                  MAX(scripts.section) as section,
                  MAX(CASE WHEN scripts.section = "A" THEN marker_que_one END) as sec_a_marker_que_one,
                  MAX(CASE WHEN scripts.section = "A" THEN marker_que_two END) as sec_a_marker_que_two,
                  MAX(CASE WHEN scripts.section = "B" THEN marker_que_one END) as sec_b_marker_que_one,
                  MAX(CASE WHEN scripts.section = "B" THEN marker_que_two END) as sec_b_marker_que_two,
                  MAX(CASE WHEN scripts.section = "A" THEN moderator_que_one END) as sec_a_moderator_que_one,
                  MAX(CASE WHEN scripts.section = "A" THEN moderator_que_two END) as sec_a_moderator_que_two,
                  MAX(CASE WHEN scripts.section = "B" THEN moderator_que_one END) as sec_b_moderator_que_one,
                  MAX(CASE WHEN scripts.section = "B" THEN moderator_que_two END) as sec_b_moderator_que_two,
                  MAX(CASE WHEN scripts.section = "A" THEN marker_id END) as sec_a_marker_id,
                  MAX(CASE WHEN scripts.section = "B" THEN marker_id END) as sec_b_marker_id,
                  MAX(CASE WHEN scripts.section = "A" THEN moderator_id END) as sec_a_moderator_id,
                  MAX(CASE WHEN scripts.section = "B" THEN moderator_id END) as sec_b_moderator_id,
                  MAX(CASE WHEN scripts.section = "A" THEN marker_score END) as sec_a_marker_score,
                  MAX(CASE WHEN scripts.section = "B" THEN marker_score END) as sec_b_marker_score,
                  MAX(CASE WHEN scripts.section = "A" THEN moderator_score END) as sec_a_moderator_score,
                  MAX(CASE WHEN scripts.section = "B" THEN moderator_score END) as sec_b_moderator_score,
                  MAX(CASE WHEN scripts.section = "A" THEN markers.full_name END) as sec_a_marker_full_name,
                  MAX(CASE WHEN scripts.section = "B" THEN markers.full_name END) as sec_b_marker_full_name,
                  MAX(CASE WHEN scripts.section = "A" THEN moderators.full_name END) as sec_a_moderator_full_name,
                  MAX(CASE WHEN scripts.section = "B" THEN moderators.full_name END) as sec_b_moderator_full_name
                  ')
                  ->leftJoin('users as markers', 'markers.id', '=', 'scripts.marker_id') // Join with markers table
                  ->leftJoin('users as moderators', 'moderators.id', '=', 'scripts.moderator_id') // Join with users table for moderators
                  ->groupBy('scripts.student_id'); // Group by student_id only
            });
    }


    public function iecScores(): array
    {
        $user = request()->user();
        return $user->hasAnyRole('iec', 'glc', 'gsl') ? [
            Tables\Columns\TextInputColumn::make('final_score')
                ->label('Final Score')
                ->disabled(fn() => !empty($this->getOwnerRecord()->exam_published_date))
                ->tooltip(fn() => !empty($this->getOwnerRecord()->exam_published_date) ? 'Final score is disabled because results have been published.' : '') // Added tooltip
                ->state(function ($record) {
                    if(!$record || (is_null($record->sec_a_moderator_score) || is_null($record->sec_b_moderator_score))) return null;
                    return ($record->sec_a_moderator_score ?? 0) + ($record->sec_b_moderator_score ?? 0);
                }),
        ] : [];
    }

    public function markerDisplayScores(): array
    {

        return request()->user()->hasAnyRole('admin', 'iec', 'glc', 'gsl') ? [
            Tables\Columns\TextColumn::make('sec_a_marker_full_name') // Changed to full name
                ->label('Sec A Marker')
                ->toggleable(isToggledHiddenByDefault: true)
                ->searchable(),
            Tables\Columns\TextColumn::make('sec_a_marker_que_one')
                ->toggleable(isToggledHiddenByDefault: true)
                ->label('Marker Sec A Q1'),
            Tables\Columns\TextColumn::make('sec_a_marker_que_two')
                ->toggleable(isToggledHiddenByDefault: true)
                ->label('Marker Sec A Q2'),
            Tables\Columns\TextColumn::make('sec_a_marker_score')
                ->toggleable(isToggledHiddenByDefault: true)
                ->label('Marker Sec A Total'),
            Tables\Columns\TextColumn::make('sec_b_marker_full_name') // Changed to full name
                ->label('Sec B Marker')
                ->toggleable(isToggledHiddenByDefault: true)
                ->searchable(),
            Tables\Columns\TextColumn::make('sec_b_marker_que_one')
                ->toggleable(isToggledHiddenByDefault: true)
                ->label('Marker Sec B Q1'),
            Tables\Columns\TextColumn::make('sec_b_marker_que_two')
                ->toggleable(isToggledHiddenByDefault: true)
                ->label('Marker Sec B Q2'),
            Tables\Columns\TextColumn::make('sec_b_marker_score')
                ->toggleable(isToggledHiddenByDefault: true)
                ->label('Marker Sec B Total'),
            Tables\Columns\TextColumn::make('marker_score')
                ->label('Total Marker Score')
                ->numeric()
                ->state(function ($record) {
                    if(!$record || (is_null($record->sec_a_marker_score) && is_null($record->sec_b_marker_score))) return null;
                    return ($record->sec_a_marker_score ?? 0) + ($record->sec_b_marker_score ?? 0);
                }),
        ] : [];
    }

    public function moderatorDisplayScores(): array
    {
        return request()->user()->hasAnyRole('admin', 'iec', 'glc', 'gsl') ? [
            Tables\Columns\TextColumn::make('sec_a_moderator_full_name')
                ->label('Sec A Moderator')
                ->toggleable(isToggledHiddenByDefault: true)
                ->searchable(),
            Tables\Columns\TextColumn::make('sec_a_moderator_que_one')
                ->toggleable(isToggledHiddenByDefault: true)
                ->label('Moderator Sec A Q1'),
            Tables\Columns\TextColumn::make('sec_a_moderator_que_two')
                ->toggleable(isToggledHiddenByDefault: true)
                ->label('Moderator Sec A Q2'),
            Tables\Columns\TextColumn::make('sec_a_moderator_score')
                ->toggleable(isToggledHiddenByDefault: true)
                ->label('Moderator Sec A Total'),
            Tables\Columns\TextColumn::make('sec_b_moderator_full_name')
                ->label('Sec B Moderator')
                ->toggleable(isToggledHiddenByDefault: true)
                ->searchable(),
            Tables\Columns\TextColumn::make('sec_b_moderator_que_one')
                ->toggleable(isToggledHiddenByDefault: true)
                ->label('Moderator Sec B Q1'),
            Tables\Columns\TextColumn::make('sec_b_moderator_que_two')
                ->toggleable(isToggledHiddenByDefault: true)
                ->label('Moderator Sec B Q2'),
            Tables\Columns\TextColumn::make('sec_b_moderator_score')
                ->toggleable(isToggledHiddenByDefault: true)
                ->label('Moderator Sec B Total'),
            Tables\Columns\TextColumn::make('moderator_score')
                ->label('Total Moderator Score')
                ->numeric()
                ->state(function ($record) {
                    if(!$record || (is_null($record->sec_a_moderator_score) && is_null($record->sec_b_moderator_score))) return null;
                    return ($record->sec_a_moderator_score ?? 0) + ($record->sec_b_moderator_score ?? 0);
                }),
            // Tables\Columns\TextColumn::make('moderator_remarks')
            //     ->label('Moderator Remarks'),
        ] : [];
    }



    public function adminResultsFilter()
    {
        return request()->user()->hasAnyRole('admin', 'iec') ? [
            Tables\Filters\SelectFilter::make('marker_id')
                ->label('Markers')
                ->relationship('marker', 'full_name'),
            Tables\Filters\SelectFilter::make('moderator_id')
                ->label('Moderators')
                ->relationship('moderator', 'full_name'),
        ] : [];
    }

    public function isReadOnly(): bool
    {
        return false;
    }

    public static function canViewForRecord($ownerRecord, string $pageClass): bool
    {
        return request()->user()->hasAnyRole('admin', 'iec', 'glc', 'gsl');
    }

    private function wroteExam($record): bool
    {
        // Check if the related submission exists and if the student wrote the exam
        $submission = $record->submission; // Assuming $record is an instance of Script
        return $submission && $submission->wrote_exam;
    }

    protected function getFilteredRecords($user, $query = null)
    {
        $query = $query ?? $this->getOwnerRecord()->scripts(); // Start with the related scripts query
        $filteredRecords = $query->where('application_id', $this->getOwnerRecord()->id);

        return $filteredRecords; // Return the query builder instead of executing it
    }

    public function save()
    {
        $data = $this->form->getState();
        $studentId = $data['student_id']; // Assuming you have the student ID

        // Retrieve both scripts for the student (sections A and B)
        $scripts = Script::where('student_id', $studentId)->get();

        // Initialize total moderator scores
        $totalModeratorScoreA = 0;
        $totalModeratorScoreB = 0;

        // Calculate total moderator scores for sections A and B
        foreach ($scripts as $script) {
            if ($script->section === 'A') {
                $totalModeratorScoreA = $script->moderator_score ?? 0; // Get section A total moderator score
            } elseif ($script->section === 'B') {
                $totalModeratorScoreB = $script->moderator_score ?? 0; // Get section B total moderator score
            }
        }

        // Calculate final score as the sum of both total moderator scores
        $finalScore = $totalModeratorScoreA + $totalModeratorScoreB;

        // Update both scripts with the final score and overridden value
        foreach ($scripts as $script) {
            $script->final_score = $finalScore; // Assuming you have a final_score column
            $script->final_score_override = $data['final_score_override']; // Capture overridden value
            $script->save();
        }
    }
}
