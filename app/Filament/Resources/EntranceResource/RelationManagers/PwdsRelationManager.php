<?php

namespace App\Filament\Resources\EntranceResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PwdsRelationManager extends RelationManager
{
    protected static string $relationship = 'submissions';

    protected static ?string $modelLabel = "H";

    protected static ?string $title = "PWDs";

    protected static ?string $label = "H";

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('student_id')
            ->columns([
                Tables\Columns\TextColumn::make('student_id')
                    ->label('Candidate ID'),
                Tables\Columns\TextColumn::make('pwd_type')
                    ->label('PWD Type'),
                Tables\Columns\TextColumn::make('pwd_details')
                    ->label('PWD Details'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make('file')
                    ->label('Permission Letter')
                    ->icon('heroicon-o-document-arrow-down')
                    ->hidden(fn($record) => !($record->getMedia("pwd")[0] ?? null))
                    ->url(function ($record) {
                        $file = $record->getMedia("pwd")[0] ?? [];
                        return $file ? $file->getUrl() : '#';
                    })
                    ->openUrlInNewTab(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->modifyQueryUsing(fn(Builder $query) => $query->where('disability', 1));
    }

    public static function canViewForRecord($ownerRecord, string $pageClass): bool
    {
        return request()->user()->hasAnyRole(['iec', 'admin']);
    }
}
