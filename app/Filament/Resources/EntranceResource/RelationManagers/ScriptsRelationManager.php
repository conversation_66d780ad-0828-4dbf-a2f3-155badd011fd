<?php

namespace App\Filament\Resources\EntranceResource\RelationManagers;

use App\Filament\Actions\EntranceResults\ReactivateMarks;
use App\Filament\Actions\TableActions\NotifyPublishedResults;
use App\Filament\Resources\ResourceUtilities\ResourceUtilities;
use App\Models\Setting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Log;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;

class ScriptsRelationManager extends RelationManager
{
    protected static string $relationship = 'scripts';

    protected static ?string $title = 'Scripts';
    protected static ?string $modelLabel = 'Scripts';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('student_id')
                    ->disabled()
                    ->required()
                    ->maxLength(255),
                Forms\Components\SpatieMediaLibraryFileUpload::make('script_scan')
                    ->label('Upload Script Scan')
                    ->downloadable()
                    ->maxSize(10240)
                    ->collection('Script_Scans')
                    ->acceptedFileTypes(['image/*'])
                    ->hidden(fn() => !request()->user()->hasAnyRole('iec', 'admin', 'glc', 'gsl')),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('student_id')
                  ->label('Student ID')
                  ->sortable()
                  ->copyable()
                  ->searchable(),
                Tables\Columns\TextColumn::make('section'),
                ...$this->markerEdits(),
                ...$this->moderatorEdits(),
                ...$this->adminDisplayScores(),
                Tables\Columns\ImageColumn::make('script_scan')
                    ->width(150)
                    ->height(150)
                    ->toggleable(isToggledHiddenByDefault: request()->user()->hasAnyRole('marker', 'moderator')), // Hide by default for markers and moderators
            ])
            ->defaultSort('student_id', 'asc')
            ->filters([
                ...$this->adminResultsFilter()
            ])
            ->headerActions([
                NotifyPublishedResults::make('Notify')
                    ->hidden(!request()->user()->hasAnyRole('glc'))
                    ->arguments(['application' => $this->getOwnerRecord(), 'type' => 'entrance']),
                Tables\Actions\Action::make('confirm_scores') // Renamed to confirm_scores for generic use
                    ->label('Confirm Results')
                    ->action(function () {
                        $user = request()->user(); // Get the logged-in user
                        $records = $this->getFilteredRecords($user)->get(); // Execute the query here

                        Log::info('Confirming scores for user: ' . $user->id, ['records_count' => $records->count()]);

                        foreach ($records as $record) {
                            Log::info('Processing record ID: ' . $record->id, [
                                'marker_que_one' => $record->marker_que_one,
                                'marker_que_two' => $record->marker_que_two,
                                'moderator_que_one' => $record->moderator_que_one,
                                'moderator_que_two' => $record->moderator_que_two,
                                'marker_score_confirmed' => $record->marker_score_confirmed,
                                'moderator_score_confirmed' => $record->moderator_score_confirmed,
                            ]);

                            // Check if the record has the necessary fields before confirming
                            if ($user->hasAnyRole('marker')) {
                                // Check if marker questions are provided (not null)
                                if (!is_null($record->marker_que_one) && !is_null($record->marker_que_two)) {
                                    $this->confirmMarkerScore($record); // Confirm score for each eligible record
                                    Log::info('Confirmed marker score for record ID: ' . $record->id);
                                } else {
                                    Log::warning('Skipping record ID: ' . $record->id . ' due to missing marker scores.');
                                }
                            } elseif ($user->hasAnyRole('moderator')) {
                                // Check if moderator questions are provided (not null)
                                if (!is_null($record->moderator_que_one) && !is_null($record->moderator_que_two)) {
                                    $this->confirmModeratorScore($record); // Confirm score for each eligible record
                                    Log::info('Confirmed moderator score for record ID: ' . $record->id);
                                } else {
                                    Log::warning('Skipping record ID: ' . $record->id . ' due to missing moderator scores.');
                                }
                            }
                        }
                    })
                    ->requiresConfirmation()
                    ->color('success')
                    ->hidden(fn() => !request()->user()->hasAnyRole('marker', 'moderator')), // Show only for markers and moderators
            ])
            ->bulkActions([
                ReactivateMarks::make('Reactivate Score Entries')
                    ->hidden(fn() => !request()->user()->hasAnyRole('iec', 'admin')), // Show only for iec and admin roles
                ExportBulkAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                ->hidden(fn() => !request()->user()->hasAnyRole('iec', 'admin', 'glc', 'gsl')),
            ])
            ->modifyQueryUsing(function (Builder $query) {
                return $this->getFilteredRecords(request()->user(), $query); // Return the query builder
            });
    }

    public function markerEdits(): array
    {
        $user = request()->user();
        return $user->hasAnyRole('marker') ? [
            Tables\Columns\TextInputColumn::make('marker_que_one')
                ->extraAttributes(['class' => 'small-input'])
                ->label('Marker Q1')
                ->disabled(fn($record) => !$this->canEditMarker($record, 1))
                ->rules(['required', 'numeric', 'max:25']),
            Tables\Columns\TextInputColumn::make('marker_que_two')
                ->extraAttributes(['class' => 'small-input'])
                ->label('Marker Q2')
                ->disabled(fn($record) => !$this->canEditMarker($record, 2))
                ->rules(['required', 'numeric', 'max:25']),
            Tables\Columns\TextColumn::make('marker_score')
                ->label("Marker Total")
                ->numeric()
                ->state(function ($record) {
                    if(!$record || (is_null($record->marker_que_one) && is_null($record->marker_que_two))) return null;
                    return ($record->marker_que_one ?? 0) + ($record->marker_que_two ?? 0);
                }),
            Tables\Columns\ToggleColumn::make('wrote_exam')
                ->state(fn($record): bool => $this->wroteExam($record))
                ->disabled()
                ->label('Wrote Exam'),
        ] : [];
    }

    public function adminDisplayScores(): array
    {
        $user = request()->user();
        return $user->hasAnyRole('admin', 'iec', 'glc', 'gsl') ? [
            ...$this->markerDisplayScores(),
            ...$this->moderatorDisplayScores(),
        ] : [];
    }

    public function markerDisplayScores(): array
    {
        return [

            Tables\Columns\TextColumn::make('marker.full_name')
                ->label('Marker')
                ->toggleable(isToggledHiddenByDefault: true)
                ->searchable(),
            Tables\Columns\TextColumn::make('marker_que_one')
                ->toggleable(isToggledHiddenByDefault: true)
                ->label('Marker Q1'),
            Tables\Columns\TextColumn::make('marker_que_two')
                ->toggleable(isToggledHiddenByDefault: true)
                ->label('Marker Q2'),
            Tables\Columns\TextColumn::make('marker_score')
                ->label('Total Marker Score'),
        ];
    }

    public function moderatorDisplayScores(): array
    {
        return [
            Tables\Columns\TextColumn::make('moderator.full_name')
                ->label('Moderator')
                ->toggleable(isToggledHiddenByDefault: true)
                ->searchable(),
            Tables\Columns\TextColumn::make('moderator_que_one')
                ->toggleable(isToggledHiddenByDefault: true)
                ->label('Moderator Q1'),
            Tables\Columns\TextColumn::make('moderator_que_two')
                ->toggleable(isToggledHiddenByDefault: true)
                ->label('Moderator Q2'),
            Tables\Columns\TextColumn::make('moderator_score')
                ->label('Total Moderator Score'),
        ];
    }

    public function moderatorEdits(): array
    {
        $user = request()->user();

        return $user->hasAnyRole('moderator') ? [
            Tables\Columns\TextInputColumn::make('marker_que_one')
                ->extraAttributes(['class' => 'small-input'])
                ->label('Marker Q1')
                ->disabled(), // Disable if confirmed
            Tables\Columns\TextInputColumn::make('marker_que_two')
                ->extraAttributes(['class' => 'small-input'])
                ->label('Marker Q2')
                ->disabled(), // Disable if confirmed
            Tables\Columns\TextColumn::make('marker_score')
                ->label("Marker Total")
                ->numeric()
                ->state(function ($record) {
                    if(!$record || (is_null($record->marker_que_one) && is_null($record->marker_que_two))) return null;
                    return ($record->marker_que_one ?? 0) + ($record->marker_que_two ?? 0);
                })
                ->disabled(), // Disable if confirmed
            Tables\Columns\TextInputColumn::make('moderator_que_one')
                ->extraAttributes(['class' => 'small-input'])
                ->label('Moderator Q1')
                ->disabled(fn($record) => !$this->canEditModerator($record, 1))
                ->rules(['required', 'numeric', 'max:25']),
            Tables\Columns\TextInputColumn::make('moderator_que_two')
                ->extraAttributes(['class' => 'small-input'])
                ->label('Moderator Q2')
                ->disabled(fn($record) => !$this->canEditModerator($record, 2))
                ->rules(['required', 'numeric', 'max:25']),
            Tables\Columns\TextColumn::make('moderator_score')
                ->label("Moderator Total")
                ->state(function ($record) {
                    if(!$record || (is_null($record->moderator_que_one) && is_null($record->moderator_que_two))) return null;
                    return ($record->moderator_que_one ?? 0) + ($record->moderator_que_two ?? 0);
                }),
            Tables\Columns\ToggleColumn::make('wrote_exam')
                ->state(fn($record): bool => $this->wroteExam($record))
                ->disabled()
                ->label('Wrote Exam'),
        ] : [];
    }

    private function isMarkerScoreWithinThreshold($score)
    {
        return true;
        $thresholdsSetting = Setting::where('slug', 'moderator_score_thresholds')->first();
        $thresholdsValue = $thresholdsSetting ? $thresholdsSetting->value : '';
        $thresholds = array_map('trim', explode(',', $thresholdsValue));
        if(!$thresholds) return false;
        $thresholds = array_map(function($range) {
            return array_map('intval', explode('-', $range));
        }, $thresholds);

        foreach ($thresholds as $range) {
            if ($score >= $range[0] && $score <= $range[1]) {
                return true;
            }
        }
        return false;
    }

    public function adminResultsFilter()
    {
        return request()->user()->hasAnyRole('admin', 'iec') ? [
            Tables\Filters\SelectFilter::make('section')
                ->options([
                    'A' => 'Section A',
                    'B' => 'Section B',
                ]),
            Tables\Filters\SelectFilter::make('marker_id')
                ->label('Markers')
                ->relationship('marker', 'full_name'),
            Tables\Filters\SelectFilter::make('moderator_id')
                ->label('Moderators')
                ->relationship('moderator', 'full_name'),
        ] : [];
    }

    public function isReadOnly(): bool
    {
        return false;
    }

    public static function canViewForRecord($ownerRecord, string $pageClass): bool
    {
        return $ownerRecord->transaction->name == 'entrance';
    }

    private function wroteExam($record): bool
    {
        // Check if the related submission exists and if the student wrote the exam
        $submission = $record->submission; // Assuming $record is an instance of Script
        return $submission && $submission->wrote_exam;
    }

    private function canEditMarker($record, $questionNumber): bool
    {
        $hasModeratorEdits = false;
        if($questionNumber == 1) {
            $hasModeratorEdits = !empty($record->moderator_que_one);
        } elseif($questionNumber == 2) {
            $hasModeratorEdits = !empty($record->moderator_que_two);
        }
        return $record->reactivate_marker || (!$hasModeratorEdits
          && $record?->result?->wrote_exam
          && !$record->marker_score_confirmed);
    }

    private function canEditModerator($record): bool
    {
        return $record->reactivate_moderator || ($this->isMarkerScoreWithinThreshold($record->marker_score)
            && $record->marker_score_confirmed
            && !$record->moderator_score_confirmed
            && $record?->result?->wrote_exam);
    }

    // Add methods to handle score confirmation
    private function confirmMarkerScore($record)
    {
        // Check if marker has entered scores for both questions
        if(!is_null($record->marker_que_one) && !is_null($record->marker_que_two)) {
            $record->marker_score_confirmed = true;
            $record->reactivate_marker = false; // Reset the reactivation flag
            $record->save();
        }
    }

    private function confirmModeratorScore($record)
    {
        // Check if moderator has entered scores for both questions
        if (!is_null($record->moderator_que_one) && !is_null($record->moderator_que_two)) {
            $record->moderator_score_confirmed = true;
            $record->reactivate_moderator = false; // Reset the reactivation flag
            $record->save();
        }
    }

    protected function getFilteredRecords($user, $query = null)
    {
        $query = $query ?? $this->getOwnerRecord()->scripts(); // Start with the related scripts query

        $filteredRecords = $query->where('application_id', $this->getOwnerRecord()->id);

        if ($user->hasAnyRole('marker')) {
            $filteredRecords->where('marker_id', $user->id);
        } elseif ($user->hasAnyRole('moderator')) {
            $filteredRecords->where('moderator_id', $user->id);
        }

        return $filteredRecords; // Return the query builder instead of executing it
    }
}
