<?php

namespace App\Filament\Resources\EntranceResource\RelationManagers;

use App\Filament\Actions\TableActions\NotifyPublishedResults;
use App\Filament\Resources\ResourceUtilities\ResourceUtilities;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;
use Filament\Tables;

class ResultsRelationManager extends RelationManager
{
    protected static string $relationship = 'submissions';
    protected static ?string $title = "Exams Results";
    protected static ?string $modelLabel = "Results";

    public function table(Table $table): Table
    {
        return ResourceUtilities::resultsTable($table, $this->getOwnerRecord()->candidates(), [
            ...$this->markerEdits(),
            ...$this->moderatorEdits(),
            ...$this->adminDisplayScores(),
            ...$this->iecDisplayScores()
        ], [
            ...$this->adminResultsFilter($this->getOwnerRecord()->candidates())
        ], [], 'glc')->headerActions([
            // Tables\Actions\CreateAction::make(),
            NotifyPublishedResults::make('Notify')
                ->hidden(!request()->user()->hasAnyRole('glc'))
                ->arguments(['application' => $this->getOwnerRecord(), 'type' => 'entrance']),
        ]);
    }

    public function adminResultsFilter($query)
    {
        return request()->user()->hasAnyRole('admin', 'iec') ? [
            Tables\Filters\SelectFilter::make('wrote_exam')
                ->options([
                    // 'all' => 'All',
                    1 => 'Present',
                    0 => 'Absent',
                ])
                ->default('all'),
                // ->multiple(),
            Tables\Filters\SelectFilter::make('marker_id')
                ->label('Markers')
                ->options(function () use ($query) {
                    return $query->with('marker')->get()->pluck('marker.full_name', 'marker.id')->filter(function ($value, $key) {
                        return $value != "";
                    })->toArray();
                }),
            Tables\Filters\SelectFilter::make('moderator_id')
                ->label('Moderators')
                ->options(function () use ($query) {
                    return $query->with('moderator')->get()->pluck('moderator.full_name', 'moderator.id')->filter(function ($value, $key) {
                        return $value != "";
                    })->toArray();
                }),
        ] : [];
    }

    public function markerEdits(): array
    {
        $user = request()->user();
        return $user->hasAnyRole('marker') ? [
            Tables\Columns\TextInputColumn::make('script_a_marker_que_one')
                ->extraAttributes(['class' => 'small-input'])
                ->label('Script A Q1')
                ->disabled(fn($record) => !empty($record->moderator_score))
                ->rules(['required', 'numeric', 'max:50']),
            Tables\Columns\TextInputColumn::make('script_a_marker_que_two')
                ->extraAttributes(['class' => 'small-input'])
                ->label('Script A Q2')
                ->disabled(fn($record) => !empty($record->moderator_score))
                ->rules(['required', 'numeric', 'max:50']),
            Tables\Columns\TextColumn::make('script_a_marker_score')
                ->label("Script A Total"),
            Tables\Columns\TextInputColumn::make('script_b_marker_que_one')
                ->extraAttributes(['class' => 'small-input'])
                ->label('Script B Q1')
                ->disabled(fn($record) => !empty($record->moderator_score))
                ->rules(['required', 'numeric', 'max:50']),
            Tables\Columns\TextInputColumn::make('script_b_marker_que_two')
                ->extraAttributes(['class' => 'small-input'])
                ->label('Script B Q2')
                ->disabled(fn($record) => !empty($record->moderator_score))
                ->rules(['required', 'numeric', 'max:50']),
            Tables\Columns\TextColumn::make('script_b_marker_score')
                ->label("Script B Total"),
        ] : [];
    }

    public function iecDisplayScores(): array
    {
        return request()->user()->hasAnyRole('iec', 'glc', 'gsl') ? [
            Tables\Columns\TextColumn::make('script_a_marker.full_name')
                ->label('Script A Marker')
                ->toggleable(isToggledHiddenByDefault: true)
                ->searchable(),
            Tables\Columns\TextColumn::make('script_a_marker_que_one')
                ->label('Script A Q1'),
            Tables\Columns\TextColumn::make('script_a_marker_que_two')
                ->label('Script A Q2'),
            Tables\Columns\TextColumn::make('script_a_marker_score')
                ->label('Script A Total'),
            Tables\Columns\TextColumn::make('script_b_marker.full_name')
                ->label('Script B Marker')
                ->toggleable(isToggledHiddenByDefault: true)
                ->searchable(),
            Tables\Columns\TextColumn::make('script_b_marker_que_one')
                ->label('Script B Q1'),
            Tables\Columns\TextColumn::make('script_b_marker_que_two')
                ->label('Script B Q2'),
            Tables\Columns\TextColumn::make('script_b_marker_score')
                ->label('Script B Total'),
            // ... existing moderator columns ...
        ] : [];
    }

    public function adminDisplayScores(): array
    {
        return request()->user()->hasAnyRole('admin') ? [
            Tables\Columns\TextColumn::make('moderator_que_one')
                ->label('QUE 1'),
            Tables\Columns\TextColumn::make('moderator_que_two')
                ->label('QUE 2'),
            Tables\Columns\TextColumn::make('moderator_score')
                ->label("Total Marks"),
            Tables\Columns\TextColumn::make('moderator_remarks')
                ->label('Remarks'),
        ] : [];
    }

    public function moderatorEdits(): array
    {
        $user = request()->user();
        return $user->hasAnyRole('moderator') ? [
            Tables\Columns\TextColumn::make('marker_que_one')
                ->label("Marker Q1"),
            Tables\Columns\TextColumn::make('marker_que_two')
                ->label("Marker Q2"),
            Tables\Columns\TextColumn::make('marker_score')
                ->label("Total"),
            // Tables\Columns\TextInputColumn::make('moderator_que_one')
            //     ->extraAttributes(['class' => 'small-input'])
            //     ->label('Moderator Q1')
            //     ->disabled(fn($record) => empty($record->marker_score))
            //     ->rules(['required', 'numeric', 'max:50']),
            // Tables\Columns\TextInputColumn::make('moderator_que_two')
            //     ->extraAttributes(['class' => 'small-input'])
            //     ->label('Moderator Q2')
            //     ->disabled(fn($record) => empty($record->marker_score))
            //     ->rules(['required', 'numeric', 'max:50']),
            // Tables\Columns\TextColumn::make('moderator_score')
            //     ->label("Final Result")
        ] : [];
    }
}
