<?php

namespace App\Filament\Resources\EntranceResource\RelationManagers;

use App\Filament\Resources\ResourceUtilities\ResourceUtilities;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;
use App\Models\Script;

class AssignRelationManager extends RelationManager
{
    protected static string $relationship = 'scripts';
    protected static ?string $title = "Assign scripts";

    public function table(Table $table): Table
    {
        return ResourceUtilities::assignEntranceExaminersTable($table, $this->getOwnerRecord()->scripts());
    }

    public static function canViewForRecord($ownerRecord, string $pageClass): bool
    {
        return request()->user()->hasAnyRole('admin', 'iec');
    }
}
