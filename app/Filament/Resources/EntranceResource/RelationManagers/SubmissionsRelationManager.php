<?php

namespace App\Filament\Resources\EntranceResource\RelationManagers;

use App\Filament\Resources\ResourceUtilities\ResourceUtilities;
use App\FormResources\FormResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Actions\TableActions\NotifyEntranceApplicants;
use App\Infolists\Components\FileList;
use App\Models\Submission;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Collection;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use Illuminate\Support\Str;

class SubmissionsRelationManager extends RelationManager
{
    protected static string $relationship = 'submissions';

    protected static function statusInfo($id = null)
    {
        return ['Rejected', 'Approved'][$id] ?? 'Pending';
    }

    protected static function statusColors($id)
    {
        return ['danger', 'success'][$id] ?? 'gray';
    }

    public function form(Form $form): Form
    {
        return FormResource::entrance($form, [
            Forms\Components\Hidden::make('application_id')
                ->default($this->getOwnerRecord()->id)
                ->required()
        ])
            ->columns(2);
    }

    public function table(Table $table): Table
    {
        $ownerRecord = $this->getOwnerRecord();

        return $table
            ->recordTitleAttribute('full_name')
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('family_name')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('first_name')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('middle_name')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('full_name')
                    ->formatStateUsing(function (string $state): string {
                        return Str::title($state, '-');
                    }),
                Tables\Columns\TextColumn::make('application.year')
                    ->label('Year')
                    ->sortable(),
                Tables\Columns\TextColumn::make('gender'),
                Tables\Columns\TextColumn::make('venue.location')
                    ->label("Location")
                    ->sortable(),
                Tables\Columns\TextColumn::make('dob')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Date of Birth')
                    ->date(),
                Tables\Columns\TextColumn::make('nationality')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('phone')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('institution')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('status')
                    ->formatStateUsing(fn(string $state): string => self::statusInfo($state))
                    ->default('Pending')
                    ->sortable()
                    ->badge()
                    ->color(fn(string $state): string => self::statusColors($state)),
                Tables\Columns\IconColumn::make('notified')
                    ->alignCenter()
                    ->default("")
                    ->icon(fn(string $state): string => match ($state) {
                        '1', '0' => 'heroicon-o-envelope',
                        default => "heroicon-o-ellipsis-horizontal"
                    })->color(fn(string $state): string => match ($state) {
                        '1' => 'success',
                        '0' => 'danger',
                        default => ''
                    }),
                Tables\Columns\TextColumn::make('fileUrls')
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Filter::make('Approved')
                    ->query(fn(Builder $query): Builder => $query->where('status', 1)),
                Filter::make('Rejected')
                    ->query(fn(Builder $query): Builder => $query->where('status', 0)),
                Filter::make('Pending')
                    ->query(fn(Builder $query): Builder => $query->where('status', null))
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\EditAction::make('feedback')
                    ->label('feedback')
                    ->modalWidth(MaxWidth::Medium)
                    ->icon('heroicon-o-document-text')
                    ->color('danger')
                    // ->hidden(fn($record) => $record->status !== 0)
                    ->modalHeading("Add Rejection Feedback")
                    ->form([
                        Forms\Components\Textarea::make('feedback')
                    ])
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('Approve')
                    ->requiresConfirmation()
                    ->color('success')
                    ->action(fn(Collection $records) => $records->each->approve())
                    ->deselectRecordsAfterCompletion(),
                Tables\Actions\BulkAction::make('Reject')
                    ->requiresConfirmation()
                    ->color('danger')
                    ->action(fn(Collection $records) => $records->each->reject())
                    ->deselectRecordsAfterCompletion(),
                Tables\Actions\BulkAction::make('Make Pending')
                    ->requiresConfirmation()
                    ->color('gray')
                    ->action(fn(Collection $records) => $records->each->markAsPending())
                    ->deselectRecordsAfterCompletion(),
                ExportBulkAction::make(),
            ])
            ->headerActions([
                // Tables\Actions\CreateAction::make(),
                NotifyEntranceApplicants::make('Notify')
                    ->arguments(['application' => $ownerRecord]),
            ])
            ->modifyQueryUsing(fn(Builder $query) => $query->where('isSubmitted', 1))
            // ->checkIfRecordIsSelectableUsing(
            //     fn($record) => !(bool)$record->notified,
            // )
            ;
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return ResourceUtilities::EntranceInfolist($infolist);
    }

    public function isReadOnly(): bool
    {
        return false; // TODO: Change the autogenerated stub
    }

    public static function canViewForRecord($ownerRecord, string $pageClass): bool
    {
        return request()->user()->hasAnyRole('admin', 'iec');
    }
}
