<?php

namespace App\Filament\Resources\EntranceResource\RelationManagers;

use App\Filament\Actions\Students\AssignHall;
use App\Filament\Actions\TableActions\NotifyEntranceRequirements;
use App\Filament\Resources\ResourceUtilities\ResourceUtilities;
use App\FormResources\FormResource;
use App\Models\Venue;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Forms;
use Filament\Forms\Form;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class CandidateRelationManager extends RelationManager
{
    protected static string $relationship = 'candidates';

    public function form(Form $form): Form
    {
        return FormResource::entrance($form, [
            Forms\Components\Hidden::make('application_id')
                ->default($this->getOwnerRecord()->id)
                ->required()
        ])
            ->columns(2);
    }

    public function table(Table $table): Table
    {
        $ownerRecord = $this->getOwnerRecord();

        return ResourceUtilities::candidatesTable($table)->columns([
            Tables\Columns\ImageColumn::make('avatar')
                ->width(150)
                ->height(150)
                ->circular()
                ->defaultImageUrl(function () {
                    return 'data:image/svg+xml;base64,' . base64_encode('
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                            <path fill-rule="evenodd" d="M18.685 19.097A9.723 9.723 0 0021.75 12c0-5.385-4.365-9.75-9.75-9.75S2.25 6.615 2.25 12a9.723 9.723 0 003.065 7.097A9.716 9.716 0 0012 21.75a9.716 9.716 0 006.685-2.653zm-12.54-1.285A7.486 7.486 0 0112 15a7.486 7.486 0 015.855 2.812A8.224 8.224 0 0112 20.25a8.224 8.224 0 01-5.855-2.438zM15.75 9a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z" clip-rule="evenodd" />
                        </svg>
                    ');
                }),
            Tables\Columns\TextColumn::make('student_id')
                ->label('Candidate ID')
                ->copyable()
                ->copyMessage('Copied!')
                ->copyMessageDuration(2000)
                ->searchable(),
            Tables\Columns\TextColumn::make('title'),
            Tables\Columns\TextColumn::make('family_name')
                ->toggleable(isToggledHiddenByDefault: true)
                ->searchable(),
            Tables\Columns\TextColumn::make('first_name')
                ->toggleable(isToggledHiddenByDefault: true)
                ->searchable(),
            Tables\Columns\TextColumn::make('middle_name')
                ->toggleable(isToggledHiddenByDefault: true)
                ->searchable(),
            Tables\Columns\TextColumn::make('full_name')
                ->formatStateUsing(function (string $state): string {
                    return Str::title($state, '-');
                }),
            Tables\Columns\TextColumn::make('venue.name')
                ->searchable(),
            Tables\Columns\TextColumn::make('ghana_card'),
            // Tables\Columns\TextColumn::make('venue.name')
            //     ->label('Center')
            //     ->searchable(),
            Tables\Columns\TextColumn::make('student_id')
                ->label('Candidate ID')
                ->searchable(),
            Tables\Columns\TextColumn::make('gender'),
            Tables\Columns\ToggleColumn::make('wrote_exam'),
        ])
        ->bulkActions([
            ExportBulkAction::make(),
            AssignHall::make('Assign Hall')
                ->deselectRecordsAfterCompletion(),
        ])
        ->filters([
            Tables\Filters\SelectFilter::make('venue_location')
            ->label('Location')
            ->options(Venue::query()->pluck('location', 'location')->unique())
            ->query(function ($query, $data) {
                if ($data['value'] === null) {
                    return $query;
                }

                return $query->whereHas('venue', function ($venueQuery) use ($data) {
                    $venueQuery->where('location', $data['value']);
                });
            })
            ->placeholder('All Locations'),
        ])
        ->modifyQueryUsing(fn(Builder $query) => $query->where('isSubmitted', 1))
        ->headerActions([
            // Tables\Actions\CreateAction::make(),
            NotifyEntranceRequirements::make('Notify')
                ->arguments(['application' => $ownerRecord]),
        ]);
    }

    public static function canViewForRecord($ownerRecord, string $pageClass): bool
    {
        return request()->user()->hasAnyRole('admin', 'iec');
    }
}
