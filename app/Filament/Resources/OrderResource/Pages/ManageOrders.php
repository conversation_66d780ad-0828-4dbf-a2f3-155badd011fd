<?php

namespace App\Filament\Resources\OrderResource\Pages;

use App\Filament\Imports\ScholarshipImporter;
use App\Filament\Resources\OrderResource;
use App\Models\Application;
use App\Models\Order;
use App\Models\Script;
use App\Models\User;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use Filament\Support\Enums\MaxWidth;
use pxlrbt\FilamentExcel\Actions\Pages\ExportAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
// use App\Imports\ScholarshipImporter;

class ManageOrders extends ManageRecords
{
    protected static string $resource = OrderResource::class;

    protected function getHeaderActions(): array
    {
        $user = request()->user();
        return [
            Actions\CreateAction::make()
                ->modalWidth(MaxWidth::Medium)
                ->modalSubmitActionLabel('Approve')
                ->successNotificationTitle("Transaction added")
                ->icon('heroicon-o-users')
                ->createAnother(false)
                ->label('New Transaction')
                ->mutateFormDataUsing(function (array $data): array {
                    $user = User::where('student_id', $data['student_id'])->first();

                    $data['user_id'] = $user?->id;
                    $application = Application::find($data['application_id']);
                    $data['unit_price'] = $application->amount;
                    $data['level_id'] = $application->level_id;
                    $data['transaction_id'] = $application->transaction_id;
                    $data['description'] = $application->name;

                    unset($data['student_id']);
                    return $data;
                })
                ->hidden(!$user->hasAnyRole('accountant'))
                ->before(function ($data, $action) {

                    $user = User::find($data['user_id']);
                    $application = Application::find($data['application_id']);

                    $studentStatus = (object)studentStatus($user);
                    $message = null;

                    $level = $studentStatus->next_level ?? $user->start_level;

                    if ($application->level_id != $level || $studentStatus->expelled) $message = 'Student is not eligible to apply for this exam';
                    if ($user->paidApplication($application)) $message = "Student has already registered for this exam";

                    if ($message) {
                        Notification::make()
                            ->danger()
                            ->title("Transaction failed — " . $message)
                            ->send();
                        $action->cancel();
                    }
                })
                ->after(function ($record) {
                    $application = $record->application;
                    foreach ($record->application->courses as $course) {
                        $result = \App\Models\Result::firstOrCreate([
                            'student_id' => $record->user->student_id,
                            'application_id' => $application->id,
                            'level_id' => $application->level_id,
                            'course_id' => $course->id,
                        ]);
                        if ($course->sub_courses->isNotEmpty()) {
                            foreach ($course->sub_courses as $subCourse) {
                                Script::firstOrCreate([
                                    'student_id' => $record->user->student_id,
                                    'application_id' => $application->id,
                                    'level_id' => $application->level_id,
                                    'result_id' => $result->id,
                                    'course_id' => $course->id,
                                    'sub_course_id' => $subCourse->id,
                                ]);
                            }
                        } else {
                            Script::firstOrCreate([
                                'student_id' => $record->user->student_id,
                                'application_id' => $application->id,
                                'level_id' => $application->level_id,
                                'result_id' => $result->id,
                                'course_id' => $course->id,
                            ]);
                        }
                    }
                    $record->status = 1;
                    $record->save();
                }),
            Actions\CreateAction::make('createScholarship')
                ->modalWidth(MaxWidth::Medium)
                ->modalSubmitActionLabel('Create Scholarship')
                ->successNotificationTitle("Scholarship added")
                ->icon('heroicon-o-academic-cap')
                ->createAnother(false)
                ->label('New Scholarship')
                ->form([
                    TextInput::make('student_id')
                        ->label('Student ID')
                        ->required()
                        ->rules('exists:users,student_id')
                        ->maxLength(255),
                    Select::make('application_id')
                        ->label('Application')
                        ->required()
                        ->options(fn(): array => Application::where('transaction_id', '!=', 1)->pluck('name', 'id')->all())
                        ->reactive()
                        ->afterStateUpdated(function (callable $set, $state) {
                            $application = Application::find($state);
                            if ($application) {
                                $set('amount', $application->amount);
                            }
                        }),
                    TextInput::make('amount')
                        ->label('Scholarship Amount')
                        ->numeric()
                        ->required()
                        ->minValue(0)
                        ->prefix('$'),
                    Select::make('scholarship_type')
                        ->label('Scholarship Type')
                        ->options([
                            'government' => 'Government Sponsored',
                            'corporate' => 'Corporate Sponsored',
                            'foundation' => 'Foundation Sponsored',
                            'partial' => 'Partial Scholarship',
                            'full' => 'Full Scholarship',
                            'other' => 'Other Sponsorship',
                        ])
                        ->default('government')
                        ->required(),
                    Textarea::make('notes')
                        ->required()
                ])
                ->mutateFormDataUsing(function (array $data): array {
                    $user = User::where('student_id', $data['student_id'])->first();

                    $application = Application::find($data['application_id']);
                    
                    return [
                        'user_id' => $user?->id,
                        'application_id' => $data['application_id'],
                        'unit_price' => $data['amount'],
                        'level_id' => $application->level_id,
                        'transaction_id' => $application->transaction_id,
                        'description' => $application->name . ' (Scholarship)',
                        'reference' => 'scholarship:' . $data['scholarship_type'],
                        'notes' => $data['notes'],
                        'status' => 1,
                        'qty' => 1,
                    ];
                })
                ->hidden(!$user->hasAnyRole('accountant', 'iec'))
                ->after(function ($record) {
                    $application = $record->application;
                    $user = $record->user;
                    
                    foreach ($application->courses as $course) {
                        $result = \App\Models\Result::firstOrCreate([
                            'student_id' => $user->student_id,
                            'application_id' => $application->id,
                            'level_id' => $application->level_id,
                            'course_id' => $course->id,
                        ]);
                        
                        if ($course->sub_courses->isNotEmpty()) {
                            foreach ($course->sub_courses as $subCourse) {
                                Script::firstOrCreate([
                                    'student_id' => $user->student_id,
                                    'application_id' => $application->id,
                                    'level_id' => $application->level_id,
                                    'result_id' => $result->id,
                                    'course_id' => $course->id,
                                    'sub_course_id' => $subCourse->id,
                                ]);
                            }
                        } else {
                            Script::firstOrCreate([
                                'student_id' => $user->student_id,
                                'application_id' => $application->id,
                                'level_id' => $application->level_id,
                                'result_id' => $result->id,
                                'course_id' => $course->id,
                            ]);
                        }
                    }
                }),
            Actions\ImportAction::make('importScholarships')
                ->importer(ScholarshipImporter::class)
                ->label('Import Scholarships')
                ->icon('heroicon-o-academic-cap')
                ->hidden(!$user->hasAnyRole('accountant', 'iec')),
            ExportAction::make()->exports([
                ExcelExport::make()
                    ->fromTable()
                    ->except('#')
                    ->withFilename("Transactions")
            ]),
        ];
    }

}
