<?php

namespace App\Filament\Resources\OrderResource\Pages;

use App\Filament\Resources\EntranceTransactResource;
use App\Models\Order;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use pxlrbt\FilamentExcel\Actions\Pages\ExportAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;

class ManageEntranceTransactions extends ManageRecords
{
    protected static string $resource = EntranceTransactResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            ExportAction::make()->exports([
                ExcelExport::make()
                    ->fromTable()
                    ->except('#')
                    ->withFilename("EntranceTransactions")
            ]),
        ];
    }
}
