<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CourseResource\Pages;
use App\Filament\Resources\CourseResource\RelationManagers;
use App\Models\Course;
use App\Models\Level;
use Filament\Forms;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CourseResource extends Resource
{
    protected static ?string $model = Course::class;
    protected static ?string $label = "Course";
    protected static ?int $navigationSort = 1;

    // protected static bool $shouldRegisterNavigation = false;

    protected static ?string $navigationGroup = 'Examination Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('level_id')
                    ->relationship('level', 'name')
                    ->options(fn(): array => Level::pluck('name', 'id')->toArray())
                    ->selectablePlaceholder(false),
                Repeater::make('sub_courses') // Add the repeater for questions
                    ->relationship('sub_courses') // Specify the relationship
                    ->schema([
                        Forms\Components\TextInput::make('name') // Field for question text
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Hidden::make('level_id')
                            ->default(fn(callable $get) => $get('../../level_id')),
                    ])
                    ->columns(1) // Adjust the number of columns as needed
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('#')
                    ->rowIndex(),
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('level.name')
                    ->sortable(),
                Tables\Columns\TextColumn::make('sub_courses.0.name')
                    ->label('Subject 1')
                    ->sortable(),
                Tables\Columns\TextColumn::make('sub_courses.1.name')
                    ->label('Subject 2')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCourses::route('/'),
        ];
    }

    public static function getTableQuery(): Builder
    {
        return parent::getTableQuery()->with('sub_courses'); // Eager load sub_courses
    }
}
