<?php

namespace App\Filament\Exports;

use App\Models\Order;
use App\Models\RemarkResult;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class RemarkResultExporter extends Exporter
{
    protected static ?string $model = RemarkResult::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('result.student_id')->label("Student ID"),
            ExportColumn::make('result.application.name')
                ->label('Examination'),
            ExportColumn::make('result.course.name'),
            ExportColumn::make('result.marker.name'),
            ExportColumn::make('result.score')
                ->label('Original Score'),
            ExportColumn::make('score'),
            ExportColumn::make('updated_at'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your order export has completed and ' . number_format($export->successful_rows) . ' ' . str('row')->plural($export->successful_rows) . ' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to export.';
        }

        return $body;
    }
}
