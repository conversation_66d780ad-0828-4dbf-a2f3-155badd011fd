<?php

namespace App\Filament\Exports;

use App\Models\EVoucher;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class EVoucherExporter extends Exporter
{
    protected static ?string $model = EVoucher::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('voucher_code'),
            ExportColumn::make('first_name'),
            ExportColumn::make('last_name'),
            ExportColumn::make('email'),
            ExportColumn::make('phone'),
            ExportColumn::make('amount'),
            ExportColumn::make('charges'),
            ExportColumn::make('status'),
            ExportColumn::make('application_id'),
            ExportColumn::make('payment_reference'),
            ExportColumn::make('paid_at'),
            ExportColumn::make('redeemed_at'),
            ExportColumn::make('redeemed_by'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your eVoucher export has completed and ' . number_format($export->successful_rows) . ' ' . str('row')->plural($export->successful_rows) . ' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to export.';
        }

        return $body;
    }
}
