<?php

namespace App\Filament\Imports;

use App\Jobs\QueuedWelcomeJob;
use App\Models\User;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;
use Illuminate\Validation\Rule;

class UserImporter extends Importer
{
    protected static ?string $model = User::class;

    private static ?string $password;

    public static function getColumns(): array
    {
        self::$password = bcrypt(randomPassword());

        return [
            ImportColumn::make('first_name')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make('last_name')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make('email')
                ->requiredMapping()
                ->rules(['required', 'email', 'max:255', Rule::unique(User::class)]),
            ImportColumn::make('phone')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make('ghana_card')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make('venue_id')
                ->label('Center')
                ->requiredMapping()
                ->rules(['required', 'numeric']),
            ImportColumn::make('student_id')
                ->rules(['required', 'max:255']),
            // ImportColumn::make('start_level')
            //     ->requiredMapping()
            //     ->integer()
                // ->rules(['required', 'numeric', Rule::in([1, 2, 3, 4])]),
        ];
    }

    public function resolveRecord(): ?User
    {
        // return User::firstOrNew([
        //     // Update existing records, matching them by `$this->data['column_name']`
        //     'email' => $this->data['email'],
        // ]);

        return new User([
            'password' => self::$password
        ]);
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your user import has completed and ' . number_format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }

    public function saveRecord(): void
    {
        $this->record->save();
        QueuedWelcomeJob::dispatch($this->record);
    }
}
