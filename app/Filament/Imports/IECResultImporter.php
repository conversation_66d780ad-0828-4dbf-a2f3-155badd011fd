<?php

namespace App\Filament\Imports;

use App\Models\Result;
use Filament\Actions\Imports\ImportColumn;

class IECResultImporter extends ResultImporter
{
    protected static ?string $model = Result::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('student_id')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make("iec_score")
                ->requiredMapping()
                ->numeric()
                ->rules(['required', 'integer', 'max:100']),
        ];
    }
}
