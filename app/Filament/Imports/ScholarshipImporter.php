<?php

namespace App\Filament\Imports;

use App\Models\Order;
use App\Models\User;
use App\Models\Application;
use App\Models\Result;
use App\Models\Script;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;
use Filament\Forms;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\CollectionExport;

class ScholarshipImporter extends Importer
{
    protected static ?string $model = Order::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('student_id')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make('scholarship_type')
                ->label('Scholarship Type')
                ->rules(['nullable', 'max:255']),
            ImportColumn::make('amount')
                ->label('Scholarship Amount')
                ->numeric()
                ->rules(['nullable', 'numeric', 'min:0']),
            ImportColumn::make('notes')
                ->label('Scholarship Notes')
                ->rules(['nullable', 'max:255']),
        ];
    }

    public function resolveRecord(): ?Order
    {
        // Get student_id directly from the data array
        $studentId = $this->data['student_id'] ?? null;
        
        if (empty($studentId)) {
            Log::error("Missing student_id in import data");
            return null;
        }
        
        Log::info("Processing student_id: " . $studentId);
        
        // Find the user by student_id
        $user = User::where('student_id', $studentId)->first();
        
        if (!$user) {
            Log::error("User not found with student_id: " . $studentId);
            return null;
        }
        
        Log::info("Found user with student_id: " . $studentId);
        Log::info("User details: " . json_encode($user));
        
        // Check if a scholarship order already exists for this student and application
        $order = Order::where('user_id', $user->id)
            ->where('application_id', $this->options['application_id'] ?? null)
            ->where('reference', 'like', 'scholarship:%')
            ->first();
            
        if (!$order) {
            // Create a new order
            $order = new Order();
            $order->user_id = $user->id;
            $order->qty = 1;
        }
        
        $order->student_id_temp = $studentId;
        $order->scholarship_type_temp = $this->data['scholarship_type'] ?? null;
        $order->scholarship_amount_temp = $this->data['amount'] ?? null;
        $order->scholarship_notes_temp = $this->data['notes'] ?? null;
        
        return $order;
    }

    protected function beforeSave(): void
    {
        $studentId = $this->record->student_id_temp ?? null;
        $scholarshipType = $this->record->scholarship_type_temp ?? $this->options['scholarship_type'] ?? 'scholarship';
        $scholarshipAmount = $this->record->scholarship_amount_temp ?? null;
        $scholarshipNotes = $this->record->scholarship_notes_temp ?? null;
        
        Log::info('Before save scholarship data', [
            'student_id' => $studentId,
            'scholarship_type' => $scholarshipType,
            'amount' => $scholarshipAmount,
            'notes' => $scholarshipNotes
        ]);
        
        if (isset($this->options['application_id'])) {
            $this->record->application_id = $this->options['application_id'];
            
            $application = Application::find($this->options['application_id']);
            if ($application) {
                $this->record->transaction_id = $application->transaction_id;
                
                $this->record->unit_price = !empty($scholarshipAmount) 
                    ? $scholarshipAmount 
                    : $application->amount;
                
                $this->record->description = $application->name . ' (Scholarship)';
            }
            
            Log::info("BeforeSave: Setting application_id to {$this->options['application_id']} for student: " . $studentId);
        }
        
        $this->record->reference = 'scholarship:' . $scholarshipType;
        $this->record->status = 1; // Mark as paid/approved
        
        if (!empty($scholarshipNotes)) {
            $this->record->notes = $scholarshipNotes;
        }
        
        unset($this->record->student_id_temp);
        unset($this->record->scholarship_type_temp);
        unset($this->record->scholarship_amount_temp);
        unset($this->record->scholarship_notes_temp);
    }

    public function saveRecord(): void
    {
        // Get the student_id from the data array
        $studentId = $this->data['student_id'] ?? null;
        
        if (empty($studentId)) {
            Log::error("Missing student_id in saveRecord");
            return;
        }
        
        // Save the record
        $this->record->save();
        Log::info('Saved scholarship order for student: ' . $studentId);
        
        // Create results for the student if they don't exist
        if (isset($this->options['application_id'])) {
            $application = Application::find($this->options['application_id']);
            
            if ($application) {
                // Get the user by student_id
                $user = User::where('student_id', $studentId)->first();
                
                if ($user) {
                    foreach ($application->courses as $course) {
                        $result = Result::firstOrCreate([
                            'student_id' => $studentId,
                            'application_id' => $application->id,
                            'level_id' => $application->level_id,
                            'course_id' => $course->id
                        ]);
                        
                        if ($course->sub_courses->isNotEmpty()) {
                            foreach ($course->sub_courses as $subCourse) {
                                Script::firstOrCreate([
                                    'student_id' => $studentId,
                                    'name' => "{$application->name} {$subCourse->name}",
                                    'application_id' => $application->id,
                                    'level_id' => $application->level_id,
                                    'result_id' => $result->id,
                                    'course_id' => $course->id,
                                    'sub_course_id' => $subCourse->id,
                                ]);
                            }
                        } else {
                            Script::firstOrCreate([
                                'student_id' => $studentId,
                                'name' => "{$application->name} {$course->name}",
                                'application_id' => $application->id,
                                'level_id' => $application->level_id,
                                'result_id' => $result->id,
                                'course_id' => $course->id,
                            ]);
                        }
                        Log::info('Created results for sponsored student: ' . $studentId);
                    }
                }
            }
        }
    }

    public function fillRecord(): void
    {
        // Do NOT include student_id or scholarship_type
        $this->record->fill([
            'application_id' => $this->options['application_id'] ?? null,
            'unit_price' => $this->data['amount'] ?? null,
            'notes' => $this->data['notes'] ?? null,
        ]);
    }

    public static function getOptionsFormComponents(): array
    {
        return [
            Forms\Components\Select::make('application_id')
                ->label('Exam/Application')
                ->options(Application::where('transaction_id', '!=', 1)->pluck('name', 'id'))
                ->required()
                ->helperText('Select the exam/application for these sponsored students'),
            Forms\Components\Select::make('scholarship_type')
                ->label('Default Scholarship Type')
                ->options([
                    'government' => 'Government Sponsored',
                    'corporate' => 'Corporate Sponsored',
                    'foundation' => 'Foundation Sponsored',
                    'partial' => 'Partial Scholarship',
                    'full' => 'Full Scholarship',
                    'other' => 'Other Sponsorship',
                ])
                ->default('government')
                ->helperText('This will be used if no scholarship type is specified in the CSV'),
            Forms\Components\Actions::make([
                Forms\Components\Actions\Action::make('downloadExample')
                    ->label('Download Example CSV')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('gray')
                    ->action(function () {
                        // Create a collection with headers as the first row
                        $exampleData = collect([
                            [
                                'student_id' => 'student_id',
                                'scholarship_type' => 'scholarship_type',
                                'amount' => 'amount',
                                'notes' => 'notes',
                            ],
                            [
                                'student_id' => 'GSL/BL.24/10000', 
                                'scholarship_type' => 'government',
                                'amount' => '500.00',
                                'notes' => 'Full scholarship by Government',
                            ],
                            [
                                'student_id' => 'GSL/BL.24/20000', 
                                'scholarship_type' => 'corporate',
                                'amount' => '750.00',
                                'notes' => 'Sponsored by XYZ Corporation',
                            ],
                            [
                                'student_id' => 'GSL/BL.24/30000', 
                                'scholarship_type' => 'foundation',
                                'amount' => '250.00',
                                'notes' => 'ABC Foundation Scholarship - Partial',
                            ],
                        ]);
                        
                        return Excel::download(
                            new CollectionExport($exampleData), 
                            'scholarship-import-example.csv',
                            \Maatwebsite\Excel\Excel::CSV
                        );
                    }),
            ]),
        ];
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your scholarship import has completed and ' . number_format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }
}
