<?php

namespace App\Filament\Imports;

use App\Models\Result;
use Filament\Actions\Imports\ImportColumn;

class MarkerResultsImporter extends ResultImporter
{
    protected static ?string $model = Result::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('student_id')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make("marker_score")
                ->requiredMapping()
                ->numeric()
                ->rules(['required', 'integer', 'max:100']),
        ];
    }
}
