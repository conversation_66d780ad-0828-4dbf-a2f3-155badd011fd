<?php

namespace App\Filament\Imports;

use App\Models\Course;
use App\Models\Result;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;
use Filament\Forms\Components\Select;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class ResultImporter extends Importer
{
    protected static ?string $model = Result::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('student_id')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make("marker_score")
                ->requiredMapping()
                ->numeric()
                ->rules(['required', 'integer', 'max:100']),
        ];
    }

    public function resolveRecord(): ?Result
    {
        $user = $this->options['user'];
        $role = self::getRole($user);

        $query = Result::where('student_id', $this->data['student_id'])
            ->where('application_id', $this->options['application_id'])
            ->where('course_id', $this->options['course_id']);

        if ($role != 'iec') $query = $query->where($role . "_id", $user->id);

        return $query->firstOrNew();
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your result import has completed and ' . number_format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }

    public static function getOptionsFormComponents(): array
    {
        return [
            Select::make('course_id')
                ->label('Course')
                ->required()
                ->options(Course::all()->pluck('name', 'id'))
        ];
    }

    protected function beforeValidate(): void
    {
        if (is_null($this->record->id)) {
            $validator = Validator::make([], []);

            $validator->errors()->add('field_name', 'You do not have the necessary permission to update this record.');
            throw new ValidationException($validator);
        }
    }

    public static function getRole($user)
    {
        $role = NULL;

        if ($user->hasAnyRole('marker')) $role = 'marker';
        if ($user->hasAnyRole('moderator')) $role = 'moderator';
        if ($user->hasAnyRole('iec')) $role = 'iec';

        return $role;
    }
}
