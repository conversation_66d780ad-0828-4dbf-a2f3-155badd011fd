<?php

namespace App\Filament\Imports;

use App\Models\Application;
use App\Models\Result;
use App\Models\Course;
use App\Models\User;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;
use Filament\Forms;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\CollectionExport;

class CourseExemptionImporter extends Importer
{
    protected static ?string $model = Result::class;
    
    public static function getColumns(): array
    {
        return [
            ImportColumn::make('student_id')
                ->label('Student ID')
                ->requiredMapping()
                ->rules(['required', 'exists:users,student_id']),
            ImportColumn::make('exemption_reason')
                ->label('Exemption Reason (Optional)')
                ->rules(['nullable', 'string', 'max:255']),
        ];
    }

    public function resolveRecord(): ?Result
    {
        $studentId = $this->data['student_id'] ?? null;

        if (!$studentId) {
            Log::warning('[CourseExemptionImporter] Skipping row due to missing student_id.');
            $this->record = null; // Ensure record is null to skip
            return null;
        }

        $user = User::where('student_id', $studentId)->first();
        if (!$user) {
            Log::warning("[CourseExemptionImporter] Student with ID '{$studentId}' not found. Skipping row.");
            $this->record = null;
            return null;
        }

        $applicationOptionId = $this->options['application_id'] ?? null;

        $selectedCourseIds = $this->options['course_ids'] ?? [];

        if (!$applicationOptionId) {
            Log::error('[CourseExemptionImporter] Missing application_id  in options.');
            $this->record = null;
            return null;
        }
        if (empty($selectedCourseIds)) {
            Log::warning('[CourseExemptionImporter] No courses selected in options for exemption. Skipping row for student ' . $studentId);
            $this->record = null;
            return null;
        }

        $application = Application::find($applicationOptionId);
        if (!$application) {
            Log::error("[CourseExemptionImporter] Application with ID '{$applicationOptionId}' not found.");
            $this->record = null;
            return null;
        }

        $validCourseIdsForStudent = [];
        foreach ($selectedCourseIds as $id) {
            $course = Course::find($id);
            if ($course) {
                $validCourseIdsForStudent[] = $id;
            } else {
                Log::warning("[CourseExemptionImporter] Course with ID '{$id}' not found. It will be skipped for student '{$studentId}'.");
            }
        }

        if (empty($validCourseIdsForStudent)) {
            Log::warning("[CourseExemptionImporter] No valid courses found for exemption for student '{$studentId}' from the selected list. Skipping row.");
            $this->record = null;
            return null;
        }

        // The first valid course will be handled by the main Importer flow.
        // Others will be handled in beforeSave().
        $representativeCourseId = array_shift($validCourseIdsForStudent);
        $otherCourseIdsToExempt = $validCourseIdsForStudent;

        $result = Result::firstOrNew([
            'student_id' => $user->student_id, // Use the actual student_id string
            'application_id' => $application->id,
            'course_id' => $representativeCourseId,
        ]);

        // Store temporary data on the record instance
        $result->csv_exemption_reason = $this->data['exemption_reason'] ?? null;
        $result->temp_level_id = $application->level_id; // To set if it's a new record
        $result->temp_other_course_ids_for_exemption = $otherCourseIdsToExempt;

        return $result;
    }

    protected function beforeSave(): void
    {
        if (!$this->record) {
            return; // Record was skipped in resolveRecord
        }

        // 1. Process the "representative" record ($this->record)
        if (!$this->record->exists) {
            $this->record->level_id = $this->record->temp_level_id;
        }

        $this->record->is_exempted = true;
        $this->record->exemption_reason = $this->record->csv_exemption_reason
                                        ?? $this->options['default_exemption_reason']
                                        ?? 'Exempted from this course';

        // 2. Process and save exemptions for "other" courses
        $studentIdForDb = $this->record->student_id; // student_id string (e.g., GSL/BL.24/10001)
        $applicationIdForDb = $this->record->application_id;
        $levelIdForNewRecords = $this->record->temp_level_id; // Level ID from the application
        $commonExemptionReason = $this->record->exemption_reason; // Use the resolved reason from the main record

        foreach ($this->record->temp_other_course_ids_for_exemption as $otherCourseId) {
            // Course validity was already checked in resolveRecord
            $additionalResult = Result::firstOrNew([
                'student_id' => $studentIdForDb,
                'application_id' => $applicationIdForDb,
                'course_id' => $otherCourseId,
            ]);

            if (!$additionalResult->exists) {
                $additionalResult->level_id = $levelIdForNewRecords;
            }
            $additionalResult->is_exempted = true;
            $additionalResult->exemption_reason = $commonExemptionReason;

            try {
                $additionalResult->save();
            } catch (\Exception $e) {
                Log::error(
                    "[CourseExemptionImporter] Failed to save additional exemption for student '{$studentIdForDb}', ".
                    "course '{$otherCourseId}': " . $e->getMessage()
                );
                // Optionally, you could collect these errors to report them more formally.
            }
        }

        // Clean up temporary properties
        unset($this->record->csv_exemption_reason);
        unset($this->record->temp_level_id);
        unset($this->record->temp_other_course_ids_for_exemption);
    }


    public static function getOptionsFormComponents(): array
    {
        return [
            Forms\Components\Select::make('application_id')
                ->label('Application')
                ->options(\App\Models\Application::examActive()
                    ->pluck('name', 'id'))
                ->required()
                ->live()
                ->afterStateUpdated(fn (callable $set) => $set('course_ids', []))
                ->helperText('Select the application to import exemptions for'),
                
            Forms\Components\CheckboxList::make('course_ids')
                ->label('Courses to Exempt')
                ->options(function (callable $get) {
                    $applicationId = $get('application_id');
                    if (!$applicationId) {
                        return [];
                    }
                    
                    return \App\Models\Application::find($applicationId)
                        ->courses()
                        ->pluck('name', 'id');
                })
                ->required()
                ->columns(2)
                ->gridDirection('row')
                ->helperText('Select which courses to exempt students from'),
                
            Forms\Components\TextInput::make('default_exemption_reason')
                ->label('Default Exemption Reason')
                ->default('Exempted from this course')
                ->helperText('This reason will be used if not specified in the CSV file.'),
                
            Forms\Components\Actions::make([
                Forms\Components\Actions\Action::make('downloadExampleCourseExemption')
                    ->label('Download Example CSV')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('gray')
                    ->action(function () {
                        $exampleData = collect([
                            ['student_id', 'exemption_reason'],
                            ['GSL/BL.24/10001', 'Special consideration for Post Call'],
                            ['GSL/BL.24/10002', null], // Will use default reason
                        ]);
                        return Excel::download(
                            new CollectionExport($exampleData),
                            'course-exemption-import-example.csv',
                            \Maatwebsite\Excel\Excel::CSV
                        );
                    }),
            ])->columnSpanFull(),
        ];
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your course exemption import has completed and ' . number_format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' processed.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }
}