<?php

namespace App\Filament\Imports;

use App\Jobs\QueuedEligibleStudentWelcomeJob;
use App\Models\User;
use App\Models\Venue;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Forms;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\CollectionExport;
use Filament\Actions\Imports\Models\Import;

class EligibilityImporter extends Importer
{
    protected static ?string $model = User::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('student_id')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make('first_name')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make('last_name')
                ->requiredMapping()
                ->rules(['required', 'max:255']),
            ImportColumn::make('email')
                ->requiredMapping()
                ->rules(['required', 'email', 'max:255']),
            ImportColumn::make('venue_id')
                ->label('Venue')
                ->requiredMapping()
                ->castStateUsing(function (string $state): int {
                    // If it's already a numeric value, convert and return
                    if (is_numeric($state)) {
                        return (int)$state;
                    }
                    
                    // Otherwise, resolve the venue ID from the name
                    $venueName = trim($state);
                    
                    // First try to find the venue by exact location match
                    $venue = Venue::where('location', $venueName)->first();
                    
                    if ($venue) {
                        Log::info("Resolved venue name '{$venueName}' to ID: {$venue->id}");
                        return $venue->id;
                    }
                    
                    // If not found, use a manual mapping as fallback
                    $venueMapping = [
                        'Accra' => 1,
                        'Kumasi' => 22,
                    ];
                    
                    $venueId = $venueMapping[$venueName] ?? null;
                    
                    if (!$venueId) {
                        Log::warning("Unknown venue name: {$venueName}. Defaulting to Accra (ID: 1)");
                        return 1; // Default to venue ID 1
                    }
                    
                    Log::info("Manually resolved venue name '{$venueName}' to ID: {$venueId}");
                    return $venueId;
                })
                ->rules(['required']),
        ];
    }

    protected function beforeSave(): void
    {
        Log::info('Before save data:', $this->data);
        $this->record->password = bcrypt(randomPassword());
        $this->record->is_eligible = true;
        $this->record->eligibility_date = now();
        $this->record->eligibility_notes = 'Imported via CSV';
        
        // Set the start_level from the options if provided
        if (isset($this->options['start_level'])) {
            $this->record->start_level = $this->options['start_level'];
            Log::info("BeforeSave: Setting start_level to {$this->options['start_level']} for student: {$this->record->student_id}");
        } else {
            Log::info("BeforeSave: No start_level provided in options for student: {$this->record->student_id}");
        }
    }

    public function resolveRecord(): ?User
    {
        $user = User::firstOrNew([
            'student_id' => $this->data['student_id'],
        ]);
        return $user;
    }

    public function saveRecord(): void
    {
        $this->record->save();
        Log::info('Saved user: ' . $this->record->student_id);
        QueuedEligibleStudentWelcomeJob::dispatch($this->record);
        Log::info('Dispatched eligible student welcome job for user: ' . $this->record->student_id);
    }

    public static function getOptionsFormComponents(): array
    {
        return [
            Forms\Components\Select::make('start_level')
                ->label('Student Level')
                ->options(levelNames())
                ->required()
                ->helperText('This level will be applied to all imported students'),
            Forms\Components\Actions::make([
                Forms\Components\Actions\Action::make('downloadExample')
                    ->label('Download Example CSV')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('gray')
                    ->action(function () {
                        // Create a collection with headers as the first row
                        $exampleData = collect([
                            [
                                'student_id' => 'student_id',
                                'first_name' => 'first_name',
                                'last_name' => 'last_name',
                                'email' => 'email',
                                'venue_id' => 'venue', // Column name in CSV
                            ],
                            [
                                'student_id' => 'GSL/BL.24/10000', 
                                'first_name' => 'John',
                                'last_name' => 'Doe',
                                'email' => '<EMAIL>',
                                'venue_id' => 'Accra', // Example venue name
                            ],
                            [
                                'student_id' => 'GSL/BL.24/20000', 
                                'first_name' => 'Jane',
                                'last_name' => 'Smith',
                                'email' => '<EMAIL>',
                                'venue_id' => 'Kumasi', // Example venue name
                            ]
                        ]);
                        
                        return Excel::download(
                            new CollectionExport($exampleData), 
                            'eligibility-import-example.csv',
                            \Maatwebsite\Excel\Excel::CSV
                        );
                    }),
            ]),
        ];
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your student eligibility import has completed and ' . number_format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }
}
