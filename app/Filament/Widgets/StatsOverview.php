<?php

namespace App\Filament\Widgets;

use App\Models\Submission;
use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
//            Stat::make('Submissions', Submission::all()->count())
//                ->description('32% increase')
//                ->chart([7, 2, 10, 3, 15, 4, 17])
//                ->descriptionIcon('heroicon-m-arrow-trending-up')
//                ->color('success'),
//            Stat::make('Bounce rate', '21%')
//                ->description('7% increase')
//                ->descriptionIcon('heroicon-m-arrow-trending-down')
//                ->color('danger'),
//            Stat::make('Average time on page', '3:12')
//                ->description('3% increase')
//                ->descriptionIcon('heroicon-m-arrow-trending-up')
//                ->color('success'),
//            Stat::make('Average time on page', '3:12'),
        ];
    }
}
