<?php

namespace App\Filament\Widgets;

use App\Models\EVoucher;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class VoucherStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $totalVouchers = EVoucher::count();
        $paidVouchers = EVoucher::where('status', 'paid')->count();
        $redeemedVouchers = EVoucher::where('status', 'redeemed')->count();
        $pendingVouchers = EVoucher::where('status', 'pending')->count();
        
        $totalRevenue = EVoucher::whereIn('status', ['paid', 'redeemed'])->sum('amount');
        $totalCharges = EVoucher::whereIn('status', ['paid', 'redeemed'])->sum('charges');
        
        $todayVouchers = EVoucher::whereDate('created_at', today())->count();
        $todayRevenue = EVoucher::whereDate('created_at', today())
            ->whereIn('status', ['paid', 'redeemed'])
            ->sum('amount');

        return [
            Stat::make('Total Vouchers', $totalVouchers)
                ->description('All time vouchers created')
                ->descriptionIcon('heroicon-m-ticket')
                ->color('primary'),
                
            Stat::make('Paid Vouchers', $paidVouchers)
                ->description('Ready for redemption')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),
                
            Stat::make('Redeemed Vouchers', $redeemedVouchers)
                ->description('Successfully used')
                ->descriptionIcon('heroicon-m-arrow-path')
                ->color('info'),
                
            Stat::make('Pending Payment', $pendingVouchers)
                ->description('Awaiting payment')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),
                
            Stat::make('Total Revenue', 'GHS ' . number_format($totalRevenue, 2))
                ->description('From voucher sales')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('success'),
                
            Stat::make('Transaction Charges', 'GHS ' . number_format($totalCharges, 2))
                ->description('Payment processing fees')
                ->descriptionIcon('heroicon-m-credit-card')
                ->color('gray'),
                
            Stat::make('Today\'s Vouchers', $todayVouchers)
                ->description('Created today')
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('primary'),
                
            Stat::make('Today\'s Revenue', 'GHS ' . number_format($todayRevenue, 2))
                ->description('Revenue today')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('success'),
        ];
    }
}
