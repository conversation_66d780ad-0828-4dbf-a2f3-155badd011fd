<?php

use App\Models\Result;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PaymentApiController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::get('/results', function (Request $request) {
    $query = Result::with(['course'])->limit(10); // Eager load course and remarkResult relationships

    // Filter by student_id if provided
    if ($request->has('student_id')) {
        $query->where('student_id', $request->input('student_id'));
    }

    // Filter by course name if provided
    if ($request->has('course_name')) {
        $query->whereHas('course', function ($q) use ($request) {
            $q->where('name', 'like', '%' . $request->input('course_name') . '%');
        });
    }

    // Filter by remark if provided
    if ($request->has('remark')) {
        $remark = $request->input('remark');
        if ($remark === 'passed') {
            $query->where('total_score', '>=', 50); // Assuming 50 is the passing score
        } elseif ($remark === 'failed') {
            $query->where('total_score', '<', 50);
        } elseif ($remark === 'referred') {
            // Assuming referred means a specific condition, adjust as necessary
            $query->where('total_score', '<', 40); // Example condition for referred
        }
    }

    // Get the results
    $results = $query->get();

    // Check if results are empty
    if ($results->isEmpty()) {
        return response()->json(['message' => 'No results found'], 404);
    }

    // Return the view with results
    return view('result-list', ['results' => $results]);
    // return response()->json($results); // Uncomment this line if you want to return JSON instead
});

Route::post('/payment/callback', [PaymentApiController::class, 'callback'])->name('payment.callback');
Route::post('/payment/batch-callback', [PaymentApiController::class, 'batchCallback'])->name('payment.batchCallback');
Route::post('/payment/batch-callback-csv', [PaymentApiController::class, 'batchCallbackFromCsv'])->name('payment.batchCallbackFromCsv');
