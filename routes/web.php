<?php

use App\Http\Controllers\CredController;
use App\Http\Controllers\ExamController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ProfileController;
use App\Models\Cred;
use App\Repository\PaymentRepository;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return redirect()->route('login');
//    return view('welcome');
});

// Route::post('/cred/register', [CredController::class, 'register'])->name('cred.register');
// Route::post('/cred/login', [CredController::class, 'login'])->name('cred.login');
Route::get('/entrance-registration', fn() => view('entrance-registration'))->name('entrance.registration');

Route::get('/pay', fn() => view('orders.pay'))->name('pay');
Route::get('/orders/{order}/pay', [OrderController::class, 'pay'])->name('orders.pay');
Route::middleware(['auth', 'checkNoRole', 'verified'])->group(function () {
    Route::get('/receipts', [OrderController::class, 'index'])->name('orders.index');
    Route::get('/receipts/{order}', [OrderController::class, 'show'])->name('orders.show');
});

Route::middleware(['cred'])->group(function () {
    Route::get('/entrance', fn() => view('entrance'))->name('entrance.application');
    Route::get('/entrance/{submission}/edit', function (\App\Models\Submission $submission) {
        if ($submission->application->expired()) return redirect()->route('entrance.application');

        // Temporarily set isSubmitted to false to allow editing
        $submission->update(['isSubmitted' => 0]);

        return view('entrance');
    })->name('entrance.edit');
    Route::get('/entrance/preview', fn() => view('entrance-preview'))->name('entrance.preview');
    Route::post('/cred/logout', [CredController::class, 'logout'])->name('cred.logout');
});

// Admin routes for voucher management (add appropriate middleware as needed)
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/vouchers', [\App\Http\Controllers\VoucherController::class, 'index'])->name('vouchers.index');
    Route::get('/vouchers/list', [\App\Http\Controllers\VoucherController::class, 'list'])->name('vouchers.list');
    Route::get('/vouchers/export', [\App\Http\Controllers\VoucherController::class, 'export'])->name('vouchers.export');
    Route::get('/vouchers/{voucher}', [\App\Http\Controllers\VoucherController::class, 'show'])->name('vouchers.show');
    Route::post('/vouchers/validate', [\App\Http\Controllers\VoucherController::class, 'validateVoucher'])->name('vouchers.validate');
});

Route::middleware(['auth', 'checkNoRole', 'verified'])->group(function () {
    Route::get('/exam/{application}/retake', [ExamController::class, 'retake'])->name('exam.retake');
    Route::get('/exam/{application}', [ExamController::class, 'show'])->name('exam.show');
    Route::post('/exam/{application}/register', [ExamController::class, 'register'])->name('exam.register');
    Route::get('/results/{level}', [ExamController::class, 'results'])->name('exam.results');
    Route::get('/remark', [ExamController::class, 'remark'])->name('exam.remark');
    Route::get('/retally', [ExamController::class, 'retally'])->name('exam.retally');
    Route::post('/remark', [ExamController::class, 'submitRemark'])->name('exam.remark.submit');
    Route::get('/invoice', fn() => view('invoice'));
    Route::get('/receipt', fn() => view('receipt'));
    Route::get('/overview', [HomeController::class, 'index'])->name('overview');
});

Route::middleware(['auth', 'checkNoRole'])->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

Route::get('/payment/callback', [PaymentController::class, 'callback'])->name('payment.callback');

Route::get('/student/ineligible', [App\Http\Controllers\EligibilityController::class, 'ineligible'])
    ->name('student.ineligible');

Route::middleware(['auth', 'eligible'])->group(function () {
    Route::get('/exam/register', [App\Http\Controllers\ExamController::class, 'register'])->name('exam.register');
    Route::get('/exam/{application}', [App\Http\Controllers\ExamController::class, 'show'])->name('exam.show');
    Route::post('/exam/{application}/register', [App\Http\Controllers\ExamController::class, 'register'])->name('exam.register');
    Route::get('/exam/{application}/retake', [ExamController::class, 'retake'])->name('exam.retake');
});

// PC Top-up routes (one-off solution)
Route::middleware(['auth'])->group(function () {
    Route::get('/pc-topup', [App\Http\Controllers\PCTopUpController::class, 'index'])->name('pc-topup');
    Route::post('/pc-topup/process', [App\Http\Controllers\PCTopUpController::class, 'process'])->name('pc-topup.process');
});

require __DIR__ . '/auth.php';
