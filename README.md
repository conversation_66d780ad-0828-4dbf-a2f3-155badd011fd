
# IEC Examination System

A Laravel-based application for managing examination processes for the Independent Examinations Council.

## Features

- User authentication and role management
- Examination application processing
- Course and level management
- Result recording and publishing
- Remarking applications
- Payment processing

## Requirements

- PHP 8.3+
- Composer 2.x
- Node.js 18+
- npm or yarn
- Docker and Docker Compose

## Installation

### Using Docker (Recommended)

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd <project-folder>
   ```

2. Create a `.env` file:
   ```bash
   cp .env.example .env
   ```

3. Update the `.env` file with your database credentials:
   ```
   DB_CONNECTION=mysql
   DB_HOST=iec_db
   DB_PORT=3306
   DB_DATABASE=your_database_name
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

4. Start the Docker containers:
   ```bash
   docker compose up -d
   ```

5. Generate application key:
   ```bash
   docker compose exec php php artisan key:generate
   ```

6. Run migrations and seed the database:
   ```bash
   docker compose exec php php artisan migrate --seed
   ```

7. Install and build frontend assets:
   ```bash
   docker compose exec php npm install
   docker compose exec php npm run build
   ```

## Accessing the Application

- **Main Application**: [http://localhost:7000](http://localhost:7000)
- **PHPMyAdmin**: [http://localhost:8080](http://localhost:8080)
- **Database**: Port 3307 (for external connections)

## Development

### Useful Commands

- Run tests:
  ```bash
  docker compose exec php php artisan test
  ```

- Clear cache:
  ```bash
  docker compose exec php php artisan optimize:clear
  ```

- Watch for frontend changes:
  ```bash
  docker compose exec php npm run dev
  ```

## Deployment

The application is configured for deployment on Fly.io. See `fly.toml` for configuration details.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
