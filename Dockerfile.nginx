FROM nginx:stable-alpine AS base

RUN printf "\
    server {\n\
    listen 80;\n\
    index index.php index.html;\n\
    error_log  /var/log/nginx/error.log;\n\
    access_log /var/log/nginx/access.log;\n\
    root /var/www/html/public;\n\
    location ~ \.php$ {\n\
    try_files \$uri =404;\n\
    fastcgi_split_path_info ^(.+\.php)(/.+)$;\n\
    fastcgi_pass php:9000;\n\
    fastcgi_index index.php;\n\
    include fastcgi_params;\n\
    fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;\n\
    fastcgi_param PATH_INFO \$fastcgi_path_info;\n\
    }\n\
    location / {\n\
    try_files \$uri \$uri/ /index.php?\$query_string;\n\
    gzip_static on;\n\
    }\n\
    }\n" > /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]