# Dockerfile
FROM php:8.3-fpm as php

RUN usermod -u 1000 www-data

# Install necessary extensions
RUN apt-get update && apt-get install -y \
    libcurl4-openssl-dev \
    libxml2-dev \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libpng-dev \
    libzip-dev \
    libicu-dev \
    libexif-dev \
    unzip \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install \
    curl \
    gd \
    dom \
    zip \
    intl \
    exif \
    bcmath \
    mysqli \
    pdo_mysql \
    xml \
    soap \
    opcache \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Set the working directory
WORKDIR /var/www/html

# Copy the entire project first
COPY --chown=www-data . .


# Install composer
COPY --from=composer:2.8.1 /usr/bin/composer /usr/local/bin/composer

# Install dependencies
RUN composer install --no-dev --prefer-dist --optimize-autoloader

# Set proper permissions
RUN chown -R www-data:www-data /var/www/html
RUN chmod -R 775 /var/www/html/storage
RUN chmod -R 775 /var/www/html/bootstrap

# COPY ./docker/php/php.ini /usr/local/etc/php/php.ini
# COPY ./docker/php/php.fpm.conf /usr/local/etc/php-fpm.d/www.conf

# Install composer
#COPY --from=composer:2.8.1 /usr/bin/composer /usr/local/bin/composer
# copy composer.json to workdir & install dependencies
#COPY composer.json ./
#RUN composer install

RUN printf "\
    chmod -R o+w /var/www/html/storage\n\
    chown -R www-data:www-data /var/www/html/storage\n\
    cp .env /var/www/html/.env\n\
    php-fpm\n\
    " > /start.sh

RUN chmod +x "/start.sh"

ENTRYPOINT "/start.sh"

# Set the default command to run php-fpm
# CMD ["php-fpm"]
