<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Examination Retake') }}
        </h2>
    </x-slot>

    <div class="container mt-8">
        <div class="bg-white border rounded-lg max-w-4xl mx-auto p-8 md:p-20">
            <h1 class="text-center font-bold uppercase text-xl">{{ $application->name }} - Retake Application</h1>

            <p class="text-center mt-4 italic max-w-sm mx-auto">To begin, select the subjects you would like to be
                retake {{ money($transaction->amount) }} per subject.</p>

            <livewire:retake-papers :application="$application" :referrals="$referrals" :transaction="$transaction"/>

            @if(count($referralRequests))
                <div class="mt-8">
                    <p class="mb-4 font-bold">Paid requests</p>
                    @foreach($referralRequests as $referralRequest)
                        <p class="font-bold">— {{ $referralRequest->created_at->format('d m, Y') }}</p>
                        <p class="mb-4">{!! nl2br($referralRequest->description) !!}</p>
                    @endforeach
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
