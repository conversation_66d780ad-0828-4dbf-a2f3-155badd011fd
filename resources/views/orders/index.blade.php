<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Examination Remarking') }}
        </h2>
    </x-slot>

    <div class="container mt-8">
        <div class="bg-white border rounded-lg max-w-4xl mx-auto p-8 md:p-20">
            <h1 class="text-center font-bold uppercase text-xl">Receipts</h1>

            <table class="table-auto w-full border-collapse mt-4">
                <thead>
                <tr class="bg-gray-100">
                    <th class="border border-solid min-w-[30px]">#</th>
                    <th class="border border-solid py-2">Transaction</th>
                    <th class="border border-solid">Amount</th>
                    <th class="border border-solid"></th>
                </tr>
                </thead>
                <tbody>
                @forelse($user->orders as $order)
                    <tr>
                        <td class="text-center border border-solid px-1">
                            {{ $loop->iteration }}
                        </td>
                        <td class="border border-solid px-4 py-2">{{ $order->application->name }}</td>
                        <td class="border border-solid text-center">{{ numberFormat($order->amount) }}</td>
                        <td class="border border-solid text-center">
                            <a href="{{ route('orders.show', $order) }}"
                               class="rounded-md bg-gray-200 px-4 py-1 text-base hover:bg-gray-500 hover:text-white my-2 inline-block">receipt</a>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td class="text-center border border-solid py-2" colspan="10">
                            You do not have any receipts at the moment.
                        </td>
                    </tr>
                @endforelse
                </tbody>
            </table>
        </div>
    </div>
</x-app-layout>
