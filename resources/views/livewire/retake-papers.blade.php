<div>
    {{--    @forelse($referrals as $referral)--}}
    {{--        <div class="mt-8">--}}

    <form autocomplete="off" wire:submit="submitRetakes">
        @php $count = 0; @endphp
        <table class="table-auto w-full border-collapse mt-8">
            <thead>
            <tr class="bg-gray-100">
                <th class="border border-solid min-w-[30px]">#</th>
                <th class="border border-solid py-2">Course</th>
                <th class="border border-solid">Remarks</th>
            </tr>
            </thead>
            <tbody>
            @foreach($referrals as $course_id => $total_score)
                    @php $course = \App\Models\Course::find($course_id); @endphp
                    @php $applied = false; $count++ @endphp
                    <tr>
                        <td class="text-center border border-solid px-1">
                            @unless($applied)
                                <input class="w-4 h-4 bg-gray-100 border-gray-300 rounded" type="checkbox"
                                       wire:click="toggleSelection('{{ $course_id }}')" {{ in_array($course_id, $selectedCourses) ? 'checked' : '' }}>
                            @endunless
                        </td>
                        @unless($applied)
                            <td class="border border-solid px-4 py-2">{{ $course->name }}</td>
                        @else
                            <td class="border border-solid px-4 py-2"><s>{{ $course->name }}</s> (Request
                                submitted)
                            </td>
                        @endunless
                        <td class="border border-solid text-center">{{ $course->remarksText($total_score) }}</td>
                    </tr>
            @endforeach
            @unless($count)
                <tr class="text-center">
                    <td colspan="8" class="border border-solid py-4">No referrals available.</td>
                </tr>
            @endunless
            </tbody>
        </table>

        @if($count)
            <div class="text-center mt-8">
                @if($this->cost ?? false)
                    <button class="rounded-md bg-gray-800 px-4 py-2 text-white hover:bg-gray-500 mt-4 inline-block">
                        Submit
                        and Pay {{ money($this->cost)}}</button>
                    <p class="text-red-600 text-center mt-3">Additional transaction charges
                        of <b>{{ money($this->cost * charges()) }}</b> will apply</p>
                @else
                    <a href="javascript:void(0)"
                       class="rounded-md bg-gray-200 px-4 py-2 text-gray-800 mt-4 inline-block cursor-not-allowed">Select
                        Courses</a>
                @endif
            </div>
        @endif
    </form>
</div>
