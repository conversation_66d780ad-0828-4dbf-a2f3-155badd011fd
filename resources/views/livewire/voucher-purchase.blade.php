<div>
    @if(session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {{ session('error') }}
        </div>
    @endif

    <h2 class="text-center font-bold mb-6">Purchase E-voucher</h2>

    <form wire:submit="purchaseVoucher" class="w-full" autocomplete="false">
        {{ $this->form }}

        <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded">
            <h3 class="font-semibold text-blue-800 mb-3">E-voucher Purchase Information</h3>
            <div class="text-blue-700 text-sm space-y-2">
                <p>You are purchasing an E-voucher for <strong>{{ money($voucherAmount) }}</strong>.</p>
                <p>After payment, you will receive your voucher code via email and SMS</p>
                <p>Use this voucher code to pay the Application fee</p>
            </div>
        </div>

        <button type="submit"
                class="rounded-full bg-gray-800 px-6 py-2 text-white hover:bg-gray-500 mt-6 inline-block text-sm w-full">
            Purchase E-voucher ({{ money($voucherAmount) }})
        </button>

        <p class="text-gray-600 text-center mt-3 text-xs">
           Transaction charges of <b>{{ money($voucherAmount * charges()) }}</b> will apply
        </p>
    </form>
</div>
