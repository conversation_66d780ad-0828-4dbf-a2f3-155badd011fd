<div class="container">
    @if($this->record->isSubmitted && !$this->editRoute)
        <p>Application submitted</p>
        <p>You will be notified about your application status in due course.</p>
    @else
        @if($this->record->application?->expired())
            <p>Application Submission has closed.</p>
        @else

            <img width="100px" class="mx-auto mb-6 md:mb-8" src="{{ URL::asset('imgs/ieclogo.jpg') }}"
                 alt="IEC Logo">
            @unless($this->submitted)
                <div class="text-center mb-6 md:mb-8">
                    <p class="text-sm md:text-base mb-4">Please download this declaration form, fillout and upload as part of this application.</p>
                    <a href="https://iec-glc.gov.gh/wp-content/uploads/2024/07/DECLARATION-FORM-FOR-ENTRANCE-EXAM-2024.pdf"
                       download
                       target="_blank"
                       class="rounded-full border border-gray-800 px-4 md:px-6 py-2 text-gray-800 hover:bg-gray-100 inline-block text-sm">
                        Download Declaration Form
                    </a>
                </div>
                <form wire:submit="create">
                    {{ $this->form }}

                    <hr class="mt-8 md:mt-12">

                    <div class="text-center">
                        <button type="submit"
                                class="rounded-full border border-gray-800 px-6 py-2 text-gray-800 hover:bg-gray-100 mt-6 md:mt-8 text-sm w-full sm:w-auto">
                            {{ $this->editRoute ? "Update application" : "Preview before submit"}}
                        </button>
                    </div>
                </form>
            @else
                {{ $this->infolist }}
                <div class="text-center">
                    <p class="mt-6 md:mt-8 text-sm md:text-base px-4">
                        <b>Notice:</b> By clicking the Submit Application button, you confirm that all information provided is accurate and verified
                    </p>

                    <div class="mt-6 md:mt-8 flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center">
                        <button wire:click="editForm"
                                class="rounded-full border border-gray-800 px-6 py-2 text-gray-800 hover:bg-gray-100 text-sm w-full sm:w-auto">
                            Edit Application
                        </button>

                        <button wire:click="finalSubmission"
                                class="rounded-full bg-gray-800 px-6 py-2 text-white hover:bg-gray-600 text-sm w-full sm:w-auto">
                            Submit Application
                        </button>
                    </div>
                </div>
            @endunless
        @endif
    @endif
    <x-filament-actions::modals/>
</div>
