<form autocomplete="off" wire:submit="submitRemarking">
    @if (session()->has('success'))
        <div class="bg-green-500 text-white p-4 rounded mb-4">
            {{ session('success') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div class="bg-red-500 text-white p-4 rounded mb-4">
            {{ session('error') }}
        </div>
    @endif
    @if (!remarkApplicationActive())
        <div class="bg-yellow-300 text-black p-4 rounded mb-4">
            <strong>Notice:</strong> We are currently not accepting remark requests.
        </div>
    @endif
    @forelse($applications as $application_id => $results)
        <div class="mt-8">
            <p><b>{{ getApplication($application_id)->name }}</b></p>

            <table class="table-auto w-full border-collapse mt-4">
                <thead>
                <tr class="bg-gray-100">
                    <th class="border border-solid min-w-[30px]">#</th>
                    <th class="border border-solid py-2">Subject</th>
                    <!-- <th class="border border-solid">Score</th> -->
                    <th class="border border-solid">Remarks</th>
                </tr>
                </thead>
                <tbody>
                @foreach($results as $result)
                    @php $applied = $result->remarkResult()->exists() @endphp
                    @php $remarkResult = $result->remarkResult()->first() @endphp
                    @php $publishedRemarkResult = $remarkResult?->isPublished @endphp
                    <tr>
                        <td class="text-center border border-solid px-1">
                            @if(!$applied || ($applied && $remarkResult?->status === 'cancelled'))
                                @if(remarkApplicationActive())
                                    <input class="w-4 h-4 bg-gray-100 border-gray-300 rounded" type="checkbox"
                                            wire:click="toggleSelection('{{ $result->id }}', '{{ $application_id }}')" {{ in_array($result->id, $selectedResults) ? 'checked' : '' }}>
                                @else
                                    <div class="relative inline-block">
                                        <input class="w-4 h-4 bg-gray-100 border-gray-300 rounded cursor-not-allowed" type="checkbox" disabled>
                                        <span class="absolute -top-6 left-0 bg-gray-700 text-white text-xs rounded py-1 px-2 opacity-0 transition-opacity duration-300 group-hover:opacity-100 tooltip">Application is not active</span>
                                    </div>
                                @endif
                            @endif
                        </td>
                        @unless($applied && $remarkResult?->status !== 'cancelled')
                            <td class="border border-solid px-4 py-2">{{ $result->course->name }}</td>
                        @else
                            <td class="border border-solid px-4 py-2">
                                <s>{{ $result->course->name }}</s>
                                @if($publishedRemarkResult)
                                    (Request remarked)
                                @elseif($remarkResult->status === 'cancel_requested')
                                    (Request pending cancellation)
                                @else
                                    (Request submitted)
                                    <button type="button" wire:click="cancelRemarkRequest('{{ $remarkResult->id }}')" class="rounded-md bg-red-400 px-2 py-1 text-white hover:bg-red-500 mt-4 inline-block">
                                        <!-- <x-heroicon-s-x-circle class="h-6 w-6 mr-2 text-red-600 flex-shrink-0"/> Icon -->
                                        <span>Cancel request</span> <!-- Text -->
                                    </button>
                                @endif
                            </td>
                        @endunless
                        <!-- <td class="border border-solid text-center">{{ $result->score }}</td> -->
                        <td class="border border-solid text-center">{{ $publishedRemarkResult ? $result->remarksTextForScore($result->remarkResult()->first()?->score) : $result->remarksText() }}</td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
    @empty
        <p class="text-center mt-8 font-bold">No Courses available for remark.</p>
    @endforelse

    @if(count($applications))
        <div class="text-center mt-8">
            @if($this->cost)
                <button class="rounded-md bg-gray-800 px-4 py-2 text-white hover:bg-gray-500 mt-4 inline-block">Submit
                    and
                    Pay {{ money($this->cost)}}</button>
                <p class="text-red-600 text-center mt-3">Additional transaction charges
                    of <b>{{ money($this->cost * charges()) }}</b> will apply</p>
            @else
                <a href="javascript:void(0)"
                   class="rounded-md bg-gray-200 px-4 py-2 text-gray-800 mt-4 inline-block cursor-not-allowed">Select
                    Courses</a>
            @endif
        </div>
    @endif
</form>
