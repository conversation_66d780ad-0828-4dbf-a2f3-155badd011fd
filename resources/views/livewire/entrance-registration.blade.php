<div class="min-h-screen bg-gray-100 py-6 sm:py-12">
    @php
        $currentApplication = currentApplication();
    @endphp

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-sm px-4 sm:px-6 lg:px-8 py-6 sm:py-8">

        <img class="mx-auto my-2 w-20 sm:w-24 md:w-32" src="{{ URL::asset('imgs/ieclogo.jpg') }}" alt="IEC Logo">

        @unless($currentApplication)
            <h2 class="text-center font-bold text-xl mt-4">Attention</h2>
            <p class="mt-4 text-center">The IEC's Ghana School of Law Entrance Examination application portal is not open
                yet.
                Check back later.</p>

            <p class="mt-4 text-center">You can however login to view your application status.</p>

            <div class="mt-8">@livewire('voucher-login')</div>
        @else

            <div class="text-xs sm:text-sm text-center border-b border-gray-200 overflow-x-auto">
                <ul class="flex -mb-px justify-center min-w-max">
                    @foreach(['Info Section', 'Buy E-voucher', 'Pay Application Fee', 'My Application'] as $stepTitle)
                        @php
                            $stepNumber = $loop->iteration;
                            if ($stepNumber == 1) $stepNumber = 1; // Info Section
                            elseif ($stepNumber == 2) $stepNumber = 2; // Buy E-voucher
                            elseif ($stepNumber == 3) $stepNumber = 4; // Pay Application Fee
                            else $stepNumber = 3; // My Application
                        @endphp
                        <li class="{{ $loop->last ? '' : 'mr-1' }}" wire:click="selectStep({{ $stepNumber }})">
                            <button
                                class="inline-block p-2 sm:p-3 border-b-2 {{ $step == $stepNumber ? 'border-blue-600 text-blue-600' : 'border-transparent'}} rounded-t-lg hover:text-gray-600 hover:border-gray-300 whitespace-nowrap">{{ $stepTitle }}</button>
                        </li>
                    @endforeach
                </ul>
            </div>

            <div class="mt-6 md:mt-8 px-4 sm:px-0">
                <div class="{{ $step == 1 ? '' : 'hidden' }}">
                    <h2 class="text-center font-bold text-lg md:text-xl mb-4">{{ $currentApplication->name }} Details</h2>
                    <div class="mt-4 md:mt-6 p-4 md:p-6 bg-blue-50 border border-blue-200 rounded-lg">
                        <h3 class="font-semibold text-blue-800 mb-3 text-base md:text-lg">Application Process - Part 1</h3>
                        <div class="text-blue-700 text-sm md:text-base space-y-2 md:space-y-3">
                            <p><strong>Step 1:</strong> Click on the "Buy E-voucher" tab to purchase an E-voucher. A code will be sent to your email and SMS account</p>
                            <p><strong>Step 2:</strong> Check your email and SMS account for the voucher code</p>
                            <p><strong>Step 3:</strong> Use your voucher code to apply for the Entrance Examination</p>
                        </div>
                        <br/>
                        <h3 class="font-semibold text-blue-800 mb-3 text-base md:text-lg">Part 2</h3>
                        <div class="text-blue-700 text-sm md:text-base space-y-2 md:space-y-3">
                            <p><strong>Step 1:</strong> Click on the "Pay Application Fee" tab to make payment for the Examination. If successful a Serial Number and PIN will be sent to your email and SMS account. THIS PAYMENT IS NOT REFUNDABLE</p>
                            <p><strong>Step 2:</strong> Check your email and SMS account for a Serial Number and PIN, which should be used for the Application</p>
                            <p><strong>Step 3:</strong> Click on the "My Application" tab and provide your Serial Number and PIN to complete your application</p>
                        </div>
                    </div>
                    <div class="mt-4 md:mt-6 text-sm md:text-base space-y-3 md:space-y-4">
                        <p>If you already have a Serial Number and Pin, click on the <b><i>My Application</i></b> tab to login.
                           If you have a voucher code, click on <b><i>Pay Application Fee</i></b>.
                           Otherwise, start by clicking on <b><i>Buy E-voucher</i></b>.</p>
                        <p>View the <a target="_blank" class="underline font-bold text-blue-600"
                                       href="https://iec-glc.gov.gh/ee-steps">Registration Steps</a></p>
                        <p>Contact the IEC between the hours of 9AM to 6PM on Monday to Friday on <a class="underline font-bold text-blue-600" href="tel:+233531034441">+233 (0)53 103 4441</a>
                           or <a class="underline font-bold text-blue-600" href="mailto:<EMAIL>"><EMAIL></a>
                           should you have any questions or challenges using this platform.</p>
                    </div>
                </div>
                <div class="{{ $step == 2 ? '' : 'hidden' }}">
                    @livewire('voucher-purchase')
                </div>
                <div class="{{ $step == 3 ? '' : 'hidden' }}">
                    @if(request()->get('message') == 'entrance')
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                            <strong>Success!</strong> Your payment has been processed. Check your email and SMS for your Serial Number and Pin.
                        </div>
                    @endif
                    @livewire('voucher-login')
                </div>
                <div class="{{ $step == 4 ? '' : 'hidden' }}">
                    @if(request()->get('message') == 'voucher_purchased')
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                            <strong>E-voucher Purchased!</strong> Check your email and SMS for your voucher code, then enter it below to pay the application fee.
                        </div>
                    @endif
                    @livewire('voucher-redemption')
                </div>
            </div>
        @endunless
        </div>
    </div>

    <x-filament-actions::modals/>
</div>
