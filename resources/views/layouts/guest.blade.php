<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet"/>

    <!-- Scripts -->
    @filamentStyles
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans text-gray-900 antialiased">
<div class="min-h-screen bg-gray-100 text-gray-900 flex justify-center items-center">
    <div
        class="max-w-screen-lg m-0 sm:m-10 bg-white shadow sm:rounded-lg flex justify-center flex-1 overflow-hidden">
        <div class="lg:w-1/2 xl:w-5/12 p-6 sm:p-12 w-full sm:max-w-md">
            <div class="flex flex-col items-center">
                <img width="100px" class="mx-auto mb-6" src="{{ URL::asset('imgs/ieclogo.jpg') }}" alt="">
                {{ $slot }}
            </div>
        </div>
        <div class="flex-1 bg-indigo-100 text-center hidden lg:flex">
            <div
                class="w-full bg-center bg-no-repeat bg-cover bg-[url('https://res.cloudinary.com/tbra/image/upload/v1700839003/iecBg_irr6nm.jpg')]">
            </div>
        </div>
    </div>
</div>
@livewire('notifications')
@filamentScripts
</body>
</html>
