<div class="container mt-8">
    <div
        class="bg-white border rounded-lg flex max-w-4xl mx-auto grid md:grid-cols-[2fr_5fr] md:divide-x md:divide-solid py-2">
        <div class="text-center flex flex-col items-center py-8 justify-center">
            <h3 class="text-2xl font-semibold">{{ getLevel($level)->abbr }}</h3>
            @php $level_courses = getLevel($level)->courses; @endphp
            @if($application = currentApplication($level))
                @if($results)
                    @if($results['meta']->resit)
                        @if(mainExamApplicationAvailable($level))
                            @php
                                // Check if the user has already paid for this application (for resit)
                                $hasPaidForResit = $user->paidApplication($application);
                            @endphp
                            <a href="{{ route('exam.show', $application) }}"
                            class="rounded-full bg-gray-800 px-6 py-2 text-white hover:bg-gray-500 mt-4 inline-block text-sm">
                                {{ $hasPaidForResit ? 'View Registration' : 'Resit Registration' }}</a>
                        @else
                            <span class="inline-block text-sm">
                                <button class="rounded-full bg-gray-300 px-6 py-2 text-gray-500 mt-4 inline-block text-sm cursor-not-allowed"
                                        title="Registration is not yet open." disabled>
                                    Resit Registration
                                </button>
                            </span>
                        @endif
                    @endif
                    @if(!$results['meta']->resit && $results['meta']->retake)
                        <a href="{{ route('exam.retake', $application) }}"
                           class="rounded-full bg-gray-800 px-6 py-2 text-white hover:bg-gray-500 mt-4 inline-block text-sm">Retake
                            Papers</a>
                    @endif
                @else
                    @if(mainExamApplicationAvailable($level))
                        @php
                            // Check if the user has already paid for this application
                            $hasPaid = $user->paidApplication($application);
                        @endphp
                        <a href="{{ route('exam.show', $application) }}"
                           class="rounded-full bg-gray-800 px-6 py-2 text-white hover:bg-gray-500 mt-4 inline-block text-sm">
                            {{ $hasPaid ? 'View Registration' : 'Exam Registration' }}
                        </a>
                    @else
                        <span class="inline-block text-sm">
                            <button class="rounded-full bg-gray-300 px-6 py-2 text-gray-500 mt-4 inline-block text-sm cursor-not-allowed"
                                    title="Registration is not yet open." disabled>
                                Exam Registration
                            </button>
                        </span>
                    @endif
                @endif
            @endif
            <!-- @if($results)
                <a href="{{ route('exam.results', $level) }}"
                   class="rounded-full px-6 py-2 mt-4 hover:bg-gray-100 inline-block text-sm border border-gray-300">Results</a>
            @endif -->
        </div>
        @if($results)
            <div class="text-center flex flex-col items-center justify-center p-8">
                <ul class="grid md:grid-cols-[auto_auto] gap-y-2 gap-x-8 text-gray-500">
                    @foreach($results['items'] as $result)
                        @unless($result->isPublished)
                            <li class="flex text-left text-gray-400">
                                <x-heroicon-s-minus-circle class="h-6 w-6 mr-4 flex-shrink-0"/>
                                {{ $result->course->name }}
                            </li>
                        @else
                            <li class="flex text-left items-center">
                                @if($result->is_exempted)
                                    <x-heroicon-s-shield-check
                                        class="h-6 w-6 mr-4 text-orange-400 flex-shrink-0"
                                    />
                                    <span class="line-through" title="{{ $result->remarksText() }}">
                                        {{ $result->course->name }}
                                    </span>
                                @else
                                    <!-- @if($result->sanction)
                                        <x-heroicon-s-exclamation-circle class="h-6 w-6 mr-4 text-red-600 flex-shrink-0"/>
                                    @elseif($result->didPass())
                                        <x-heroicon-s-check-circle class="h-6 w-6 mr-4 text-green-600 flex-shrink-0"/>
                                    @else
                                        <x-heroicon-s-x-circle class="h-6 w-6 mr-4 text-red-600 flex-shrink-0"/>
                                    @endif -->
                                    {{ $result->course->name }}
                                @endif
                            </li>
                        @endunless
                    @endforeach
                    @foreach($level_courses as $course)
                        @unless(array_key_exists($course->id, $results['courses']))
                            <li class="flex text-left text-gray-400">
                                <x-heroicon-s-minus-circle class="h-6 w-6 mr-4 flex-shrink-0"/>
                                {{ $course->name }}
                            </li>
                        @endunless
                    @endforeach
                </ul>
                <!-- @if(!$studentStatus->has_sanctioned_results)
                    <h3 class="mt-4 text-center max-w-[60%] italic leading-snug">
                        <b>{{ $results['meta']->count }}</b> result(s) published,
                        <b>{{ $results['meta']->passed }}</b> passed and <b>{{ $results['meta']->notPassed }}</b> failed —
                        @if($results['meta']->resit)
                            <b>You have to resit the entire exam</b>
                        @else
                            You have been referred in <b>{{ $results['meta']->notPassed }}</b> paper(s).
                        @endif
                    </h3>
                @endif -->
            </div>
        @else
            <div class="text-center flex flex-col items-center justify-center p-8">
                <ul class="grid md:grid-cols-[auto_auto] gap-y-2 gap-x-8 text-gray-500">
                    @foreach($level_courses as $course)
                        <li class="flex text-left text-gray-400">
                            <x-heroicon-s-minus-circle class="h-6 w-6 mr-4 flex-shrink-0"/>
                            {{ $course->name }}
                        </li>
                    @endforeach
                </ul>
            </div>
        @endif
    </div>
</div>
