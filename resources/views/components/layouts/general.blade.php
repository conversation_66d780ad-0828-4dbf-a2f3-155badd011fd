<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet"/>

    <!-- Scripts -->
    @filamentStyles
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <style>
        .font-medium {
            font-weight: 600
        }

        /* Fix text overflow for long content like emails */
        .fi-in-text {
            word-break: break-word !important;
            overflow-wrap: break-word !important;
            max-width: 100% !important;
        }

        /* Additional text wrapping for Filament components */
        .fi-in-entry-content {
            word-break: break-word !important;
            overflow-wrap: break-word !important;
        }

        /* Print styles */
        @media print {
            .noPrint {
                display: none !important;
            }

            body {
                background: white !important;
                color: black !important;
            }

            .bg-gray-100 {
                background: white !important;
            }

            .bg-gray-700 {
                background: #f3f4f6 !important;
                color: black !important;
            }

            .text-white {
                color: black !important;
            }

            .printarea {
                box-shadow: none !important;
                border: none !important;
                margin: 0 !important;
                padding: 20px !important;
            }

            /* Ensure proper page breaks */
            .printarea {
                page-break-inside: avoid;
            }

            /* Document preview styling for print */
            .border-gray-200 {
                border-color: #e5e7eb !important;
            }

            .border-gray-300 {
                border-color: #d1d5db !important;
            }

            /* Hide view links when printing */
            a[target="_blank"] {
                display: none !important;
            }

            /* Fix text overflow for long content */
            .fi-in-text {
                word-break: break-word !important;
                overflow-wrap: break-word !important;
            }

            /* File preview styling for print */
            .bg-gray-50 {
                background: white !important;
                border: 1px solid #e5e7eb !important;
            }

            .hover\:bg-gray-100:hover {
                background: white !important;
            }
        }
    </style>
</head>
<body class="font-sans text-gray-900 antialiased bg-gray-100 min-h-screen">
{{ $slot }}

@livewire('notifications')
@filamentScripts
</body>
</html>
