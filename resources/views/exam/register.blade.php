<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Examination Registration') }}
        </h2>
    </x-slot>

    <div class="container mt-8">
        <div class="bg-white border rounded-lg max-w-4xl mx-auto p-8 md:p-20 printarea">

            <h2 class="font-semibold text-4xl text-gray-800 leading-tight text-center mb-8">
                @if($paidApplication)
                    Proof of Submission
                @endif
            </h2>
            <h1 class="text-center font-bold uppercase text-xl">{{ $application->name }}</h1>

            <div class="grid grid-cols-2 mt-12">
                <div class="">
                    <p><strong>Registration begins:</strong> {{$application->start_date->format('F d, Y')}} </p>
                    <p><strong>Registration ends:</strong> {{ $application->end_date->format('F d, Y') }} </p>
                    <p><strong>Centre:</strong> {{ $user->venue?->location }}</p>
                </div>
                <div class="text-right">
                    @php
                        $examFee = !$paidApplication && $studentStatus->has_postcall_resit ? 1500 : $application->amount;
                    @endphp
                    <p><strong>Examination fee:</strong> {{ money($examFee) }}</p>
                    <p><strong>Status:</strong> {{ $paidApplication ? '' : 'Not' }} Registered</p>
                </div>
            </div>


            <table class="table-auto w-full mx-auto lg:w-3/4 border-collapse mt-12">
                <thead>
                <tr class="bg-gray-100">
                    <th class="border border-solid min-w-[30px]">#</th>
                    <th class="border border-solid py-2 px-4 text-left">Subject</th>
                </tr>
                </thead>
                <tbody>
                @foreach($courses as $course)
                    @php
                        // Check if there's a result record for this user, application, and course
                        $resultForCourse = $user->results()
                                               ->where('application_id', $application->id)
                                               ->where('course_id', $course->id)
                                               ->orderBy('id', 'desc')
                                               ->first();

                        $isExempted = $resultForCourse && $resultForCourse->is_exempted;
                        $exemptionReason = $isExempted ? $resultForCourse->exemption_reason : null;
                    @endphp
                    <tr>
                        <td class="text-center border border-solid px-1">{{ $loop->iteration }}</td>
                        <td class="border border-solid px-4 py-2">
                            @if($isExempted)
                                <div class="flex items-center" title="{{ $exemptionReason ?: 'Exempted from this course' }}">
                                    <x-heroicon-s-shield-check class="h-5 w-5 mr-2 text-green-600 flex-shrink-0"/>
                                    <span class="line-through">{{ $course->name }}</span>
                                    @if($exemptionReason)
                                    <span class="text-xs text-gray-600 ml-2 italic">({{ \Illuminate\Support\Str::limit($exemptionReason, 40) }})</span>
                                    @else
                                    <span class="text-xs text-gray-600 ml-2 italic">(Exempted)</span>
                                    @endif
                                </div>
                            @else
                                {{ $course->name }}
                            @endif
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>


            <div class="mt-12">
                <p>Students are to familiarise themselves with the Provisions on Examinations in the Student Handbook.
                    In addition to that, Students are to note the following:</p>

                <ul class="mt-4 list-disc ml-8">
                    <li>Students must arrive at least one hour before the commencement of exams to go through
                        pre-examination Protocols, locate their seats and be seated no later than 11:30am each morning!
                    </li>
                    <li>All students MUST ensure that their ID Cards are VALID and that the ID Cards capture their
                        correct Index Numbers.
                    </li>
                    <li>Mobile phones, smart watches, books and bags are not allowed in and around the examination
                        venue.
                    </li>
                    <li>For Legal Accountancy ONLY simple calculators are allowed. Programmable and electronic
                        calculators are NOT allowed.
                    </li>
                    <li>Students must be attired in the formal manner prescribed for lectures.</li>
                    <li>Students are to note that per the 2022/2023 Academic calendar, Practical Advocacy End of Year
                        Examinations will commence before the end of year exams. The Schedule and dates for Practical
                        Advocacy for all Part II (Main / Repeat / Transitional/ Old Part II and Supplementary) students
                        will be communicated in due course.
                    </li>
                </ul>
            </div>

            {{-- TODO: extract to partial; supply btn title, display view receipt if already paid --}}
            @unless($paidApplication)
                <livewire:exam-transact :application="$application" :order="$paidApplication" :amount="$examFee" />
                {{--                <div class="text-center mt-4">--}}
                {{--                    <x-transact-btn :application="$application" :order="$paidApplication"/>--}}
                {{--                </div>--}}
            @else
                <x-print-button/>
            @endunless

        </div>
    </div>
</x-app-layout>
