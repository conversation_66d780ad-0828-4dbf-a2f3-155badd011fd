<x-layouts.general>
    @php
        $user = Auth::guard('cred')->user();
        $submission = $user->submission ?? new \App\Models\Submission();
    @endphp

    <div class="bg-gray-100 min-h-screen">
        <div class="py-4 md:py-8 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header - Mobile Responsive -->
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
                <div class="flex-1 text-center sm:text-left">
                    <h2 class="font-bold text-lg md:text-xl">IEC Entrance Examination Application</h2>
                    <p class="text-sm text-gray-600 mt-1">Note that you can make changes after submitting the form until the portal is closed on {{ currentApplication()->end_date->format('F j, Y') }}.</p>
                </div>
                <form action="{{ route('cred.logout') }}" method="POST" class="flex justify-center sm:justify-end">
                    @csrf
                    <button type="submit"
                            class="rounded-full bg-gray-800 px-4 py-2 text-white hover:bg-gray-500 text-sm min-w-[80px]">
                        Logout
                    </button>
                </form>
            </div>

            <!-- Main Content - Mobile Responsive -->
            <div class="bg-white border rounded-lg p-4 sm:p-8 md:p-12 lg:p-20 printarea">
                @if(!$submission->isSubmitted)
                    <livewire:create-submission :record="$submission"/>
                @else
                    @if(is_null($submission->status))
                        <p>Application Submitted. You will be notified about your application status in
                            due course.</p>
                        <p class="mt-4"><b>Notice:</b> Upon approval of submitted application Candidate Examination ID
                            will be provided for the applicant to write the examination.</p>
                    @else
                        @if($submission->status)
                            @if(!$submission->isPublished)
                                <img width="100px" class="mx-auto mb-8" src="{{ URL::asset('imgs/ieclogo.jpg') }}"
                                     alt="">

                                <p class="font-bold text-2xl text-center">Proof of Submission: {{ $submission->application->name }}</p>
                                <p class="mt-4 text-center"><b>Notice:</b> You are required to bring along your Ghana
                                    Card ID & Proof of Submission for identification during the examination</p>
                            @endif
                            <p class="p-2 bg-gray-700 mt-6 text-white text-center mb-2"><b>Candidate Examination
                                    ID:</b> {{ $submission->student_id }}</p>
                            <p class="my-4 font-bold">Examination Details:</p>
                            <p class="">
                                <b>Date:</b> {{ $submission->application->exam_start_date?->format('D d M, Y') }}
                                {{ $submission->application->exam_end_date ? " - " . $submission->application->exam_end_date->format('D d M, Y') : "" }}
                            </p>
                            <p class=""><b>Centre:</b> {{ $submission->venue?->location }}</p>
                            <p class=""><b>Venue:</b> {{ $submission->venue?->hall ?: "To be assigned a week to date of Examination" }}</p>


                            @if($submission->isPublished)
                                <hr class="my-8">
                                <table class="table-auto w-full border-collapse mt-4">
                                    <thead>
                                    <tr class="bg-gray-100">
                                        <th class="border border-solid min-w-[30px]">#</th>
                                        <th class="border border-solid py-2">QUE 1</th>
                                        <th class="border border-solid">QUE 2</th>
                                        <th class="border border-solid">TOTAL</th>
                                        <th class="border border-solid">REMARKS</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr class="text-center">
                                        <td class="text-center border border-solid px-1">1</td>
                                        <td class="border border-solid px-4 py-2">{{ $submission->moderator_que_one }}</td>
                                        <td class="border border-solid px-4 py-2">{{ $submission->moderator_que_two }}</td>
                                        <td class="border border-solid px-4 py-2">{{ $submission->moderator_score }}</td>
                                        <td class="border border-solid px-4 py-2">{{ $submission?->moderator_remarks }}</td>
                                    </tr>
                                    </tbody>
                                </table>

                                <p class="text-center mt-8"><b>Notice:</b> Results stated are provisional results and
                                    thus do not
                                    guarantee automatic qualification into the Ghana School of Law</p>
                            @endif
                        @else
                            <p>We regret to inform you that your application was unsuccessful.</p>

                            <p class="mt-4">{{ $submission->status === 0 ? $submission->feedback : "" }}</p>

                        @endif
                        <div class="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 mt-6 md:mt-8">
                            <x-print-button/>
                            @unless($submission->application->expired())
                                <a href="{{ route('entrance.edit', $submission) }}"
                                   class="noPrint rounded-full px-6 py-2 border border-black text-sm text-center hover:bg-gray-50 transition-colors">
                                    Edit Application
                                </a>
                            @endunless
                        </div>

                    @endif
                @endif

            </div>
        </div>
    </div>
</x-layouts.general>
