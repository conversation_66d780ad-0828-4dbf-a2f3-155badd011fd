<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Examination Results') }}
        </h2>
    </x-slot>

    <div class="container mt-8">
        <div class="bg-white border rounded-lg max-w-4xl mx-auto p-8 md:p-20">
            <h1 class="text-center font-bold uppercase text-xl">{{ $level->abbr }} — Examination Results</h1>

            <div class="mt-8 bg-gray-100 p-2 max-w-xs mx-auto text-center border rounded-md">
                <strong>Index #: </strong>
                {{ $user->student_id }}
            </div>

            @foreach($results as $i => $appResults)
                @php $application = getApplication($i); @endphp

                <div class="mt-8">
                    <p><strong>{{ $loop->iteration > 1 ? 'Supplementary' : 'Examination' }}
                            :</strong> {{ $application->name }}
                    </p>

                    <table class="table-auto w-full border-collapse mt-4">
                        <thead>
                        <tr class="bg-gray-100">
                            <th class="border border-solid min-w-[30px]">#</th>
                            <th class="border border-solid py-2">Subject</th>
                            <!-- <th class="border border-solid">Score</th> -->
                            <th class="border border-solid">Remarks</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($appResults as $result)
                            <tr>
                                <td class="text-center border border-solid px-1">{{ $loop->iteration }}</td>
                                <td class="border border-solid px-4 py-2">{{ $result->course->name }}</td>
                                @if($result->sanction)
                                    <!-- <td class="border border-solid text-center">N/A</td> -->
                                    <td class="border border-solid text-center">{{ ucfirst($result->sanction?->name) }}</td>
                                @elseif(!$result->wrote_exam)
                                    <!-- <td class="border border-solid text-center">N/A</td> -->
                                    <td class="border border-solid text-center">ABSENT</td>
                                @else
                                    <!-- @unless($result->remarkResult && $result->finalScore)
                                        <td class="border border-solid text-center">{{ $result->score }}</td>
                                    @else
                                        <td class="border border-solid text-center">
                                            <s>{{ $result->score }}</s> {{ $result->finalScore }}</td>
                                    @endunless -->
                                    <td class="border border-solid text-center">{{ $result->remarksText() }}</td>
                                @endif
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            @endforeach

        </div>
    </div>
</x-app-layout>
