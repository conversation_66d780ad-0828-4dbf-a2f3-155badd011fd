<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Professional Course Fee Top-up') }}
        </h2>
    </x-slot>

    <div class="container mt-8">
        <div class="bg-white border rounded-lg max-w-4xl mx-auto p-8 md:p-20">
            <h1 class="text-center font-bold uppercase text-xl mb-8">Complete Your Registration Payment</h1>

            @if (session('status'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('status') }}
                </div>
            @endif

            @if (session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {{ session('error') }}
                </div>
            @endif

            <div class="bg-blue-50 border-l-4 border-blue-500 text-blue-700 p-4 mb-6">
                <p>Due to a fee adjustment, you need to make an additional payment to complete your Professional Course registration.</p>
            </div>
            
            <div class="grid grid-cols-2 mt-8">
                <div>
                    <p><strong>Top-up Amount:</strong></p>
                    <p><strong>Transaction Charges:</strong></p>
                    <p><strong>Total to Pay:</strong></p>
                </div>
                <div class="text-right">
                    <p>{{ money($amount) }}</p>
                    <p>{{ money($charges) }}</p>
                    <p>{{ money($total) }}</p>
                </div>
            </div>
            
            <div class="text-center mt-12">
                <form method="POST" action="{{ route('pc-topup.process') }}">
                    @csrf
                    <button type="submit" class="rounded-md bg-gray-800 px-6 py-2 text-white hover:bg-gray-500 inline-block">
                        Proceed to Payment
                    </button>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
