<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Examination Remarking') }}
        </h2>
    </x-slot>

    <div class="container mt-8">
        <div class="bg-white border rounded-lg max-w-4xl mx-auto p-8 md:p-20">
            <h1 class="text-center font-bold uppercase text-xl">Remarking Request</h1>

            <p class="text-center mt-4 italic max-w-sm mx-auto">To begin, select the subjects you would like to be
                remarked @ {{ money($remarkApplication->amount) }} per subject.</p>
            <livewire:remark-list :applications="$applications" :remarkApplication="$remarkApplication"/>
        </div>
    </div>
</x-app-layout>
