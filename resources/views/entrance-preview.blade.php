<x-layouts.general>
    @php
        $user = Auth::guard('cred')->user();
        $submission = $user->submission ?? new \App\Models\Submission();
    @endphp

    <div class="bg-gray-100 min-h-screen">
        <div class="py-4 md:py-8 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header - Mobile Responsive -->
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
                <div class="flex-1 text-center sm:text-left">
                    <h2 class="font-bold text-lg md:text-xl">IEC Entrance Examination Application</h2>
                    <p class="text-sm text-gray-600 mt-1">Note that you can make changes after submitting the form until the portal is closed on {{ $submission->application->end_date->format('F j, Y') }} .</p>
                </div>
                <form action="{{ route('cred.logout') }}" method="POST" class="flex justify-center sm:justify-end">
                    @csrf
                    <button type="submit"
                            class="rounded-full bg-gray-800 px-4 py-2 text-white hover:bg-gray-500 text-sm min-w-[80px]">
                        Logout
                    </button>
                </form>
            </div>

            <!-- Main Content - Mobile Responsive -->
            <div class="bg-white border rounded-lg p-4 sm:p-8 md:p-12 lg:p-20 printarea">
                @unless($submission->isSubmitted)
                    <livewire:create-submission
                        :record="$submission" :submitted="true"/>
                @else
                    @if(is_null($submission->status))
                        <div class="text-center space-y-4">
                            <p class="text-base md:text-lg">Application Submitted. You will be notified about your application status in due course.</p>
                            <p class="text-sm md:text-base"><b>Notice:</b> Upon approval of submitted application Candidate Examination ID will be provided for the applicant to write the examination.</p>
                        </div>
                    @else
                        @if($submission->status)
                            @if(!$submission->isPublished)
                                <img class="mx-auto mb-6 md:mb-8 w-20 sm:w-24 md:w-32" src="{{ URL::asset('imgs/ieclogo.jpg') }}"
                                     alt="IEC Logo">

                                <p class="font-bold text-xl md:text-2xl text-center">Proof of Submission for Entrance
                                    Examination</p>
                                <p class="mt-4 text-center text-sm md:text-base"><b>Notice:</b> You are required to bring along your Ghana
                                    Card ID for identification during the examination</p>
                            @endif
                            <div class="p-3 md:p-4 bg-gray-700 mt-6 text-white text-center mb-4 print:text-gray-900 print:bg-gray-100 rounded">
                                <p class="text-sm md:text-base">
                                    <b>Candidate Examination ID:</b> {{ $submission->student_id }}
                                </p>
                            </div>

                            <div class="my-4 md:my-6">
                                <p class="font-bold text-base md:text-lg mb-3">Examination Details:</p>
                                <div class="space-y-2 text-sm md:text-base">
                                    <p>
                                        <b>Date:</b> {{ $submission->application->exam_start_date?->format('D d M, Y') }}
                                        {{ $submission->application->exam_end_date ? " - " . $submission->application->exam_end_date->format('D d M, Y') : "" }}
                                    </p>
                                    <p><b>Venue:</b> {{ $submission->venue->name }}</p>
                                </div>
                            </div>


                            @if($submission->isPublished)
                                <hr class="my-6 md:my-8">
                                <div class="overflow-x-auto">
                                    <table class="table-auto w-full border-collapse mt-4 text-sm md:text-base">
                                        <thead>
                                        <tr class="bg-gray-100">
                                            <th class="border border-solid min-w-[30px] p-2">#</th>
                                            <th class="border border-solid py-2 px-2">QUE 1</th>
                                            <th class="border border-solid py-2 px-2">QUE 2</th>
                                            <th class="border border-solid py-2 px-2">TOTAL</th>
                                            <th class="border border-solid py-2 px-2">REMARKS</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr class="text-center">
                                            <td class="text-center border border-solid px-2 py-2">1</td>
                                            <td class="border border-solid px-2 py-2">{{ $submission->moderator_que_one }}</td>
                                            <td class="border border-solid px-2 py-2">{{ $submission->moderator_que_two }}</td>
                                            <td class="border border-solid px-2 py-2">{{ $submission->moderator_score }}</td>
                                            <td class="border border-solid px-2 py-2">{{ $submission?->moderator_remarks }}</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            @endif
                        @else
                            <div class="text-center">
                                <p class="text-base md:text-lg text-red-600">We regret to inform you that your application was unsuccessful.</p>
                            </div>
                        @endif
                        <div class="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 mt-6 md:mt-8">
                            <x-print-button/>
                            @unless($submission->application->expired())
                                <a href="{{ route('entrance.edit', $submission) }}"
                                   class="noPrint rounded-full px-6 py-2 border border-black text-sm text-center hover:bg-gray-50 transition-colors">
                                    Edit Application
                                </a>
                            @endunless
                        </div>
                    @endif
                @endunless

            </div>
        </div>
    </div>
</x-layouts.general>
