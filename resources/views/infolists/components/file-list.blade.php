<x-dynamic-component :component="$getEntryWrapperView()" :entry="$entry">
    @php
        $collections = [];
        $files = $getState();
    @endphp
    @if($files && $files->count() > 0)
        @php $collections = $files->groupBy('collection_name'); @endphp
    @endif
    <div class="text-sm">
        @forelse($collections as $collection => $files)
            <div class="mb-6">
                <p class="font-semibold text-gray-700 capitalize mb-3 {{ $loop->index > 0 ? 'mt-6' : '' }}">{{ $collection }}</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @foreach($files as $file)
                        @php
                            $isImage = in_array(strtolower(pathinfo($file->name, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif', 'webp']);
                            $isPdf = strtolower(pathinfo($file->name, PATHINFO_EXTENSION)) === 'pdf';
                            $fileUrl = $file->getFullUrl();
                        @endphp
                        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow print:break-inside-avoid bg-white">
                            @if($isImage)
                                <!-- Image Preview -->
                                <div class="mb-3 relative">
                                    <img src="{{ $fileUrl }}"
                                         alt="{{ $file->name }}"
                                         class="w-full h-40 object-cover rounded border cursor-pointer hover:scale-105 transition-transform duration-200"
                                         onclick="openImageModal('{{ $fileUrl }}', '{{ $file->name }}')"
                                         tabindex="0"
                                         onkeydown="if(event.key==='Enter'||event.key===' ') openImageModal('{{ $fileUrl }}', '{{ $file->name }}')"
                                         role="button"
                                         aria-label="Click to view {{ $file->name }} in full size"
                                         loading="lazy"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                                         style="display: block;">
                                    <!-- Error fallback -->
                                    <div class="w-full h-40 bg-gray-100 rounded border flex items-center justify-center text-gray-500" style="display: none;">
                                        <div class="text-center">
                                            <svg class="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                            </svg>
                                            <p class="text-xs">Image failed to load</p>
                                        </div>
                                    </div>
                                </div>
                            @elseif($isPdf)
                                <!-- PDF Preview -->
                                <div class="mb-3 flex items-center justify-center h-40 bg-red-50 rounded border">
                                    <div class="text-center">
                                        <svg class="w-12 h-12 text-red-500 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                        </svg>
                                        <p class="text-xs text-red-600 font-medium">PDF</p>
                                    </div>
                                </div>
                            @else
                                <!-- Other File Types -->
                                <div class="mb-3 flex items-center justify-center h-40 bg-gray-50 rounded border">
                                    <div class="text-center">
                                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <p class="text-xs text-gray-500 font-medium uppercase">{{ pathinfo($file->name, PATHINFO_EXTENSION) }}</p>
                                    </div>
                                </div>
                            @endif

                            <!-- File Info -->
                            <div class="text-center">
                                <p class="text-xs text-gray-600 mb-2 truncate" title="{{ $file->name }}">{{ $file->name }}</p>
                                <a href="{{ $fileUrl }}"
                                   target="_blank"
                                   class="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded-full hover:bg-blue-100 transition-colors">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    {{ $isImage ? 'View' : 'Download' }}
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @empty
            <div class="text-center py-8">
                <svg class="w-12 h-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                </svg>
                <p class="text-gray-500 italic">No files uploaded</p>
            </div>
        @endforelse
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 z-[9999] hidden items-center justify-center p-4 print:hidden" onclick="closeImageModal(event)">
        <div class="relative max-w-4xl max-h-full" onclick="event.stopPropagation()">
            <button onclick="closeImageModal()" class="absolute -top-10 right-0 text-white hover:text-gray-300 z-10 bg-black bg-opacity-50 rounded-full p-2">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            <img id="modalImage" src="" alt="" class="max-w-full max-h-[80vh] object-contain rounded shadow-2xl">
            <p id="modalImageName" class="text-white text-center mt-4 text-sm bg-black bg-opacity-50 rounded px-4 py-2"></p>
        </div>
    </div>

    <script>
        function openImageModal(src, name) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalImageName = document.getElementById('modalImageName');

            if (modal && modalImage && modalImageName) {
                modalImage.src = src;
                modalImageName.textContent = name;
                modal.classList.remove('hidden');
                modal.classList.add('flex');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeImageModal(event) {
            if (event) {
                event.stopPropagation();
            }
            const modal = document.getElementById('imageModal');
            if (modal) {
                modal.classList.add('hidden');
                modal.classList.remove('flex');
                document.body.style.overflow = 'auto';
            }
        }

        // Close modal on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageModal();
            }
        });
    </script>
</x-dynamic-component>
