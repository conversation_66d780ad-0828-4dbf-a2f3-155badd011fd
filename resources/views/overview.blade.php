<x-app-layout>
    {{--    <x-slot name="header">--}}
    {{--        <h2 class="font-semibold text-xl text-gray-800 leading-tight">--}}
    {{--            {{ __('Overview') }}--}}
    {{--        </h2>--}}
    {{--    </x-slot>--}}

    <div class="mt-12 container text-center text-xl">
        <h2>Welcome, <strong>{{ $user->name }}</strong> to the Independent Examinations Committee Portal</h2>
        <!-- @if(!$studentStatus->expelled && !$studentStatus->has_sanctioned_results && !$user->is_banned)
            <a href="{{ route('exam.remark') }}"
            class="rounded-md bg-gray-800 px-4 py-2.5 text-base text-white hover:bg-gray-500 mt-4 inline-block font-semibold">Request
                Remarking</a>
            <a href="{{ route('exam.retally') }}"
            class="rounded-md bg-blue-800 px-4 py-2.5 text-base text-white hover:bg-blue-500 mt-4 inline-block font-semibold mx-2">Request
                Retallying</a>
        @endif -->
    </div>

    @if($user->is_banned)
        <p class="text-red-500 text-center mt-16 text-xl font-semibold">You have been banned until {{ \Carbon\Carbon::parse($user->ban_release_date)?->format('F j, Y') }}. You can apply to rewrite after ban period.</p>
    @else
        <!-- <p class="text-red-500 text-center mt-16 text-xl font-semibold">We are currently performing scheduled maintenance. Please check back later.</p> -->
        <x-overview-card :results="$studentStatus->start_results" :level="$studentStatus->start_level" :studentStatus="$studentStatus" :user="$user"/>
        @if($studentStatus->next_level && !$studentStatus->expelled && !$studentStatus->has_sanctioned_results)
            <x-overview-card :results="$studentStatus->next_results" :level="$studentStatus->next_level" :studentStatus="$studentStatus" :user="$user" />
        @endif
    @endif


</x-app-layout>
