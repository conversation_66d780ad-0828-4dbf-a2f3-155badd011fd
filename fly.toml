# fly.toml app configuration file generated for iec on 2024-09-03T20:32:31Z
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'iec'
primary_region = 'ams'
console_command = 'php /var/www/html/artisan tinker'

[build]
  [build.args]
    NODE_VERSION = '18'
    PHP_VERSION = '8.3'

[env]
  APP_ENV = 'production'
  LOG_CHANNEL = 'stderr'
  LOG_LEVEL = 'info'
  LOG_STDERR_FORMATTER = 'Monolog\Formatter\JsonFormatter'
  SESSION_DRIVER = 'cookie'
  SESSION_SECURE_COOKIE = 'true'

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
