<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Submission>
 */
class SubmissionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $application = \App\Models\Application::where('transaction_id', 1)->inRandomOrder()->first();

        return [
            'application_id' => $application,
            'family_name' => $this->faker->lastName,
            'first_name' => $this->faker->firstName,
            'middle_name' => $this->faker->firstName,
            'title' => $this->faker->title,
            'gender' => $this->faker->randomElement(['Male', 'Female']),
            'dob' => $this->faker->date('Y-m-d'),
            'nationality' => $this->faker->country,
            'email' => $this->faker->unique()->safeEmail,
            'phone' => $this->faker->e164PhoneNumber,
            'institution' => $this->faker->company,
            'status' => $this->faker->randomElement([1, 0, null]),
            'created_at' => "$application->year" . "-01-10"
        ];
    }
}
