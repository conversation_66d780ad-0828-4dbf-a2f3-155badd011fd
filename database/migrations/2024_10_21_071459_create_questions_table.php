<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if(Schema::hasTable('questions')) {
            return;
        }
        Schema::create('questions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('script_id')->constrained()->onDelete('cascade'); // Assuming a script has many questions
            $table->integer('question_number'); // The text of the question
            $table->string('question_title')->nullable(); // The text of the question
            $table->integer('pass_mark'); // The pass mark for this question
            $table->integer('score_denominator'); // The denominator for scoring
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('questions');
    }
};
