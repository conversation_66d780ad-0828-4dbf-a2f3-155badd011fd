<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Schema::table('venues', function (Blueprint $table) {
            // $table->string('hall')->nullable();
            // $table->integer('capacity')->nullable();
        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Schema::table('venues', function (Blueprint $table) {
            // $table->dropColumn(['hall', 'capacity']);
        // });
    }
};
