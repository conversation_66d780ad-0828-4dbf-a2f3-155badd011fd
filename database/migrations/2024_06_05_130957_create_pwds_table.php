<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pwds', function (Blueprint $table) {
            $table->uuid('id');
            $table->foreignUuid('user_id')->nullable();
            $table->foreignId('application_id')->nullable();
            $table->string('pwd_type')->default('');
            $table->text('pwd_details')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pwds');
    }
};
