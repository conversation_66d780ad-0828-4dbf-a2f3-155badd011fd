<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->uuid('id');
            $table->foreignUuid('user_id');
            $table->foreignId('application_id');
            $table->foreignId('transaction_id');
            $table->decimal('unit_price', 8, 2)->default(0);
            $table->decimal('charges', 8, 2)->default(0);
            $table->tinyInteger('qty')->default(1);
            $table->decimal('amount', 8, 2)->virtualAs('qty * unit_price');
            $table->boolean('status')->default(0);
            $table->json('meta')->nullable();
            $table->text('description')->nullable();
            $table->text('notes')->nullable();
            $table->string('reference')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
