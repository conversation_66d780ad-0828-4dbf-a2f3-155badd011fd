<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('results', function (Blueprint $table) {
            $table->id();
            $table->string('student_id');
            $table->foreignId('application_id');
            $table->foreignId('level_id');
            $table->foreignId('course_id');
            $table->foreignUuid('marker_id')->nullable();
            $table->decimal('marker_score', 3, 1)->nullable();
            $table->foreignUuid('moderator_id')->nullable();
            $table->decimal('moderator_score', 3, 1)->nullable();
            $table->tinyInteger('iec_score')->nullable();
            $table->tinyInteger('remark_score')->nullable();
            $table->tinyInteger('total_score')->storedAs('GREATEST(COALESCE(iec_score, 0), COALESCE(remark_score, 0))');
            $table->foreignUuid('remarker_id')->nullable();
            $table->boolean('isPublished')->default(0);
            $table->boolean('wrote_exam')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('results');
    }
};
