<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('entrance_results', function (Blueprint $table) {
            $table->id();
            $table->string('student_id');
            $table->foreignId('application_id');
            $table->foreignId('level_id');
            $table->foreignId('course_id');
            $table->foreignUuid('marker_id')->nullable();
            $table->foreignUuid('submission_id');
            $table->tinyInteger('marker_que_one')->nullable();
            $table->tinyInteger('marker_que_two')->nullable();
            $table->tinyInteger('marker_score')->storedAs('marker_que_one + marker_que_two')->nullable();
            $table->foreignUuid('moderator_id')->nullable();
            $table->tinyInteger('moderator_que_one')->nullable();
            $table->tinyInteger('moderator_que_two')->nullable();
            $table->tinyInteger('moderator_score')->storedAs('moderator_que_one + moderator_que_two')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('entrance_results');
    }
};
