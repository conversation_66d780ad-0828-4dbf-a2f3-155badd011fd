<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('remark_results', function (Blueprint $table) {
            $table->foreignUuid('moderator_id')->nullable();
            $table->renameColumn('score', 'marker_score');
            $table->tinyInteger('moderator_score')->nullable();
            $table->boolean('moderator_score_confirmed')->default(0);
            $table->boolean('reactivate_moderator')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('remark_results', function (Blueprint $table) {
            $table->dropColumn(['moderator_score', 'moderator_score_confirmed', 'reactivate_moderator']);
            $table->renameColumn('marker_score', 'score');
        });
    }
};
