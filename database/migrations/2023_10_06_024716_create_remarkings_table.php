<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('remarkings', function (Blueprint $table) {
            $table->id();
            $table->string('student_id');
            $table->foreignId('course_id');
            $table->foreignId('appication_id');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('remarkings');
    }
};
