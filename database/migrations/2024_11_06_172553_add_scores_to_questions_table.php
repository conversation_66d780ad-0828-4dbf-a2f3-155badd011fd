<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('questions', function (Blueprint $table) {
            $table->tinyInteger('marker_score')->nullable(); // Add marker_score column
            $table->tinyInteger('moderator_score')->nullable(); // Add moderator_score column
            $table->tinyInteger('final_score')->nullable(); // Add moderator_score column
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('questions', function (Blueprint $table) {
            $table->dropColumn(['marker_score', 'moderator_score', 'final_score']); // Remove columns if rolling back
        });
    }
};
