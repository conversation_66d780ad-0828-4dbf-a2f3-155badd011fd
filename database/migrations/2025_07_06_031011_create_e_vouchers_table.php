<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('e_vouchers', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('voucher_code', 20)->unique();
            $table->string('email');
            $table->string('phone');
            $table->string('first_name');
            $table->string('last_name');
            $table->string('full_name')->storedAs('CONCAT(first_name, " ", last_name)');
            $table->decimal('amount', 8, 2)->default(200.00);
            $table->decimal('charges', 8, 2)->default(0);
            $table->enum('status', ['pending', 'paid', 'redeemed', 'expired'])->default('pending');
            $table->unsignedBigInteger('application_id');
            $table->string('payment_reference')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->timestamp('redeemed_at')->nullable();
            $table->uuid('redeemed_by')->nullable(); // Links to cred_id when redeemed
            $table->timestamps();
            
            $table->index(['voucher_code', 'status']);
            $table->index(['email', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('e_vouchers');
    }
};
