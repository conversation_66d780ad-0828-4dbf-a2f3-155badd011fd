<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('creds', function (Blueprint $table) {
            $table->uuid('id');
            $table->string('serial', 12)->nullable();
            $table->string('pin')->nullable();
            $table->string('phone')->default('');
            $table->string('email')->default('');
            $table->string('first_name')->default('');
            $table->string('last_name')->default('');
            $table->string('full_name')->storedAs('CONCAT(first_name, " ", last_name)');
            $table->string('student_id')->nullable();
            $table->boolean('status')->default(0);
            $table->foreignId('application_id')->constrained('applications');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('creds');
    }
};
