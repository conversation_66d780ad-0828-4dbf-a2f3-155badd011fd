<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('retally_results', function (Blueprint $table) {
            // Change score field from tinyInteger to decimal to support decimal values
            $table->decimal('score', 5, 2)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('retally_results', function (Blueprint $table) {
            // Revert back to tinyInteger (this will truncate decimal values)
            $table->tinyInteger('score')->nullable()->change();
        });
    }
};
