<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Make voucher_amount nullable
        Schema::table('applications', function (Blueprint $table) {
            $table->decimal('voucher_amount', 8, 2)->nullable()->change();
        });

        // Set voucher_amount to NULL for non-entrance applications (transaction_id != 1)
        DB::table('applications')
            ->where('transaction_id', '!=', 1)
            ->update(['voucher_amount' => null]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore default value for all applications
        DB::table('applications')
            ->whereNull('voucher_amount')
            ->update(['voucher_amount' => 200.00]);

        // Make voucher_amount not nullable again
        Schema::table('applications', function (Blueprint $table) {
            $table->decimal('voucher_amount', 8, 2)->default(200.00)->change();
        });
    }
};
