<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('submissions');

        Schema::create('submissions', function (Blueprint $table) {
            $table->engine = 'MyISAM';
            $table->uuid('id')->primary();
            $table->foreignId('application_id')->constrained('applications');
            $table->unsignedBigInteger('serial');
            $table->unsignedBigInteger('serial_auto')->unique();
            $table->year('year')->nullable();
            $table->string('student_id')->nullable();
            $table->foreignUuid('cred_id')->nullable();
            $table->string('family_name')->default('');
            $table->string('first_name')->default('');
            $table->string('middle_name')->default('')->nullable();
            $table->string('full_name')->storedAs('CONCAT(family_name, ", ", first_name, " ", COALESCE(middle_name, ""))');
            $table->string('title')->default('');
            $table->string('gender')->default('');
            $table->date('dob')->nullable();
            $table->string('nationality')->default('');
            $table->string('email')->default('');
            $table->string('ghana_card')->default('');
            $table->string('phone')->default('');
            $table->string('institution')->default('');
            $table->boolean('status')->nullable()->default(1);
            $table->boolean('notified')->nullable();
            $table->boolean('wrote_exam')->default(0);
            $table->boolean('disability')->default(0);
            $table->string('pwd_type')->default('');
            $table->text('pwd_details')->nullable();
            $table->foreignId('venue_id')->nullable();
            $table->foreignUuid('marker_id')->nullable();
            $table->foreignUuid('sec_a_marker_id')->nullable();
            $table->foreignUuid('sec_b_marker_id')->nullable();
            $table->tinyInteger('sec_a_marker_que_one')->nullable();
            $table->tinyInteger('sec_a_marker_que_two')->nullable();
            $table->tinyInteger('sec_b_marker_que_one')->nullable();
            $table->tinyInteger('sec_b_marker_que_two')->nullable();
            $table->foreignUuid('sec_a_moderator_id')->nullable();
            $table->foreignUuid('sec_b_moderator_id')->nullable();
            $table->tinyInteger('sec_a_moderator_que_one')->nullable();
            $table->tinyInteger('sec_a_moderator_que_two')->nullable();
            $table->tinyInteger('sec_b_moderator_que_one')->nullable();
            $table->tinyInteger('sec_b_moderator_que_two')->nullable();
            $table->tinyInteger('marker_que_one')->nullable();
            $table->tinyInteger('marker_que_two')->nullable();
            $table->tinyInteger('marker_score')->storedAs('marker_que_one + marker_que_two')->nullable();
            $table->foreignUuid('moderator_id')->nullable();
            $table->tinyInteger('moderator_que_one')->storedAs('marker_que_one')->nullable();
            $table->tinyInteger('moderator_que_two')->storedAs('marker_que_two')->nullable();
            $table->tinyInteger('moderator_score')->storedAs('moderator_que_one + moderator_que_two')->nullable();
            $table->tinyInteger('iec_score')->nullable();
            $table->boolean('isPublished')->default(0);
            $table->boolean('isSubmitted')->default(0);
            $table->text('feedback')->nullable();
            $table->timestamps();

            $table->unique(['application_id', 'serial']);
        });

        DB::statement("ALTER TABLE submissions MODIFY serial_auto BIGINT UNSIGNED NOT NULL AUTO_INCREMENT");

        // Create a trigger to generate student_id
        DB::unprepared('
            CREATE TRIGGER generate_student_id BEFORE INSERT ON submissions
            FOR EACH ROW
            BEGIN
                SET NEW.student_id = CONCAT("EE/", RIGHT(NEW.year, 2), "/", LPAD(NEW.serial_auto, 4, "0"));
            END
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::unprepared('DROP TRIGGER IF EXISTS generate_student_id');
        Schema::dropIfExists('submissions');
    }
};
