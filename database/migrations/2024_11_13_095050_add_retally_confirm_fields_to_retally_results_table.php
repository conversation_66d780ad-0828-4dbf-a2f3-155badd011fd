<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('retally_results', function (Blueprint $table) {
            $table->boolean('retally_score_confirmed')->default(0);
            $table->boolean('final_score_confirmed')->default(0);
            $table->boolean('reactivate_retally')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('retally_results', function (Blueprint $table) {
            $table->dropColumn('retally_score_confirmed');
            $table->dropColumn('final_score_confirmed');
            $table->dropColumn('reactivate_retally');
        });
    }
};
