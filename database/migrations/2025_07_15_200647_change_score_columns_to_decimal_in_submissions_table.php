<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('submissions', function (Blueprint $table) {
            // Change all score fields from tinyInteger to decimal to support decimal values
            $table->decimal('moderator_que_one', 5, 2)->nullable()->change();
            $table->decimal('moderator_que_two', 5, 2)->nullable()->change();
            $table->decimal('marker_que_one', 5, 2)->nullable()->change();
            $table->decimal('marker_que_two', 5, 2)->nullable()->change();
            $table->decimal('iec_score', 5, 2)->nullable()->change();
        });
        
        // Note: marker_score, moderator_que_one, moderator_que_two, and moderator_score 
        // are computed columns (storedAs) so they will automatically use decimal 
        // when the underlying columns are changed to decimal
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('submissions', function (Blueprint $table) {
            // Revert back to tinyInteger (this will truncate decimal values)
            $table->tinyInteger('moderator_que_one')->nullable()->change();
            $table->tinyInteger('moderator_que_two')->nullable()->change();
            $table->tinyInteger('marker_que_one')->nullable()->change();
            $table->tinyInteger('marker_que_two')->nullable()->change();
            $table->tinyInteger('iec_score')->nullable()->change();
        });
    }
};
