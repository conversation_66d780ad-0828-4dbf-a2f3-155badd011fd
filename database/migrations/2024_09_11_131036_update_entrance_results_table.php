<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('entrance_results', function (Blueprint $table) {
            // Modify existing columns
            // $table->renameColumn('marker_que_one', 'sec_a_marker_que_one');
            // $table->renameColumn('marker_que_two', 'sec_a_marker_que_two');

            // $table->renameColumn('moderator_que_one', 'sec_a_moderator_que_one');
            // $table->renameColumn('moderator_que_two', 'sec_a_moderator_que_two');

            // Drop existing columns
            // $table->dropColumn('marker_score');
            // $table->dropColumn('marker_que_one');
            // $table->dropColumn('marker_que_two');
            // $table->dropColumn('moderator_que_one');
            // $table->dropColumn('moderator_que_two');
            // Add new columns
            // $table->foreignId('submission_id');
            // $table->foreignUuid('sec_a_marker_id')->nullable();
            // $table->tinyInteger('sec_a_marker_que_one')->nullable();
            // $table->tinyInteger('sec_a_marker_que_two')->nullable();
            // $table->tinyInteger('sec_b_marker_que_one')->nullable();
            // $table->tinyInteger('sec_b_marker_que_two')->nullable();
            // $table->boolean('sec_a_marker_editable')->default(0);
            // $table->boolean('sec_b_marker_editable')->default(0);
            // $table->foreignUuid('sec_a_moderator_id')->nullable();
            // $table->tinyInteger('sec_a_moderator_que_one')->nullable();
            // $table->tinyInteger('sec_a_moderator_que_two')->nullable();
            // $table->tinyInteger('sec_b_moderator_que_one')->nullable();
            // $table->tinyInteger('sec_b_moderator_que_two')->nullable();
            // $table->boolean('sec_a_moderator_editable')->default(0);
            // $table->boolean('sec_b_moderator_editable')->default(0);
            // $table->tinyInteger('sec_a_marker_score')->storedAs('sec_a_marker_que_one + sec_a_marker_que_two')->nullable();
            // $table->tinyInteger('sec_b_marker_score')->storedAs('sec_b_marker_que_one + sec_b_marker_que_two')->nullable();
            // $table->tinyInteger('sec_a_moderator_score')->storedAs('sec_a_moderator_que_one + sec_a_moderator_que_two')->nullable();
            // $table->tinyInteger('sec_b_moderator_score')->storedAs('sec_b_moderator_que_one + sec_b_moderator_que_two')->nullable();

            // Add any other new columns you need
        });
    }

    public function down()
    {
        Schema::table('entrance_results', function (Blueprint $table) {
            // Revert column renames
            // $table->renameColumn('sec_a_marker_que_one', 'marker_que_one');
            // $table->renameColumn('sec_a_marker_que_two', 'marker_que_two');
            // $table->renameColumn('sec_a_moderator_que_one', 'moderator_que_one');
            // $table->renameColumn('sec_a_moderator_que_two', 'moderator_que_two');

            // Drop new columns
            // $table->dropColumn('submission_id');
            // $table->dropColumn('sec_a_marker_id');
            // $table->dropColumn('sec_b_marker_que_one');
            // $table->dropColumn('sec_b_marker_que_two');
            // $table->dropColumn('sec_a_marker_score');
            // $table->dropColumn('sec_b_marker_score');
            // $table->dropColumn('sec_a_moderator_id');
            // $table->dropColumn('sec_a_moderator_score');
            // $table->dropColumn('sec_b_moderator_score');

            // Recreate dropped columns if necessary
            // $table->integer('marker_score')->nullable();
            // $table->integer('moderator_score')->nullable();
        });
    }
};
