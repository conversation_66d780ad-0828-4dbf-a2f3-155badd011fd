<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Schema::table('results', function (Blueprint $table) {
        //     $table->foreignId('sanction_id')->nullable()->constrained('sanctions');
        //     $table->string('sanction_reason')->nullable(); // Duration of the ban in years
        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('results', function (Blueprint $table) {
            $table->dropColumn('sanction_id');
            $table->dropColumn('sanction_reason');
        });
    }
};
