<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('questions', function (Blueprint $table) {
            // Change marker_score from tinyInteger to decimal to support decimal values
            $table->decimal('marker_score', 5, 2)->nullable()->change();

            // Change moderator_score from tinyInteger to decimal to support decimal values
            $table->decimal('moderator_score', 5, 2)->nullable()->change();

            // Change final_score from tinyInteger to decimal to support decimal values
            $table->decimal('final_score', 5, 2)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('questions', function (Blueprint $table) {
            // Revert back to tinyInteger (this will truncate decimal values)
            $table->tinyInteger('marker_score')->nullable()->change();
            $table->tinyInteger('moderator_score')->nullable()->change();
            $table->tinyInteger('final_score')->nullable()->change();
        });
    }
};
