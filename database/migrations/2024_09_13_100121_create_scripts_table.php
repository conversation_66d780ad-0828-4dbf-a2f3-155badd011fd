<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Schema::create('scripts', function (Blueprint $table) {
        //     $table->id();
        //     $table->timestamps();
        //     $table->string('student_id');
        //     $table->string('name');
        //     $table->string('section');
        //     $table->uuid('submission_id');
        //     $table->foreign('submission_id')->references('id')->on('submissions');
        //     $table->unsignedBigInteger('application_id');
        //     $table->unsignedBigInteger('level_id')->nullable();
        //     $table->unsignedBigInteger('course_id')->nullable();
        //     $table->uuid('marker_id')->nullable();
        //     $table->tinyInteger('marker_que_one')->nullable();
        //     $table->tinyInteger('marker_que_two')->nullable();
        //     $table->tinyInteger('marker_score')->storedAs('marker_que_one + marker_que_two')->nullable();
        //     $table->uuid('moderator_id')->nullable();
        //     $table->tinyInteger('moderator_que_one')->nullable();
        //     $table->tinyInteger('moderator_que_two')->nullable();
        //     $table->tinyInteger('moderator_score')->storedAs('moderator_que_one + moderator_que_two')->nullable();
        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('scripts');
    }
};
