<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('scripts', function (Blueprint $table) {
            $table->unsignedBigInteger('sub_course_id')->nullable();
            $table->foreign('sub_course_id')->references('id')->on('sub_courses');

            $table->unsignedBigInteger('remark_result_id')->nullable();
            $table->foreign('remark_result_id')->references('id')->on('remark_results')->nullable();

            $table->unsignedBigInteger('retally_result_id')->nullable();
            $table->foreign('retally_result_id')->references('id')->on('retally_results')->nullable();

            $table->unsignedBigInteger('result_id')->nullable();
            $table->foreign('result_id')->references('id')->on('results')->nullable();
            $table->boolean('isPublished')->nullable();
            $table->boolean('wrote_exam')->nullable();
            $table->string('section')->nullable()->change(); // Make section nullable
            $table->foreignUuid('submission_id')->nullable()->change(); // Make submission_id nullable
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('scripts', function (Blueprint $table) {
            $table->dropForeign(['sub_course_id']);
            $table->dropForeign(['remark_result_id']);
            $table->dropForeign(['retally_result_id']);
            $table->dropForeign(['result_id']);
            $table->dropColumn(['sub_course_id', 'remark_result_id', 'retally_result_id', 'result_id']);
            // $table->string('section')->nullable(false)->change(); // Revert to not nullable
            // $table->uuid('submission_id')->nullable(false)->change(); // Revert to not nullable
        });
    }
};
