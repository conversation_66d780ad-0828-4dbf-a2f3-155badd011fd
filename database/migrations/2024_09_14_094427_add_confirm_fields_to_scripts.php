<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('scripts', function (Blueprint $table) {
            $table->boolean('marker_score_confirmed')->default(0);
            $table->boolean('moderator_score_confirmed')->default(0);
            $table->boolean('final_score_confirmed')->default(0);
            $table->boolean('reactivate_marker')->default(0);
            $table->boolean('reactivate_moderator')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('scripts', function (Blueprint $table) {
            $table->dropColumn('marker_score_confirmed'); // Drop the marker_score_confirmed column
            $table->dropColumn('moderator_score_confirmed'); // Drop the moderator_score_confirmed column
            $table->dropColumn('final_score_confirmed'); // Drop the final_score_confirmed column
            $table->dropColumn('reactivate_marker'); // Drop the reactivate_marker column
            $table->dropColumn('reactivate_moderator'); // Drop the reactivate_marker column
        });
    }
};
