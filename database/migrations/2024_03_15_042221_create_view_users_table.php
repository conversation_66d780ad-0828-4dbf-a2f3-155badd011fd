<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            CREATE OR REPLACE VIEW view_users AS
            SELECT DISTINCT
                orders.`user_id` as id,
                CASE
                    WHEN orders.transaction_id = 1 THEN creds.full_name
                    ELSE users.full_name
                END AS full_name
            FROM orders
            LEFT JOIN creds ON orders.`user_id` = creds.id
            LEFT JOIN users ON orders.`user_id` = users.id
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('view_users');
    }
};
